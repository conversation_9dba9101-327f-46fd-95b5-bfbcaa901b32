L251 LINKER/LOCATER V4.66.93.0                                                          07/26/2025  15:35:33  PAGE 1


L251 LINKER/LOCATER V4.66.93.0, INVOKED BY:
D:\KEIL_V5\C251\BIN\L251.EXE .\Objects\main.obj, .\Objects\isr.obj, .\Objects\config.obj, ..\Driver\stc_usb_cdc_32g.lib,
>>  .\Objects\MPU6050.obj, .\Objects\OLED_SPI.obj, .\Objects\MKS.obj, .\Objects\D2Car.obj, .\Objects\ADC.obj, .\Objects\
>> Delay.obj, .\Objects\GPIO.obj, .\Objects\IIC.obj, .\Objects\INT.obj, .\Objects\PIT.obj, .\Objects\PWM.obj, .\Objects\
>> UART.obj, .\Objects\EEPROM.obj, .\Objects\CAN.obj, .\Objects\DMA.obj, .\Objects\SPI.obj TO .\Objects\mode PRINT (.\Li
>> stings\mode.map) CASE DISABLEWARNING (57) CLASSES (EDATA (0X0-0XFFF), HDATA (0X0-0XFFF))


CPU MODE:     251 SOURCE MODE
INTR FRAME:   2 BYTES SAVED ON INTERRUPT
MEMORY MODEL: XSMALL WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\main.obj (main)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\isr.obj (isr)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\config.obj (config)
         COMMENT TYPE 0: C251 V5.60.0
  ..\Driver\stc_usb_cdc_32g.lib (usb)
         COMMENT TYPE 0: C251 V5.60.0
  ..\Driver\stc_usb_cdc_32g.lib (usb_req_class)
         COMMENT TYPE 0: C251 V5.60.0
  ..\Driver\stc_usb_cdc_32g.lib (usb_req_std)
         COMMENT TYPE 0: C251 V5.60.0
  ..\Driver\stc_usb_cdc_32g.lib (usb_req_vendor)
         COMMENT TYPE 0: C251 V5.60.0
  ..\Driver\stc_usb_cdc_32g.lib (util)
         COMMENT TYPE 0: C251 V5.60.0
  ..\Driver\stc_usb_cdc_32g.lib (usb_desc)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\MPU6050.obj (MPU6050)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\OLED_SPI.obj (OLED_SPI)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\MKS.obj (MKS)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\D2Car.obj (D2Car)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\ADC.obj (ADC)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\Delay.obj (Delay)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\GPIO.obj (GPIO)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\IIC.obj (IIC)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\INT.obj (INT)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\PIT.obj (PIT)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\PWM.obj (PWM)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\UART.obj (UART)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\EEPROM.obj (EEPROM)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\CAN.obj (CAN)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\DMA.obj (DMA)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\SPI.obj (SPI)
         COMMENT TYPE 0: C251 V5.60.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FPADD)
         COMMENT TYPE 0: A251 V4.69.6.0
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 2


  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FPMUL)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FPDIV)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FPCMP)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FPNEG)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FCAST)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?CASTF)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (SPRINTF)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (VSPRINTF)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (FABS)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (LOG10?_)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (SQRT?_)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (ASIN?_)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (floor)
         COMMENT TYPE 0: C251 V5.58.7
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FPGETOPN)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?PRNFMT)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (LOG?_)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (ATAN?_)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FPCONVERT)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FPSERIES)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FTNPWR)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (?C_START)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (?C?INITEDATA)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (?C?SIDIV)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (?C?LMUL)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (?C?ULDIV)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (?C?SLDIV)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (ABS)
         COMMENT TYPE 0: C251 V5.58.7
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (LABS)
         COMMENT TYPE 0: C251 V5.58.7
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (STRLEN)
         COMMENT TYPE 0: C251 V5.58.7
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (memcpy)
         COMMENT TYPE 0: C251 V5.58.7
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (?C?INITEDATA_END)
         COMMENT TYPE 0: A251 V4.69.6.0


ACTIVE MEMORY CLASSES OF MODULE:  .\Objects\mode (main)
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 3



BASE        START       END         USED      MEMORY CLASS
==========================================================
000000H     000000H     000FFFH     0003D6H   EDATA
000000H     000000H     000FFFH               HDATA
000000H     FF0000H     FFFFFFH     0002B3H   HCONST
FF0000H     FF0000H     FFFFFFH     008234H   CODE
000020H.0   000020H.0   00002FH.7   000002H.4 BIT
010000H     010000H     01FFFFH     0000C3H   XDATA
FF0000H     FF0000H     FFFFFFH     000004H   CONST
000000H     000000H     00007FH     000008H   DATA


MEMORY MAP OF MODULE:  .\Objects\mode (main)


START     STOP      LENGTH    ALIGN  RELOC    MEMORY CLASS   SEGMENT NAME
=========================================================================
000000H   000007H   000008H   ---    AT..     DATA           "REG BANK 0"
000008H   00001FH   000018H   BYTE   UNIT     EDATA          ?ED?IMUUPDATE?MPU6050
000020H.0 000020H.6 000000H.7 BIT    UNIT     BIT            ?BI?MAIN
000020H.7 000021H.1 000000H.3 BIT    UNIT     BIT            ?BI?USB
000021H.2 000021H.3 000000H.2 BIT    UNIT     BIT            ?BI?CONFIG
000021H.4 000021H.4 000000H.1 BIT    UNIT     BIT            ?BI?CAN_MKS_CONTROL?MKS
000021H.5 000021H.5 000000H.1 BIT    UNIT     BIT            ?BI?ESD_M1_POS_CONTROL?D2CAR
000021H.6 000021H.6 000000H.1 BIT    UNIT     BIT            ?BI?GET_IO?GPIO
000021H.7 000021H.7 000000H.1 BIT    UNIT     BIT            ?BI?OUT_IO?GPIO
000022H.0 000022H.0 000000H.1 BIT    UNIT     BIT            ?BI?CAN
000022H.1 000022H.1 000000H.1 BIT    UNIT     BIT            ?BI?CANINIT?CAN
000022H.2 000022H.2 000000H.1 BIT    UNIT     BIT            ?BI?CANREADMSG?CAN
000022H.3 000022H.3 000000H.1 BIT    UNIT     BIT            ?BI?CANSENDMSG?CAN
000022H.4 000022H   000000H.4 ---    ---      **GAP**
000023H   0000B5H   000093H   BYTE   UNIT     EDATA          ?ED?MPU6050
0000B6H   00010BH   000056H   BYTE   UNIT     EDATA          _EDATA_GROUP_
00010CH   000152H   000047H   BYTE   UNIT     EDATA          ?ED?D2CAR
000153H   00017EH   00002CH   BYTE   UNIT     EDATA          ?ED?MAIN
00017FH   0001AAH   00002CH   BYTE   UNIT     EDATA          ?ED?SEG7_SHOWSTRING?UTIL
0001ABH   0001D6H   00002CH   BYTE   UNIT     EDATA          ?ED?PRINTF_HID?UTIL
0001D7H   0001F3H   00001DH   BYTE   UNIT     EDATA          ?ED?CONFIG
0001F4H   000209H   000016H   BYTE   UNIT     EDATA          ?ED?MKS
00020AH   00021EH   000015H   BYTE   UNIT     EDATA          ?ED?CANREADMSG?CAN
00021FH   000232H   000014H   BYTE   UNIT     EDATA          ?ED?USB
000233H   000242H   000010H   BYTE   UNIT     EDATA          ?ED?UART_SEND_FLOAT?UART
000243H   00024EH   00000CH   BYTE   UNIT     EDATA          ?ED?UART_SEND_INT?UART
00024FH   000258H   00000AH   BYTE   UNIT     EDATA          ?ED?PIT
000259H   000261H   000009H   BYTE   UNIT     EDATA          ?ED?CANSENDMSG?CAN
000262H   000269H   000008H   BYTE   UNIT     EDATA          ?ED?REVERSE4?UTIL
00026AH   000271H   000008H   BYTE   UNIT     EDATA          ?ED?MPU6050_GET_GYRO?MPU6050
000272H   000279H   000008H   BYTE   UNIT     EDATA          ?ED?OLED_SHOWNUM?OLED_SPI
00027AH   000281H   000008H   BYTE   UNIT     EDATA          ?ED?OLED_DRAWBMP?OLED_SPI
000282H   000289H   000008H   BYTE   UNIT     EDATA          ?ED?GPIO_INIT_PIN?GPIO
00028AH   000291H   000008H   BYTE   UNIT     EDATA          ?ED?GPIO_PULL_PIN?GPIO
000292H   000299H   000008H   BYTE   UNIT     EDATA          ?ED?GPIO_ISR_INIT?GPIO
00029AH   0002A0H   000007H   BYTE   UNIT     EDATA          ?ED?USB_REQ_CLASS
0002A1H   0002A6H   000006H   BYTE   UNIT     EDATA          ?ED?MPU6050_GET_ACC?MPU6050
0002A7H   0002ABH   000005H   BYTE   UNIT     EDATA          ?ED?USB_SENDDATA?USB
0002ACH   0002B0H   000005H   BYTE   UNIT     EDATA          ?ED?LCD12864_SETBUFFER?UTIL
0002B1H   0002B5H   000005H   BYTE   UNIT     EDATA          ?ED?OLED12864_SETBUFFER?UTIL
0002B6H   0002BAH   000005H   BYTE   UNIT     EDATA          ?ED?CAN
0002BBH   0002BEH   000004H   BYTE   UNIT     EDATA          ?ED?LIMIT_INT?CONFIG
0002BFH   0002C2H   000004H   BYTE   UNIT     EDATA          ?ED?LIMIT_FLOAT?CONFIG
0002C3H   0002C6H   000004H   BYTE   UNIT     EDATA          ?ED?SEG7_SHOWLONG?UTIL
0002C7H   0002CAH   000004H   BYTE   UNIT     EDATA          ?ED?OLED_SHOWCHINESE?OLED_SPI
0002CBH   0002CEH   000004H   BYTE   UNIT     EDATA          ?ED?ESD_WRITE_IIC?IIC
0002CFH   0002D2H   000004H   BYTE   UNIT     EDATA          ?ED?ESD_READ_IIC?IIC
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 4


0002D3H   0002D6H   000004H   BYTE   UNIT     EDATA          ?ED?UART
0002D7H   0002DAH   000004H   BYTE   UNIT     EDATA          ?ED?VSPRINTF
0002DBH   0002DDH   000003H   BYTE   UNIT     EDATA          ?ED?MPU6050_SIMIIC_READ_REGS?MPU6050
0002DEH   0002DFH   000002H   BYTE   UNIT     EDATA          ?ED?LCD12864_SHOWSTRING?UTIL
0002E0H   0002E0H   000001H   BYTE   UNIT     EDATA          ?ED?UART_SEND_STRING?UART
0002E1H   0003E0H   000100H   BYTE   UNIT     EDATA          ?STACK
0003E1H   00FFFFH   00FC1FH   ---    ---      **GAP**
010000H   0100BFH   0000C0H   BYTE   INSEG    XDATA          ?XD?USB
0100C0H   0100C2H   000003H   BYTE   INSEG    XDATA          ?XD?MAIN
0100C3H   FEFFFFH   FDFF3DH   ---    ---      **GAP**
FF0000H   FF0002H   000003H   ---    OFFS..   CODE           ?CO?start251?4
FF0003H   FF0005H   000003H   ---    OFFS..   CODE           ?PR?IV?0
FF0006H   FF000AH   000005H   BYTE   INSEG    CODE           ?PR?SET_MOTOR_SPEED?MAIN
FF000BH   FF000DH   000003H   ---    OFFS..   CODE           ?PR?IV?1
FF000EH   FF0012H   000005H   BYTE   INSEG    CODE           ?PR?SQ?CONFIG
FF0013H   FF0015H   000003H   ---    OFFS..   CODE           ?PR?IV?2
FF0016H   FF0019H   000004H   BYTE   INSEG    CODE           ?PR?ESD_SPI_DEINIT?SPI
FF001AH   FF001AH   000001H   BYTE   INSEG    CODE           ?PR?INT2_ISR?MAIN
FF001BH   FF001DH   000003H   ---    OFFS..   CODE           ?PR?IV?3
FF001EH   FF0020H   000003H   BYTE   INSEG    CODE           ?PR?HID_ISR?CONFIG
FF0021H   FF0021H   000001H   BYTE   INSEG    CODE           ?PR?IIC_I?ISR
FF0022H   FF0022H   000001H   BYTE   INSEG    CODE           ?PR?CMP_I?ISR
FF0023H   FF0025H   000003H   ---    OFFS..   CODE           ?PR?IV?4
FF0026H   FF0028H   000003H   BYTE   INSEG    CODE           ?PR?USB_SET_DESCRIPTOR?USB_REQ_STD
FF0029H   FF0029H   000001H   BYTE   INSEG    CODE           ?PR?LVD_I?ISR
FF002AH   FF002AH   000001H   BYTE   INSEG    CODE           ?PR?SPI_I?ISR
FF002BH   FF002DH   000003H   ---    OFFS..   CODE           ?PR?IV?5
FF002EH   FF0030H   000003H   BYTE   INSEG    CODE           ?PR?USB_SYNCH_FRAME?USB_REQ_STD
FF0031H   FF0031H   000001H   BYTE   INSEG    CODE           ?PR?DMA_ADC_ISR?ISR
FF0032H   FF0032H   000001H   BYTE   INSEG    CODE           ?PR?DMA_M2M_ISR?ISR
FF0033H   FF0035H   000003H   ---    OFFS..   CODE           ?PR?IV?6
FF0036H   FF0042H   00000DH   BYTE   INSEG    CODE           ?PR?USB_SETUP_STATUS?USB
FF0043H   FF0045H   000003H   ---    OFFS..   CODE           ?PR?IV?8
FF0046H   FF0048H   000003H   BYTE   INSEG    CODE           ?PR?USB_REQ_VENDOR?USB_REQ_VENDOR
FF0049H   FF0049H   000001H   BYTE   INSEG    CODE           ?PR?INT0_I?ISR
FF004AH   FF004AH   000001H   BYTE   INSEG    CODE           ?PR?INT1_I?ISR
FF004BH   FF004DH   000003H   ---    OFFS..   CODE           ?PR?IV?9
FF004EH   FF004EH   000001H   BYTE   INSEG    CODE           ?PR?INT3_I?ISR
FF004FH   FF004FH   000001H   BYTE   INSEG    CODE           ?PR?INT4_I?ISR
FF0050H   FF0050H   000001H   BYTE   INSEG    CODE           ?PR?DMA_SPI_ISR?ISR
FF0051H   FF0051H   000001H   BYTE   INSEG    CODE           ?PR?TIMER1_I?ISR
FF0052H   FF0052H   000001H   BYTE   INSEG    CODE           ?PR?TIMER2_I?ISR
FF0053H   FF0055H   000003H   ---    OFFS..   CODE           ?PR?IV?10
FF0056H   FF0056H   000001H   BYTE   INSEG    CODE           ?PR?PWMA_I?ISR
FF0057H   FF0057H   000001H   BYTE   INSEG    CODE           ?PR?TIMER3_I?ISR
FF0058H   FF0058H   000001H   BYTE   INSEG    CODE           ?PR?PWMB_I?ISR
FF0059H   FF0059H   000001H   BYTE   INSEG    CODE           ?PR?TIMER4_I?ISR
FF005AH   FF005AH   000001H   BYTE   INSEG    CODE           ?PR?ADC_I?ISR
FF005BH   FF005DH   000003H   ---    OFFS..   CODE           ?PR?IV?11
FF005EH   FF005EH   000001H   BYTE   INSEG    CODE           ?PR?USB_SUSPEND?USB
FF005FH   FF005FH   000001H   BYTE   INSEG    CODE           ?PR?USB_RESUME?USB
FF0060H   FF0060H   000001H   BYTE   INSEG    CODE           ?PR?MKS_ALL_RUN?MKS
FF0061H   FF0062H   000002H   ---    ---      **GAP**
FF0063H   FF0065H   000003H   ---    OFFS..   CODE           ?PR?IV?12
FF0066H   FF0082H   00001DH   BYTE   INSEG    CODE           ?PR?DMA_UART1_TXD_ISR?ISR
FF0083H   FF0085H   000003H   ---    OFFS..   CODE           ?PR?IV?16
FF0086H   FF0089H   000004H   BYTE   UNIT     CONST          ?CO?PRINTF
FF008AH   FF008AH   000001H   ---    ---      **GAP**
FF008BH   FF008DH   000003H   ---    OFFS..   CODE           ?PR?IV?17
FF008EH   FF0092H   000005H   ---    ---      **GAP**
FF0093H   FF0095H   000003H   ---    OFFS..   CODE           ?PR?IV?18
FF0096H   FF009AH   000005H   ---    ---      **GAP**
FF009BH   FF009DH   000003H   ---    OFFS..   CODE           ?PR?IV?19
FF009EH   FF00A2H   000005H   ---    ---      **GAP**
FF00A3H   FF00A5H   000003H   ---    OFFS..   CODE           ?PR?IV?20
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 5


FF00A6H   FF00AAH   000005H   ---    ---      **GAP**
FF00ABH   FF00ADH   000003H   ---    OFFS..   CODE           ?PR?IV?21
FF00AEH   FF00C2H   000015H   BYTE   INSEG    CODE           ?PR?USB_BULK_INTR_IN?USB
FF00C3H   FF00C5H   000003H   ---    OFFS..   CODE           ?PR?IV?24
FF00C6H   FF00CAH   000005H   ---    ---      **GAP**
FF00CBH   FF00CDH   000003H   ---    OFFS..   CODE           ?PR?IV?25
FF00CEH   FF00D2H   000005H   ---    ---      **GAP**
FF00D3H   FF00D5H   000003H   ---    OFFS..   CODE           ?PR?IV?26
FF00D6H   FF00DAH   000005H   ---    ---      **GAP**
FF00DBH   FF00DDH   000003H   ---    OFFS..   CODE           ?PR?IV?27
FF00DEH   FF00E2H   000005H   ---    ---      **GAP**
FF00E3H   FF00E5H   000003H   ---    OFFS..   CODE           ?PR?IV?28
FF00E6H   FF00EAH   000005H   ---    ---      **GAP**
FF00EBH   FF00EDH   000003H   ---    OFFS..   CODE           ?PR?IV?29
FF00EEH   FF012AH   00003DH   BYTE   INSEG    CODE           ?PR?SEG7_SHOWLONG?UTIL
FF012BH   FF012DH   000003H   ---    OFFS..   CODE           ?PR?IV?37
FF012EH   FF0132H   000005H   ---    ---      **GAP**
FF0133H   FF0135H   000003H   ---    OFFS..   CODE           ?PR?IV?38
FF0136H   FF013AH   000005H   ---    ---      **GAP**
FF013BH   FF013DH   000003H   ---    OFFS..   CODE           ?PR?IV?39
FF013EH   FF0142H   000005H   ---    ---      **GAP**
FF0143H   FF0145H   000003H   ---    OFFS..   CODE           ?PR?IV?40
FF0146H   FF014AH   000005H   ---    ---      **GAP**
FF014BH   FF014DH   000003H   ---    OFFS..   CODE           ?PR?IV?41
FF014EH   FF0152H   000005H   ---    ---      **GAP**
FF0153H   FF0155H   000003H   ---    OFFS..   CODE           ?PR?IV?42
FF0156H   FF015AH   000005H   ---    ---      **GAP**
FF015BH   FF015DH   000003H   ---    OFFS..   CODE           ?PR?IV?43
FF015EH   FF0162H   000005H   ---    ---      **GAP**
FF0163H   FF0165H   000003H   ---    OFFS..   CODE           ?PR?IV?44
FF0166H   FF017AH   000015H   BYTE   INSEG    CODE           ?PR?USB_READ_REG?USB
FF017BH   FF017DH   000003H   ---    OFFS..   CODE           ?PR?IV?47
FF017EH   FF0182H   000005H   ---    ---      **GAP**
FF0183H   FF0185H   000003H   ---    OFFS..   CODE           ?PR?IV?48
FF0186H   FF018AH   000005H   ---    ---      **GAP**
FF018BH   FF018DH   000003H   ---    OFFS..   CODE           ?PR?IV?49
FF018EH   FF0192H   000005H   ---    ---      **GAP**
FF0193H   FF0195H   000003H   ---    OFFS..   CODE           ?PR?IV?50
FF0196H   FF019AH   000005H   ---    ---      **GAP**
FF019BH   FF019DH   000003H   ---    OFFS..   CODE           ?PR?IV?51
FF019EH   FF01A2H   000005H   ---    ---      **GAP**
FF01A3H   FF01A5H   000003H   ---    OFFS..   CODE           ?PR?IV?52
FF01A6H   FF01AAH   000005H   ---    ---      **GAP**
FF01ABH   FF01ADH   000003H   ---    OFFS..   CODE           ?PR?IV?53
FF01AEH   FF01B2H   000005H   ---    ---      **GAP**
FF01B3H   FF01B5H   000003H   ---    OFFS..   CODE           ?PR?IV?54
FF01B6H   FF01BAH   000005H   ---    ---      **GAP**
FF01BBH   FF01BDH   000003H   ---    OFFS..   CODE           ?PR?IV?55
FF01BEH   FF01C2H   000005H   ---    ---      **GAP**
FF01C3H   FF01C5H   000003H   ---    OFFS..   CODE           ?PR?IV?56
FF01C6H   FF01CAH   000005H   ---    ---      **GAP**
FF01CBH   FF01CDH   000003H   ---    OFFS..   CODE           ?PR?IV?57
FF01CEH   FF0E5AH   000C8DH   BYTE   UNIT     CODE           ?C?LIB_CODE
FF0E5BH   FF1792H   000938H   BYTE   UNIT     CODE           ?CO?OLED_SPI
FF1793H   FF1FAFH   00081DH   BYTE   INSEG    CODE           ?PR?UART_SEND_FLOAT?UART
FF1FB0H   FF2738H   000789H   BYTE   INSEG    CODE           ?PR?UART_SEND_INT?UART
FF2739H   FF2D0BH   0005D3H   BYTE   INSEG    CODE           ?PR?GPIO_ISR_INIT?GPIO
FF2D0CH   FF30BDH   0003B2H   BYTE   INSEG    CODE           ?PR?IMUUPDATE?MPU6050
FF30BEH   FF3409H   00034CH   BYTE   INSEG    CODE           ?PR?PWM_INIT?PWM
FF340AH   FF3717H   00030EH   BYTE   INSEG    CODE           ?PR?GPIO_INIT_PIN?GPIO
FF3718H   FF399EH   000287H   BYTE   INSEG    CODE           ?PR?GET_IO?GPIO
FF399FH   FF3BC8H   00022AH   BYTE   INSEG    CODE           ?PR?PWM_CHANGE?PWM
FF3BC9H   FF3DE9H   000221H   BYTE   INSEG    CODE           ?PR?OUT_IO?GPIO
FF3DEAH   FF3FF7H   00020EH   BYTE   INSEG    CODE           ?PR?CANINIT?CAN
FF3FF8H   FF41A4H   0001ADH   BYTE   INSEG    CODE           ?PR?KALMAN_FILTER?MPU6050
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 6


FF41A5H   FF434BH   0001A7H   BYTE   INSEG    CODE           ?PR?GPIO_PULL_PIN?GPIO
FF434CH   FF44C9H   00017EH   BYTE   INSEG    CODE           ?PR?GPIO_INIT_8PIN?GPIO
FF44CAH   FF4635H   00016CH   BYTE   INSEG    CODE           ?PR?UART_INIT?UART
FF4636H   FF479DH   000168H   BYTE   INSEG    CODE           ?PR?CANSENDMSG?CAN
FF479EH   FF48E5H   000148H   BYTE   INSEG    CODE           ?PR?DMA_RXD_INIT?DMA
FF48E6H   FF4A25H   000140H   BYTE   INSEG    CODE           ?PR?MAIN?MAIN
FF4A26H   FF4B65H   000140H   BYTE   INSEG    CODE           ?PR?DMA_TXD_INIT?DMA
FF4B66H   FF4C82H   00011DH   BYTE   INSEG    CODE           ?PR?SET_CLK?CONFIG
FF4C83H   FF4D95H   000113H   BYTE   INSEG    CODE           ?PR?CAR_ADD_ANGLE?D2CAR
FF4D96H   FF4EA2H   00010DH   BYTE   INSEG    CODE           ?PR?GPIO_ISR_DEINIT?GPIO
FF4EA3H   FF4FA8H   000106H   BYTE   INSEG    CODE           ?PR?DIS_OLED?MAIN
FF4FA9H   FF50ACH   000104H   BYTE   INSEG    CODE           ?PR?OLED_SHOWFLOAT?OLED_SPI
FF50ADH   FF51AFH   000103H   BYTE   INSEG    CODE           ?PR?USB_GET_STATUS?USB_REQ_STD
FF51B0H   FF52B1H   000102H   BYTE   INSEG    CODE           ?PR?GRIPPER_CONTROL?MAIN
FF52B2H   FF5399H   0000E8H   BYTE   INSEG    CODE           ?PR?OLED_SHOWNUM?OLED_SPI
FF539AH   FF547DH   0000E4H   BYTE   INSEG    CODE           ?PR?CAN_MKS_CONTROL?MKS
FF547EH   FF5555H   0000D8H   BYTE   INSEG    CODE           ?PR?MKS_PID_SET?MKS
FF5556H   FF562CH   0000D7H   BYTE   INSEG    CODE           ?PR?OLED_INIT?OLED_SPI
FF562DH   FF56F5H   0000C9H   BYTE   INSEG    CODE           ?PR?MKS_HOME?MKS
FF56F6H   FF57B9H   0000C4H   BYTE   INSEG    CODE           ?PR?USB_OUT_EP1?USB
FF57BAH   FF587CH   0000C3H   BYTE   INSEG    CODE           ?PR?OLED_SHOWCHAR?OLED_SPI
FF587DH   FF593DH   0000C1H   BYTE   INSEG    CODE           ?PR?CAR_PID_CONTROL?D2CAR
FF593EH   FF59F6H   0000B9H   BYTE   INSEG    CODE           ?PR?CANREADMSG?CAN
FF59F7H   FF5A9BH   0000A5H   BYTE   INSEG    CODE           ?PR?USB_SET_CONFIGURATION?USB_REQ_STD
FF5A9CH   FF5B3BH   0000A0H   BYTE   INSEG    CODE           ?PR?ESD_INIT_IIC?IIC
FF5B3CH   FF5BDAH   00009FH   BYTE   INSEG    CODE           ?PR?USB_CLEAR_FEATURE?USB_REQ_STD
FF5BDBH   FF5C79H   00009FH   BYTE   INSEG    CODE           ?PR?USB_GET_DESCRIPTOR?USB_REQ_STD
FF5C7AH   FF5D12H   000099H   BYTE   INSEG    CODE           ?PR?USB_SET_FEATURE?USB_REQ_STD
FF5D13H   FF5DAAH   000098H   BYTE   INSEG    CODE           ?PR?OLED12864_SETBUFFER?UTIL
FF5DABH   FF5E42H   000098H   BYTE   INSEG    CODE           ?PR?LCD12864_SETBUFFER?UTIL
FF5E43H   FF5EDAH   000098H   BYTE   INSEG    CODE           ?PR?OLED_SHOWINT?OLED_SPI
FF5EDBH   FF5F71H   000097H   BYTE   INSEG    CODE           ?PR?DMA_RXD2_ISR?MAIN
FF5F72H   FF6008H   000097H   BYTE   INSEG    CODE           ?PR?OLED_DRAWBMP?OLED_SPI
FF6009H   FF609FH   000097H   BYTE   INSEG    CODE           ?PR?CAN_MKS_SPD_CONTROL?MKS
FF60A0H   FF6135H   000096H   BYTE   INSEG    CODE           ?PR?CANREADFIFO?CAN
FF6136H   FF61C8H   000093H   BYTE   INSEG    CODE           ?PR?PIT_INIT_US?PIT
FF61C9H   FF6254H   00008CH   BYTE   INSEG    CODE           ?PR?PIT_INIT_MS?PIT
FF6255H   FF62DEH   00008AH   BYTE   INSEG    CODE           ?PR?USB_SETUP?USB
FF62DFH   FF6362H   000084H   BYTE   INSEG    CODE           ?PR?USB_ISR?USB
FF6363H   FF63E5H   000083H   BYTE   UNIT     CODE           ?CO?USB_DESC
FF63E6H   FF6467H   000082H   BYTE   INSEG    CODE           ?PR?ESD_READ_IIC?IIC
FF6468H   FF64E3H   00007CH   BYTE   INSEG    CODE           ?PR?OLED_SHOWCHINESE?OLED_SPI
FF64E4H   FF655BH   000078H   BYTE   INSEG    CODE           ?PR?TM0_ISR?MAIN
FF655CH   FF65CEH   000073H   BYTE   INSEG    CODE           ?PR?OLED_SHOWSTRING?OLED_SPI
FF65CFH   FF663EH   000070H   BYTE   INSEG    CODE           ?PR?SYSTEM_INIT?CONFIG
FF663FH   FF66ADH   00006FH   BYTE   INSEG    CODE           ?PR?USB_SENDDATA?USB
FF66AEH   FF671CH   00006FH   BYTE   INSEG    CODE           ?PR?ESD_M1_POS_CONTROL?D2CAR
FF671DH   FF6789H   00006DH   BYTE   INSEG    CODE           ?PR?ADC_INIT?ADC
FF678AH   FF67F5H   00006CH   BYTE   INSEG    CODE           ?PR?MPU6050_IIC_INIT?MPU6050
FF67F6H   FF6860H   00006BH   BYTE   INSEG    CODE           ?PR?CAN1_I?ISR
FF6861H   FF68CBH   00006BH   BYTE   INSEG    CODE           ?PR?CAN2_I?ISR
FF68CCH   FF6936H   00006BH   BYTE   INSEG    CODE           ?PR?FLOOR?_?FLOOR
FF6937H   FF699DH   000067H   BYTE   INSEG    CODE           ?PR?USB_CTRL_IN?USB
FF699EH   FF6A03H   000066H   BYTE   INSEG    CODE           ?PR?GPIO_INIT_ALLPIN?GPIO
FF6A04H   FF6A64H   000061H   BYTE   INSEG    CODE           ?PR?REVERSE4?UTIL
FF6A65H   FF6AC2H   00005EH   BYTE   INSEG    CODE           ?PR?USB_INIT?USB
FF6AC3H   FF6B1EH   00005CH   BYTE   INSEG    CODE           ?PR?USB_REQ_STD?USB_REQ_STD
FF6B1FH   FF6B76H   000058H   BYTE   INSEG    CODE           ?PR?ESD_WRITE_IIC?IIC
FF6B77H   FF6BCBH   000055H   BYTE   INSEG    CODE           ?PR?MPU6050_SIMIIC_READ_REGS?MPU6050
FF6BCCH   FF6C1FH   000054H   BYTE   INSEG    CODE           ?PR?USB_GET_CONFIGURATION?USB_REQ_STD
FF6C20H   FF6C73H   000054H   BYTE   INSEG    CODE           ?PR?MPU6050_INIT?MPU6050
FF6C74H   FF6CC6H   000053H   BYTE   INSEG    CODE           ?PR?PIT_COUNT_CLEAN?PIT
FF6CC7H   FF6D18H   000052H   BYTE   INSEG    CODE           ?PR?IR_OBJECT_DETECTION?MAIN
FF6D19H   FF6D69H   000051H   BYTE   INSEG    CODE           ?PR?UART_SEND_BYTE?UART
FF6D6AH   FF6D7CH   000013H   BYTE   UNIT     CODE           ?C_C51STARTUP
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 7


FF6D7DH   FF6D8EH   000012H   BYTE   UNIT     CODE           ?C_C51STARTUP?1
FF6D8FH   FF6DB7H   000029H   BYTE   UNIT     CODE           ?C_C51STARTUP?2
FF6DB8H   FF6DBAH   000003H   BYTE   UNIT     CODE           ?C_C51STARTUP?3
FF6DBBH   FF6E0AH   000050H   BYTE   INSEG    CODE           ?PR?MPU6050_GET_GYRO?MPU6050
FF6E0BH   FF6E59H   00004FH   BYTE   INSEG    CODE           ?PR?LCD12864_SHOWPICTURE?UTIL
FF6E5AH   FF6EA6H   00004DH   BYTE   INSEG    CODE           ?PR?USB_CTRL_OUT?USB
FF6EA7H   FF6EF3H   00004DH   BYTE   INSEG    CODE           ?PR?PIT_COUNT_GET?PIT
FF6EF4H   FF6F3FH   00004CH   BYTE   INSEG    CODE           ?PR?PIT_INIT_ENCODER?PIT
FF6F40H   FF6F89H   00004AH   BYTE   INSEG    CODE           ?PR?USB_SET_ADDRESS?USB_REQ_STD
FF6F8AH   FF6FD2H   000049H   BYTE   INSEG    CODE           ?PR?SEG7_SHOWSTRING?UTIL
FF6FD3H   FF701AH   000048H   BYTE   INSEG    CODE           ?PR?USB_RESET?USB
FF701BH   FF7061H   000047H   BYTE   INSEG    CODE           ?PR?LED40_SENDDATA?UTIL
FF7062H   FF70A8H   000047H   BYTE   INSEG    CODE           ?PR?LED64_SENDDATA?UTIL
FF70A9H   FF70ECH   000044H   BYTE   INSEG    CODE           ?PR?ADC_GET?ADC
FF70EDH   FF712FH   000043H   BYTE   INSEG    CODE           ?PR?EC11_APP?MAIN
FF7130H   FF7171H   000042H   BYTE   INSEG    CODE           ?PR?DMA_UART1_RXD_ISR?ISR
FF7172H   FF71B3H   000042H   BYTE   INSEG    CODE           ?PR?DMA_UART2_RXD_ISR?ISR
FF71B4H   FF71F5H   000042H   BYTE   INSEG    CODE           ?PR?DMA_UART3_RXD_ISR?ISR
FF71F6H   FF7237H   000042H   BYTE   INSEG    CODE           ?PR?DMA_UART4_RXD_ISR?ISR
FF7238H   FF7278H   000041H   BYTE   INSEG    CODE           ?PR?YIJIELVBO?MPU6050
FF7279H   FF72B8H   000040H   BYTE   INSEG    CODE           ?PR?OLED12864_SHOWPICTURE?UTIL
FF72B9H   FF72F8H   000040H   BYTE   INSEG    CODE           ?PR?MPU6050_GET_ACC?MPU6050
FF72F9H   FF7337H   00003FH   BYTE   INSEG    CODE           ?PR?KEY_RST?CONFIG
FF7338H   FF7375H   00003EH   BYTE   INSEG    CODE           ?PR?USB_GET_INTERFACE?USB_REQ_STD
FF7376H   FF73B2H   00003DH   BYTE   INSEG    CODE           ?PR?LCD12864_SHOWSTRING?UTIL
FF73B3H   FF73EDH   00003BH   BYTE   INSEG    CODE           ?PR?MPU6050_READ_CH?MPU6050
FF73EEH   FF7427H   00003AH   BYTE   INSEG    CODE           ?PR?USB_READ_FIFO?_?USB
FF7428H   FF7461H   00003AH   BYTE   INSEG    CODE           ?PR?OLED12864_SCROLLRIGHT?UTIL
FF7462H   FF749BH   00003AH   BYTE   INSEG    CODE           ?PR?OLED12864_SCROLLUP?UTIL
FF749CH   FF74D4H   000039H   BYTE   INSEG    CODE           ?PR?OLED12864_SCROLLLEFT?UTIL
FF74D5H   FF750AH   000036H   BYTE   INSEG    CODE           ?PR?TIMER0_I?ISR
FF750BH   FF7540H   000036H   BYTE   INSEG    CODE           ?PR?OLED_CLEAR?OLED_SPI
FF7541H   FF7575H   000035H   BYTE   INSEG    CODE           ?PR?SEG7_SHOWCODE?UTIL
FF7576H   FF75AAH   000035H   BYTE   INSEG    CODE           ?PR?REVERSE2?UTIL
FF75ABH   FF75DDH   000033H   BYTE   INSEG    CODE           ?PR?SEG7_SHOWFLOAT?UTIL
FF75DEH   FF7610H   000033H   BYTE   INSEG    CODE           ?PR?ESD_M1_SPD_CONTROL?D2CAR
FF7611H   FF7642H   000032H   BYTE   INSEG    CODE           ?PR?INT_INIT?INT
FF7643H   FF7673H   000031H   BYTE   INSEG    CODE           ?PR?ESD_M1_PWM_CONTROL?D2CAR
FF7674H   FF76A3H   000030H   BYTE   INSEG    CODE           ?PR?INT2_I?ISR
FF76A4H   FF76D2H   00002FH   BYTE   INSEG    CODE           ?PR?USB_IN?USB
FF76D3H   FF7700H   00002EH   BYTE   INSEG    CODE           ?PR?MPU6050_SEND_CH?MPU6050
FF7701H   FF772DH   00002DH   BYTE   INSEG    CODE           ?PR?USB_IN_EP1?USB
FF772EH   FF775AH   00002DH   BYTE   INSEG    CODE           ?PR?PRINTF_HID?UTIL
FF775BH   FF7786H   00002CH   BYTE   INSEG    CODE           ?PR?OLED_DISPLAYTURN?OLED_SPI
FF7787H   FF77B2H   00002CH   BYTE   INSEG    CODE           ?PR?EEPROM_READ?EEPROM
FF77B3H   FF77DEH   00002CH   BYTE   INSEG    CODE           ?PR?EEPROM_CHANGE?EEPROM
FF77DFH   FF780AH   00002CH   BYTE   INSEG    CODE           ?PR?ESD_SPI_INIT?SPI
FF780BH   FF7835H   00002BH   BYTE   INSEG    CODE           ?PR?USB_IN_EP2?USB
FF7836H   FF785EH   000029H   BYTE   INSEG    CODE           ?PR?USB_WRITE_FIFO?_?USB
FF785FH   FF7887H   000029H   BYTE   INSEG    CODE           ?PR?MPU6050_SIMIIC_READ_REG?MPU6050
FF7888H   FF78B0H   000029H   BYTE   INSEG    CODE           ?PR?OLED_SET_POS?OLED_SPI
FF78B1H   FF78D8H   000028H   BYTE   INSEG    CODE           ?PR?USB_SET_INTERFACE?USB_REQ_STD
FF78D9H   FF78FFH   000027H   BYTE   INSEG    CODE           ?PR?LCD12864_SCROLLUP?UTIL
FF7900H   FF7925H   000026H   BYTE   INSEG    CODE           ?PR?USB_GET_LINE_CODING?USB_REQ_CLASS
FF7926H   FF794BH   000026H   BYTE   INSEG    CODE           ?PR?USB_SET_LINE_CODING?USB_REQ_CLASS
FF794CH   FF7970H   000025H   BYTE   INSEG    CODE           ?PR?MEMCPY?_?MEMCPY
FF7971H   FF7993H   000023H   BYTE   INSEG    CODE           ?PR?ESD_SPI_READMULTIBYTES?SPI
FF7994H   FF79B5H   000022H   BYTE   INSEG    CODE           ?PR?MPU6050_SIMIIC_WRITE_REG?MPU6050
FF79B6H   FF79D7H   000022H   BYTE   INSEG    CODE           ?PR?EEPROM_DELETE?EEPROM
FF79D8H   FF79F8H   000021H   BYTE   INSEG    CODE           ?PR?LIMIT_FLOAT?CONFIG
FF79F9H   FF7A19H   000021H   BYTE   INSEG    CODE           ?PR?OLED12864_SETADDRESSMODE?UTIL
FF7A1AH   FF7A3AH   000021H   BYTE   INSEG    CODE           ?PR?OLED12864_SETCONTRAST?UTIL
FF7A3BH   FF7A5BH   000021H   BYTE   INSEG    CODE           ?PR?LCD12864_REVERSELINE?UTIL
FF7A5CH   FF7A7BH   000020H   BYTE   INSEG    CODE           ?PR?MOTOR_CONTROL?MAIN
FF7A7CH   FF7A9BH   000020H   BYTE   INSEG    CODE           ?PR?MPU6050_SIMIIC_SENDACK?MPU6050
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 8


FF7A9CH   FF7ABBH   000020H   BYTE   INSEG    CODE           ?PR?ESD_SPI_WRITEMULTIBYTES?SPI
FF7ABCH   FF7AD9H   00001EH   BYTE   INSEG    CODE           ?PR?PIN0_I?ISR
FF7ADAH   FF7AF7H   00001EH   BYTE   INSEG    CODE           ?PR?PIN1_I?ISR
FF7AF8H   FF7B15H   00001EH   BYTE   INSEG    CODE           ?PR?PIN2_I?ISR
FF7B16H   FF7B33H   00001EH   BYTE   INSEG    CODE           ?PR?PIN3_I?ISR
FF7B34H   FF7B51H   00001EH   BYTE   INSEG    CODE           ?PR?PIN4_I?ISR
FF7B52H   FF7B6FH   00001EH   BYTE   INSEG    CODE           ?PR?PIN5_I?ISR
FF7B70H   FF7B8DH   00001EH   BYTE   INSEG    CODE           ?PR?PIN6_I?ISR
FF7B8EH   FF7BABH   00001EH   BYTE   INSEG    CODE           ?PR?PIN7_I?ISR
FF7BACH   FF7BC9H   00001EH   BYTE   INSEG    CODE           ?PR?OLED_COLORTURN?OLED_SPI
FF7BCAH   FF7BE7H   00001EH   BYTE   INSEG    CODE           ?PR?UART_SEND_STRING?UART
FF7BE8H   FF7C04H   00001DH   BYTE   INSEG    CODE           ?PR?DMA_UART2_TXD_ISR?ISR
FF7C05H   FF7C21H   00001DH   BYTE   INSEG    CODE           ?PR?DMA_UART3_TXD_ISR?ISR
FF7C22H   FF7C3EH   00001DH   BYTE   INSEG    CODE           ?PR?DMA_UART4_TXD_ISR?ISR
FF7C3FH   FF7C5BH   00001DH   BYTE   INSEG    CODE           ?PR?SLEEP_US?UTIL
FF7C5CH   FF7C77H   00001CH   BYTE   INSEG    CODE           ?PR?UART2_I?ISR
FF7C78H   FF7C93H   00001CH   BYTE   INSEG    CODE           ?PR?UART3_I?ISR
FF7C94H   FF7CAFH   00001CH   BYTE   INSEG    CODE           ?PR?UART4_I?ISR
FF7CB0H   FF7CCBH   00001CH   BYTE   INSEG    CODE           ?PR?MPU6050_SCCB_WAITACK?MPU6050
FF7CCCH   FF7CE7H   00001CH   BYTE   INSEG    CODE           ?PR?ESD_IIC_RECV_DATA?IIC
FF7CE8H   FF7D03H   00001CH   BYTE   INSEG    CODE           ?PR?ESD_IIC_READ_NACK_BYTE?IIC
FF7D04H   FF7D1FH   00001CH   BYTE   INSEG    CODE           ?PR?ESD_IIC_READ_ACK_BYTE?IIC
FF7D20H   FF7D3AH   00001BH   BYTE   INSEG    CODE           ?PR?BCD2HEX?CONFIG
FF7D3BH   FF7D55H   00001BH   BYTE   INSEG    CODE           ?PR?USB_REQ_CLASS?USB_REQ_CLASS
FF7D56H   FF7D6FH   00001AH   BYTE   INSEG    CODE           ?PR?OLED12864_SCROLLSTART?UTIL
FF7D70H   FF7D89H   00001AH   BYTE   INSEG    CODE           ?PR?OLED12864_DISPLAYON?UTIL
FF7D8AH   FF7DA3H   00001AH   BYTE   INSEG    CODE           ?PR?LCD12864_SCROLLRIGHT?UTIL
FF7DA4H   FF7DBDH   00001AH   BYTE   INSEG    CODE           ?PR?LCD12864_AUTOWRAPON?UTIL
FF7DBEH   FF7DD7H   00001AH   BYTE   INSEG    CODE           ?PR?OLED12864_VERTICALMIRROR?UTIL
FF7DD8H   FF7DF1H   00001AH   BYTE   INSEG    CODE           ?PR?LCD12864_CURSORRETURNHOME?UTIL
FF7DF2H   FF7E0BH   00001AH   BYTE   INSEG    CODE           ?PR?OLED12864_DISPLAYENTIRE?UTIL
FF7E0CH   FF7E25H   00001AH   BYTE   INSEG    CODE           ?PR?LCD12864_DISPLAYON?UTIL
FF7E26H   FF7E3FH   00001AH   BYTE   INSEG    CODE           ?PR?LCD12864_CURSORMOVERIGHT?UTIL
FF7E40H   FF7E59H   00001AH   BYTE   INSEG    CODE           ?PR?LCD12864_CURSORON?UTIL
FF7E5AH   FF7E73H   00001AH   BYTE   INSEG    CODE           ?PR?OLED_WR_BYTE?OLED_SPI
FF7E74H   FF7E8DH   00001AH   BYTE   INSEG    CODE           ?PR?OLED_POW?OLED_SPI
FF7E8EH   FF7EA6H   000019H   BYTE   INSEG    CODE           ?PR?USB_BULK_INTR_OUT?USB
FF7EA7H   FF7EBFH   000019H   BYTE   INSEG    CODE           ?PR?OLED12864_DISPLAYOFF?UTIL
FF7EC0H   FF7ED8H   000019H   BYTE   INSEG    CODE           ?PR?OLED12864_SETHEADER?UTIL
FF7ED9H   FF7EF1H   000019H   BYTE   INSEG    CODE           ?PR?LCD12864_AUTOWRAPOFF?UTIL
FF7EF2H   FF7F0AH   000019H   BYTE   INSEG    CODE           ?PR?OLED12864_SCROLLSTOP?UTIL
FF7F0BH   FF7F23H   000019H   BYTE   INSEG    CODE           ?PR?LCD12864_DISPLAYOFF?UTIL
FF7F24H   FF7F3CH   000019H   BYTE   INSEG    CODE           ?PR?LCD12864_SETHEADER?UTIL
FF7F3DH   FF7F55H   000019H   BYTE   INSEG    CODE           ?PR?LCD12864_SCROLLLEFT?UTIL
FF7F56H   FF7F6EH   000019H   BYTE   INSEG    CODE           ?PR?OLED12864_HORIZONTALMIRROR?UTIL
FF7F6FH   FF7F87H   000019H   BYTE   INSEG    CODE           ?PR?OLED12864_DISPLAYCONTENT?UTIL
FF7F88H   FF7FA0H   000019H   BYTE   INSEG    CODE           ?PR?LCD12864_CURSOROFF?UTIL
FF7FA1H   FF7FB9H   000019H   BYTE   INSEG    CODE           ?PR?LCD12864_CURSORMOVELEFT?UTIL
FF7FBAH   FF7FD2H   000019H   BYTE   INSEG    CODE           ?PR?SEG7_SETHEADER?UTIL
FF7FD3H   FF7FEBH   000019H   BYTE   INSEG    CODE           ?PR?ESD_IIC_SEND_NACK?IIC
FF7FECH   FF8003H   000018H   BYTE   INSEG    CODE           ?PR?UART1_I?ISR
FF8004H   FF801BH   000018H   BYTE   INSEG    CODE           ?PR?USB_OUT_DONE?USB
FF801CH   FF8033H   000018H   BYTE   INSEG    CODE           ?PR?SLEEP_MS?UTIL
FF8034H   FF804BH   000018H   BYTE   INSEG    CODE           ?PR?ESD_IIC_SEND_ACK?IIC
FF804CH   FF8062H   000017H   BYTE   INSEG    CODE           ?PR?LIMIT_INT?CONFIG
FF8063H   FF8079H   000017H   BYTE   INSEG    CODE           ?PR?HEX2BCD?CONFIG
FF807AH   FF8090H   000017H   BYTE   INSEG    CODE           ?PR?DELAY_X_MS?DELAY
FF8091H   FF80A7H   000017H   BYTE   INSEG    CODE           ?PR?DELAY_X_US?DELAY
FF80A8H   FF80BEH   000017H   BYTE   INSEG    CODE           ?PR?ESD_IIC_WRITE_START_BYTE?IIC
FF80BFH   FF80D5H   000017H   BYTE   INSEG    CODE           ?PR?ESD_IIC_WAIT?IIC
FF80D6H   FF80ECH   000017H   BYTE   INSEG    CODE           ?PR?ESD_IIC_WRITE_ONE_BYTE?IIC
FF80EDH   FF8103H   000017H   BYTE   INSEG    CODE           ?PR?ESD_IIC_SEND_DATA?IIC
FF8104H   FF8119H   000016H   BYTE   INSEG    CODE           ?PR?USB_SET_CTRL_LINE_STATE?USB_REQ_CLASS
FF811AH   FF812EH   000015H   BYTE   INSEG    CODE           ?PR?OLED_DISPLAY_ON?OLED_SPI
FF812FH   FF8143H   000015H   BYTE   INSEG    CODE           ?PR?OLED_DISPLAY_OFF?OLED_SPI
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 9


FF8144H   FF8158H   000015H   BYTE   INSEG    CODE           ?PR?CANWRITEREG?CAN
FF8159H   FF816CH   000014H   BYTE   INSEG    CODE           ?PR?LCD12864_DISPLAYCLEAR?UTIL
FF816DH   FF8180H   000014H   BYTE   INSEG    CODE           ?PR?OLED12864_DISPLAYREVERSE?UTIL
FF8181H   FF8193H   000013H   BYTE   INSEG    CODE           ?PR?USB_WRITE_REG?USB
FF8194H   FF81A6H   000013H   BYTE   INSEG    CODE           ?PR?EEPROM_OFF?EEPROM
FF81A7H   FF81B9H   000013H   BYTE   INSEG    CODE           ?PR?CANREADREG?CAN
FF81BAH   FF81CAH   000011H   BYTE   INSEG    CODE           ?PR?USB_SETUP_IN?USB
FF81CBH   FF81DBH   000011H   BYTE   INSEG    CODE           ?PR?MPU6050_SIMIIC_STOP?MPU6050
FF81DCH   FF81EBH   000010H   BYTE   INSEG    CODE           ?PR?ESD_IIC_START?IIC
FF81ECH   FF81FBH   000010H   BYTE   INSEG    CODE           ?PR?ESD_IIC_RECV_ACK?IIC
FF81FCH   FF820BH   000010H   BYTE   INSEG    CODE           ?PR?ESD_IIC_STOP?IIC
FF820CH   FF821BH   000010H   BYTE   INSEG    CODE           ?PR?ABS?_?ABS
FF821CH   FF822BH   000010H   BYTE   INSEG    CODE           ?PR?LABS?_?LABS
FF822CH   FF823AH   00000FH   BYTE   INSEG    CODE           ?PR?FMAX?CONFIG
FF823BH   FF8249H   00000FH   BYTE   INSEG    CODE           ?PR?MPU6050_SIMIIC_START?MPU6050
FF824AH   FF8257H   00000EH   BYTE   INSEG    CODE           ?PR?USB_SETUP_OUT?USB
FF8258H   FF8265H   00000EH   BYTE   INSEG    CODE           ?PR?USB_SETUP_STALL?USB
FF8266H   FF8273H   00000EH   BYTE   INSEG    CODE           ?PR?ESD_SPI_READBYTE?SPI
FF8274H   FF8281H   00000EH   BYTE   INSEG    CODE           ?PR?ESD_SPI_RW?SPI
FF8282H   FF828FH   00000EH   BYTE   INSEG    CODE           ?PR?STRLEN?_?STRLEN
FF8290H   FF829BH   00000CH   BYTE   INSEG    CODE           ?PR?INT1_ISR?MAIN
FF829CH   FF82A7H   00000CH   BYTE   INSEG    CODE           ?PR?ESD_SPI_WRITEBYTE?SPI
FF82A8H   FF82B2H   00000BH   BYTE   INSEG    CODE           ?PR?MPU6050_SIMIIC_DELAY?MPU6050
FF82B3H   FF82BCH   00000AH   BYTE   INSEG    CODE           ?PR?USB_RST?CONFIG
FF82BDH   FF84E0H   000224H   BYTE   UNIT     HCONST         ?C_INITEDATA
FF84E1H   FF8538H   000058H   BYTE   UNIT     HCONST         ?HC?PWM
FF8539H   FF8550H   000018H   BYTE   UNIT     HCONST         ?HC?GPIO
FF8551H   FF855FH   00000FH   BYTE   UNIT     HCONST         ?HC?MAIN
FF8560H   FF8568H   000009H   BYTE   UNIT     HCONST         ?HC?CONFIG
FF8569H   FF856FH   000007H   BYTE   UNIT     HCONST         ?HC?OLED_SPI



OVERLAY MAP OF MODULE:   .\Objects\mode (main)


FUNCTION/MODULE                            EDATA_GROUP
--> CALLED FUNCTION/MODULE                 START  STOP
======================================================
?C_C51STARTUP?1                            ----- -----

*** NEW ROOT *****************************

IIC_I/isr                                  ----- -----

*** NEW ROOT *****************************

CMP_I/isr                                  ----- -----

*** NEW ROOT *****************************

LVD_I/isr                                  ----- -----

*** NEW ROOT *****************************

SPI_I/isr                                  ----- -----

*** NEW ROOT *****************************

UART1_I/isr                                ----- -----

*** NEW ROOT *****************************

UART2_I/isr                                ----- -----

L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 10


*** NEW ROOT *****************************

UART3_I/isr                                ----- -----

*** NEW ROOT *****************************

UART4_I/isr                                ----- -----

*** NEW ROOT *****************************

DMA_UART1_RXD_isr/isr                      ----- -----

*** NEW ROOT *****************************

DMA_UART2_RXD_isr/isr                      ----- -----

*** NEW ROOT *****************************

DMA_UART3_RXD_isr/isr                      ----- -----

*** NEW ROOT *****************************

DMA_UART1_TXD_isr/isr                      ----- -----

*** NEW ROOT *****************************

DMA_UART4_RXD_isr/isr                      ----- -----

*** NEW ROOT *****************************

DMA_UART2_TXD_isr/isr                      ----- -----

*** NEW ROOT *****************************

DMA_UART3_TXD_isr/isr                      ----- -----

*** NEW ROOT *****************************

DMA_UART4_TXD_isr/isr                      ----- -----

*** NEW ROOT *****************************

DMA_ADC_isr/isr                            ----- -----

*** NEW ROOT *****************************

CAN1_I/isr                                 ----- -----
  +--> CanReadReg/CAN

CanReadReg/CAN                             ----- -----

*** NEW ROOT *****************************

CAN2_I/isr                                 ----- -----
  +--> CanReadReg/CAN

*** NEW ROOT *****************************

DMA_M2M_isr/isr                            ----- -----

*** NEW ROOT *****************************

PIN0_I/isr                                 ----- -----

*** NEW ROOT *****************************
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 11



PIN1_I/isr                                 ----- -----

*** NEW ROOT *****************************

PIN2_I/isr                                 ----- -----

*** NEW ROOT *****************************

PIN3_I/isr                                 ----- -----

*** NEW ROOT *****************************

PIN4_I/isr                                 ----- -----

*** NEW ROOT *****************************

INT0_I/isr                                 ----- -----

*** NEW ROOT *****************************

PIN5_I/isr                                 ----- -----

*** NEW ROOT *****************************

INT1_I/isr                                 ----- -----

*** NEW ROOT *****************************

PIN6_I/isr                                 ----- -----

*** NEW ROOT *****************************

INT2_I/isr                                 ----- -----
  +--> INT2_isr/main

INT2_isr/main                              ----- -----

*** NEW ROOT *****************************

PIN7_I/isr                                 ----- -----

*** NEW ROOT *****************************

INT3_I/isr                                 ----- -----

*** NEW ROOT *****************************

INT4_I/isr                                 ----- -----

*** NEW ROOT *****************************

DMA_SPI_isr/isr                            ----- -----

*** NEW ROOT *****************************

TIMER0_I/isr                               ----- -----
  +--> Key_Rst/config
  +--> Usb_Rst/config
  +--> TM0_isr/main

Key_Rst/config                             ----- -----
  +--> Delay_X_mS/Delay

Delay_X_mS/Delay                           ----- -----
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 12



Usb_Rst/config                             ----- -----
  +--> HID_isr/config
  +--> usb_OUT_done/usb

HID_isr/config                             ----- -----

usb_OUT_done/usb                           ----- -----
  +--> usb_write_reg/usb

usb_write_reg/usb                          ----- -----

TM0_isr/main                               ----- -----

*** NEW ROOT *****************************

TIMER1_I/isr                               ----- -----

*** NEW ROOT *****************************

TIMER2_I/isr                               ----- -----

*** NEW ROOT *****************************

PWMA_I/isr                                 ----- -----

*** NEW ROOT *****************************

TIMER3_I/isr                               ----- -----

*** NEW ROOT *****************************

PWMB_I/isr                                 ----- -----

*** NEW ROOT *****************************

TIMER4_I/isr                               ----- -----

*** NEW ROOT *****************************

ADC_I/isr                                  ----- -----

*** NEW ROOT *****************************

usb_isr/usb                                ----- -----
  +--> usb_read_reg/usb
  +--> usb_resume/usb
  +--> usb_reset/usb
  +--> usb_setup/usb
  +--> usb_in_ep1/usb
  +--> usb_in_ep2/usb
  +--> usb_out_ep1/usb
  +--> usb_suspend/usb

usb_read_reg/usb                           ----- -----

usb_resume/usb                             ----- -----

usb_reset/usb                              ----- -----
  +--> usb_write_reg/usb

usb_setup/usb                              ----- -----
  +--> usb_write_reg/usb
  +--> usb_read_reg/usb
  +--> usb_read_fifo?_/usb
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 13


  +--> reverse2/util
  +--> usb_req_std/usb_req_std
  +--> usb_req_class/usb_req_class
  +--> usb_req_vendor/usb_req_vendor
  +--> usb_setup_stall/usb
  +--> usb_ctrl_in/usb
  +--> usb_ctrl_out/usb

usb_read_fifo?_/usb                        ----- -----
  +--> usb_read_reg/usb

reverse2/util                              00B6H 00B9H

usb_req_std/usb_req_std                    ----- -----
  +--> usb_get_status/usb_req_std
  +--> usb_clear_feature/usb_req_std
  +--> usb_set_feature/usb_req_std
  +--> usb_set_address/usb_req_std
  +--> usb_get_descriptor/usb_req_std
  +--> usb_set_descriptor/usb_req_std
  +--> usb_get_configuration/usb_req_std
  +--> usb_set_configuration/usb_req_std
  +--> usb_get_interface/usb_req_std
  +--> usb_set_interface/usb_req_std
  +--> usb_synch_frame/usb_req_std
  +--> usb_setup_stall/usb

usb_get_status/usb_req_std                 ----- -----
  +--> usb_setup_stall/usb
  +--> usb_setup_in/usb

usb_setup_stall/usb                        ----- -----
  +--> usb_write_reg/usb

usb_setup_in/usb                           ----- -----
  +--> usb_write_reg/usb
  +--> usb_ctrl_in/usb

usb_ctrl_in/usb                            ----- -----
  +--> usb_write_reg/usb
  +--> usb_read_reg/usb
  +--> usb_write_fifo?_/usb

usb_write_fifo?_/usb                       ----- -----
  +--> usb_write_reg/usb

usb_clear_feature/usb_req_std              ----- -----
  +--> usb_write_reg/usb
  +--> usb_setup_stall/usb
  +--> usb_setup_status/usb

usb_setup_status/usb                       ----- -----
  +--> usb_write_reg/usb

usb_set_feature/usb_req_std                ----- -----
  +--> usb_write_reg/usb
  +--> usb_setup_stall/usb
  +--> usb_setup_status/usb

usb_set_address/usb_req_std                ----- -----
  +--> usb_setup_stall/usb
  +--> usb_write_reg/usb
  +--> usb_setup_status/usb

usb_get_descriptor/usb_req_std             ----- -----
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 14


  +--> usb_setup_stall/usb
  +--> usb_setup_in/usb

usb_set_descriptor/usb_req_std             ----- -----
  +--> usb_setup_stall/usb

usb_get_configuration/usb_req_std          ----- -----
  +--> usb_setup_stall/usb
  +--> usb_setup_in/usb

usb_set_configuration/usb_req_std          ----- -----
  +--> usb_setup_stall/usb
  +--> usb_write_reg/usb
  +--> usb_setup_status/usb

usb_get_interface/usb_req_std              ----- -----
  +--> usb_setup_stall/usb
  +--> usb_setup_in/usb

usb_set_interface/usb_req_std              ----- -----
  +--> usb_setup_stall/usb
  +--> usb_setup_status/usb

usb_synch_frame/usb_req_std                ----- -----
  +--> usb_setup_stall/usb

usb_req_class/usb_req_class                ----- -----
  +--> usb_set_line_coding/usb_req_class
  +--> usb_get_line_coding/usb_req_class
  +--> usb_set_ctrl_line_state/usb_req_class
  +--> usb_setup_stall/usb

usb_set_line_coding/usb_req_class          ----- -----
  +--> usb_setup_stall/usb
  +--> usb_setup_out/usb

usb_setup_out/usb                          ----- -----
  +--> usb_write_reg/usb

usb_get_line_coding/usb_req_class          ----- -----
  +--> usb_setup_stall/usb
  +--> usb_setup_in/usb

usb_set_ctrl_line_state/usb_req_class      ----- -----
  +--> usb_setup_stall/usb
  +--> usb_setup_status/usb

usb_req_vendor/usb_req_vendor              ----- -----
  +--> usb_setup_stall/usb

usb_ctrl_out/usb                           ----- -----
  +--> usb_write_reg/usb
  +--> usb_read_reg/usb
  +--> usb_read_fifo?_/usb

usb_in_ep1/usb                             ----- -----
  +--> usb_write_reg/usb
  +--> usb_read_reg/usb

usb_in_ep2/usb                             ----- -----
  +--> usb_write_reg/usb
  +--> usb_read_reg/usb

usb_out_ep1/usb                            ----- -----
  +--> usb_write_reg/usb
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 15


  +--> usb_read_reg/usb
  +--> usb_read_fifo?_/usb
  +--> sleep_ms/util

sleep_ms/util                              ----- -----
  +--> sleep_us/util

sleep_us/util                              ----- -----

usb_suspend/usb                            ----- -----

*** NEW ROOT *****************************

?C_C51STARTUP                              ----- -----

*** NEW ROOT *****************************

?C_C51STARTUP?3                            ----- -----
  +--> main/main

main/main                                  ----- -----
  +--> System_init/config
  +--> PIT_init_ms/PIT
  +--> OLED_Init/OLED_SPI
  +--> UART_init/UART
  +--> DMA_RXD_init/DMA
  +--> ADC_init/ADC
  +--> INT_init/INT
  +--> PWM_init/PWM
  +--> EC11_app/main
  +--> IR_Object_Detection/main
  +--> Gripper_Control/main
  +--> ADC_get/ADC
  +--> DIS_OLED/main
  +--> MOTOR_control/main

System_init/config                         ----- -----
  +--> usb_init/usb

usb_init/usb                               ----- -----
  +--> usb_write_reg/usb

PIT_init_ms/PIT                            ----- -----
  +--> ?C?ULIDIV/?C?ULDIV

?C?ULIDIV/?C?ULDIV                         ----- -----

OLED_Init/OLED_SPI                         ----- -----
  +--> ESD_SPI_Init/SPI
  +--> Delay_X_mS/Delay
  +--> OLED_WR_Byte/OLED_SPI
  +--> OLED_Clear/OLED_SPI

ESD_SPI_Init/SPI                           ----- -----

OLED_WR_Byte/OLED_SPI                      ----- -----
  +--> ESD_SPI_WriteByte/SPI

ESD_SPI_WriteByte/SPI                      ----- -----

OLED_Clear/OLED_SPI                        ----- -----
  +--> OLED_WR_Byte/OLED_SPI

UART_init/UART                             ----- -----
  +--> ?C?ULIDIV/?C?ULDIV
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 16



DMA_RXD_init/DMA                           ----- -----

ADC_init/ADC                               ----- -----

INT_init/INT                               ----- -----

PWM_init/PWM                               00BAH 00BDH
  +--> ?C?ULIDIV/?C?ULDIV

EC11_app/main                              ----- -----

IR_Object_Detection/main                   ----- -----

Gripper_Control/main                       ----- -----
  +--> Set_Motor_Speed/main

Set_Motor_Speed/main                       ----- -----

ADC_get/ADC                                ----- -----

DIS_OLED/main                              ----- -----
  +--> OLED_ShowString/OLED_SPI
  +--> OLED_ShowFloat/OLED_SPI
  +--> OLED_ShowInt/OLED_SPI
  +--> OLED_ShowChar/OLED_SPI

OLED_ShowString/OLED_SPI                   00E4H 00E7H
  +--> OLED_ShowChar/OLED_SPI

OLED_ShowChar/OLED_SPI                     00E8H 00ECH
  +--> OLED_Set_Pos/OLED_SPI
  +--> OLED_WR_Byte/OLED_SPI

OLED_Set_Pos/OLED_SPI                      ----- -----
  +--> OLED_WR_Byte/OLED_SPI

OLED_ShowFloat/OLED_SPI                    00BAH 00E3H
  +--> SPRINTF/SPRINTF
  +--> OLED_ShowString/OLED_SPI

SPRINTF/SPRINTF                            00E4H 010BH
  +--> OUT/SPRINTF

OUT/SPRINTF                                ----- -----
  +--> PUTCH/SPRINTF

PUTCH/SPRINTF                              ----- -----

OLED_ShowInt/OLED_SPI                      00BAH 00E0H
  +--> SPRINTF/SPRINTF
  +--> OLED_ShowString/OLED_SPI

MOTOR_control/main                         ----- -----
  +--> PWM_change/PWM

PWM_change/PWM                             ----- -----

*** NEW ROOT *****************************

?C_C51STARTUP?2                            ----- -----



PUBLIC SYMBOLS OF MODULE:  .\Objects\mode (main)
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 17




      VALUE       CLASS    TYPE      PUBLIC SYMBOL NAME
      =================================================
      00FF043EH   CODE     NEAR LAB  ?C?CASTF
      000000FFH   NUMBER   ---       ?C?CODESEG
      00FF040BH   CODE     NEAR LAB  ?C?FCASTC
      00FF0406H   CODE     NEAR LAB  ?C?FCASTI
      00FF0401H   CODE     NEAR LAB  ?C?FCASTL
      00FF0CFDH   CODE     ---       ?C?FP2SERIES
      00FF01D4H   CODE     ---       ?C?FPADD
      00FF03AFH   CODE     ---       ?C?FPCMP
      00FF03ADH   CODE     ---       ?C?FPCMP3
      00FF0C02H   CODE     ---       ?C?FPCONVERT
      00FF032CH   CODE     ---       ?C?FPDIV
      00FF0584H   CODE     ---       ?C?FPGETOPN2
      00FF028CH   CODE     ---       ?C?FPMUL
      00FF05C1H   CODE     ---       ?C?FPNANRESULT
      00FF03F3H   CODE     ---       ?C?FPNEG
      00FF05C9H   CODE     ---       ?C?FPOVERFLOW
      00FF059DH   CODE     ---       ?C?FPRESULT
      00FF05B3H   CODE     ---       ?C?FPRESULT2
      00FF0CBAH   CODE     ---       ?C?FPROUND
      00FF0D06H   CODE     ---       ?C?FPSERIES
      00FF01D1H   CODE     ---       ?C?FPSUB
      00FF05C6H   CODE     ---       ?C?FPUNDERFLOW
      00FF0D5AH   CODE     ---       ?C?FTNPWR
      00FF6D8FH   CODE     ---       ?C?INITEDATA
      00FF84DFH   HCONST   WORD      ?C?INITEDATA_END
      00FF0DC3H   CODE     ---       ?C?LMUL
      00FF0602H   CODE     ---       ?C?PRNFMT
      00FF0D91H   CODE     ---       ?C?SIDIV
      00FF0E27H   CODE     NEAR LAB  ?C?SLDIV
      00FF0000H   CODE     ---       ?C?STARTUP
      00FF0DD6H   CODE     NEAR LAB  ?C?ULDIV
      00FF0DD4H   CODE     NEAR LAB  ?C?ULIDIV
      00000001H   NUMBER   ---       ?C?XDATASEG
      00FF0000H   CODE     ---       ?C_STARTUP
      00000021H.4 BIT      BIT       ?CAN_MKS_CONTROL?BIT
      00000022H.1 BIT      BIT       ?CANInit?BIT
      00000022H.2 BIT      BIT       ?CanReadMsg?BIT
      00000022H.3 BIT      BIT       ?CanSendMsg?BIT
      00000254H   EDATA    BYTE      ?CanSendMsg?BYTE
      00000021H.5 BIT      BIT       ?ESD_M1_POS_CONTROL?BIT
      00000004H   EDATA    BYTE      ?IMUupdate?BYTE
      000002B7H   EDATA    BYTE      ?Limit_float?BYTE
      000002B3H   EDATA    BYTE      ?Limit_int?BYTE
      00000021H.7 BIT      BIT       ?Out_IO?BIT
      000001D3H   EDATA    BYTE      ?printf_hid?BYTE
      000000B4H   EDATA    BYTE      ?PWM_init?BYTE
      000001A7H   EDATA    BYTE      ?SEG7_ShowString?BYTE
      0000010CH   EDATA    ---       ?sprintf?BYTE
      00000232H   EDATA    BYTE      ?UART_Send_float?BYTE
      00000242H   EDATA    BYTE      ?UART_Send_int?BYTE
      000002CFH   EDATA    ---       ?vsprintf?BYTE
      00FF820CH   CODE     ---       abs?_
      0000010CH   EDATA    INT       ACC_X
      0000010EH   EDATA    INT       ACC_Y
      00000110H   EDATA    INT       ACC_Z
*SFR* 000000BCH   DATA     BYTE      ADC_CONTR
*SFR* 000000BCH.5 DATA     BIT       ADC_FLAG
      00FF70A9H   CODE     ---       ADC_get
      00FF005AH   CODE     ---       ADC_I
      00FF671DH   CODE     ---       ADC_init
*SFR* 000000BCH.7 DATA     BIT       ADC_POWER
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 18


*SFR* 000000BDH   DATA     BYTE      ADC_RES
*SFR* 000000BEH   DATA     BYTE      ADC_RESL
*SFR* 000000BCH.6 DATA     BIT       ADC_START
*SFR* 000000DEH   DATA     BYTE      ADCCFG
      0000011AH   EDATA    FLOAT     ANG_ERR
      00000135H   EDATA    FLOAT     ANG_ERR_OLD
      00000124H   EDATA    FLOAT     ANG_OUT
      00000031H   EDATA    FLOAT     angle
      00000083H   EDATA    FLOAT     angle_dot
      00000041H   EDATA    FLOAT     Angle_err
      00000093H   EDATA    FLOAT     AngleX
      00000097H   EDATA    FLOAT     AngleY
      00FF0E5BH   CODE     ---       asc2_0806
      00FF1083H   CODE     ---       asc2_1608
      00FF0564H   CODE     ---       asin?_
      00FF0B22H   CODE     ---       ATAN?_
*SFR* 000000EFH   DATA     BYTE      AUXINTIF
*SFR* 0000008EH   DATA     BYTE      AUXR
*SFR* 00000097H   DATA     BYTE      AUXR2
      00FF7D20H   CODE     ---       BCD2HEX
      000002B8H   EDATA    BYTE      BRP
      00000021H.0 BIT      BIT       bUsbFeatureReady
      00000021H.1 BIT      BIT       bUsbInBusy
      00000020H.7 BIT      BIT       bUsbOutReady
      000000A3H   EDATA    CHAR      C_0
      00FF67F6H   CODE     ---       CAN1_I
      00FF6861H   CODE     ---       CAN2_I
*SFR* 000000F1H.5 DATA     BIT       CAN2IE
      00FF539AH   CODE     ---       CAN_MKS_CONTROL
      00FF6009H   CODE     ---       CAN_MKS_SPD_CONTROL
      000002B9H   EDATA    WORD      CAN_time
      00000022H.0 BIT      BIT       CAN_TX_OK
*SFR* 000000F1H   DATA     BYTE      CANICR
*SFR* 000000F1H.1 DATA     BIT       CANIE
      00FF3DEAH   CODE     ---       CANInit
      00FF60A0H   CODE     ---       CanReadFifo
      00FF593EH   CODE     ---       CanReadMsg
      00FF81A7H   CODE     ---       CanReadReg
*SFR* 00000097H.3 DATA     BIT       CANSEL
      00FF4636H   CODE     ---       CanSendMsg
      00FF8144H   CODE     ---       CanWriteReg
      00FF4C83H   CODE     ---       CAR_ADD_ANGLE
      00000112H   EDATA    FLOAT     CAR_ANG
      00FF587DH   CODE     ---       CAR_PID_CONTROL
      000001DBH   EDATA    ---       char_uchar
*SFR* 000000EAH   DATA     BYTE      CKCON
      00FF0022H   CODE     ---       CMP_I
*SFR* 000000E6H   DATA     BYTE      CMPCR1
*SFR* 000000E7H   DATA     BYTE      CMPCR2
      00FF6397H   CODE     ---       CONFIGDESC
      00000128H   EDATA    ---       D2_CAN_TX
      00000130H   EDATA    ---       D2int_uchar
      00000116H   EDATA    ---       D2long_uchar
      00000087H   EDATA    FLOAT     d_t
      00FF807AH   CODE     ---       Delay_X_mS
      00FF8091H   CODE     ---       Delay_X_uS
      0000015FH   EDATA    INT       detection_counter
      00FF6385H   CODE     ---       DEVICEDESC
      00000229H   EDATA    BYTE      DeviceState
      00FF4EA3H   CODE     ---       DIS_OLED
      00FF0031H   CODE     ---       DMA_ADC_isr
      00FF0032H   CODE     ---       DMA_M2M_isr
      00FF5EDBH   CODE     ---       DMA_RXD2_isr
      00FF479EH   CODE     ---       DMA_RXD_init
      00FF0050H   CODE     ---       DMA_SPI_isr
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 19


      00FF4A26H   CODE     ---       DMA_TXD_init
      00FF7130H   CODE     ---       DMA_UART1_RXD_isr
      00FF0066H   CODE     ---       DMA_UART1_TXD_isr
      00FF7172H   CODE     ---       DMA_UART2_RXD_isr
      00FF7BE8H   CODE     ---       DMA_UART2_TXD_isr
      00FF71B4H   CODE     ---       DMA_UART3_RXD_isr
      00FF7C05H   CODE     ---       DMA_UART3_TXD_isr
      00FF71F6H   CODE     ---       DMA_UART4_RXD_isr
      00FF7C22H   CODE     ---       DMA_UART4_TXD_isr
      0000009BH   EDATA    FLOAT     E
*SFR* 000000A8H.7 DATA     BIT       EA
*SFR* 000000BAH.7 DATA     BIT       EAXFR
      00FF70EDH   CODE     ---       EC11_app
      00000020H.5 BIT      BIT       EC11_DIR
      00000020H.1 BIT      BIT       EC11_R_flag
      00FF77B3H   CODE     ---       EEPROM_Change
      00FF79B6H   CODE     ---       EEPROM_Delete
      00FF8194H   CODE     ---       EEPROM_OFF
      00FF7787H   CODE     ---       EEPROM_Read
      0000022AH   EDATA    ---       Ep0State
      00000177H   EDATA    ---       err_spd
*SFR* 000000A8H.4 DATA     BIT       ES
*SFR* 000000AFH.0 DATA     BIT       ES2
*SFR* 000000AFH.3 DATA     BIT       ES3
*SFR* 000000AFH.4 DATA     BIT       ES4
      00FF7D04H   CODE     ---       ESD_IIC_READ_ACK_BYTE
      00FF7CE8H   CODE     ---       ESD_IIC_READ_NACK_BYTE
      00FF81ECH   CODE     ---       ESD_IIC_Recv_ack
      00FF7CCCH   CODE     ---       ESD_IIC_Recv_data
      00FF8034H   CODE     ---       ESD_IIC_Send_ack
      00FF80EDH   CODE     ---       ESD_IIC_Send_data
      00FF7FD3H   CODE     ---       ESD_IIC_Send_nack
      00FF81DCH   CODE     ---       ESD_IIC_Start
      00FF81FCH   CODE     ---       ESD_IIC_Stop
      00FF80BFH   CODE     ---       ESD_IIC_Wait
      00FF80D6H   CODE     ---       ESD_IIC_WRITE_ONE_BYTE
      00FF80A8H   CODE     ---       ESD_IIC_WRITE_START_BYTE
      00FF5A9CH   CODE     ---       ESD_Init_IIC
      00FF66AEH   CODE     ---       ESD_M1_POS_CONTROL
      00FF7643H   CODE     ---       ESD_M1_PWM_CONTROL
      00FF75DEH   CODE     ---       ESD_M1_SPD_CONTROL
      00FF63E6H   CODE     ---       ESD_Read_IIC
      00FF0016H   CODE     ---       ESD_SPI_Deinit
      00FF77DFH   CODE     ---       ESD_SPI_Init
      00FF8266H   CODE     ---       ESD_SPI_ReadByte
      00FF7971H   CODE     ---       ESD_SPI_ReadMultiBytes
      00FF8274H   CODE     ---       ESD_SPI_RW
      00FF829CH   CODE     ---       ESD_SPI_WriteByte
      00FF7A9CH   CODE     ---       ESD_SPI_WriteMultiBytes
      00FF6B1FH   CODE     ---       ESD_Write_IIC
*SFR* 000000A8H.1 DATA     BIT       ET0
*SFR* 000000A8H.3 DATA     BIT       ET1
*SFR* 000000AFH.7 DATA     BIT       EUSB
*SFR* 000000A8H.0 DATA     BIT       EX0
*SFR* 000000A8H.2 DATA     BIT       EX1
      00000035H   EDATA    FLOAT     exInt
      00000039H   EDATA    FLOAT     eyInt
      0000003DH   EDATA    FLOAT     ezInt
      00FF04B7H   CODE     NEAR LAB  fabs?_
      000001D7H   EDATA    ---       float_uchar
      00FF68CCH   CODE     ---       floor?_
      00FF822CH   CODE     ---       fmax
      00FF3718H   CODE     ---       Get_IO
      00FF434CH   CODE     ---       GPIO_init_8pin
      00FF699EH   CODE     ---       GPIO_init_allpin
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 20


      00FF340AH   CODE     ---       GPIO_init_pin
      00FF4D96H   CODE     ---       GPIO_isr_deinit
      00FF2739H   CODE     ---       GPIO_isr_init
      00FF41A5H   CODE     ---       GPIO_pull_pin
      00000020H.2 BIT      BIT       gripper_auto_mode
      00FF51B0H   CODE     ---       Gripper_Control
      00000153H   EDATA    INT       gripper_state
      00000173H   EDATA    INT       gripper_timeout
      00000147H   EDATA    FLOAT     GYRO_X
      0000014BH   EDATA    FLOAT     GYRO_Y
      0000014FH   EDATA    FLOAT     GYRO_Z
      00000122H   EDATA    INT       GYRO_Z_OFFSET
      00000132H   EDATA    INT       GYRO_Z_OFFSET_ADD
      00000134H   EDATA    BYTE      GYRO_Z_OFFSET_N
      00FF8063H   CODE     ---       HEX2BCD
      00FF001EH   CODE     ---       HID_isr
      00FF1673H   CODE     ---       Hzk
*SFR* 000000F6H   DATA     BYTE      IAP_ADDRE
*SFR* 000000C3H   DATA     BYTE      IAP_ADDRH
*SFR* 000000C4H   DATA     BYTE      IAP_ADDRL
*SFR* 000000C5H   DATA     BYTE      IAP_CMD
*SFR* 000000C7H   DATA     BYTE      IAP_CONTR
*SFR* 000000C2H   DATA     BYTE      IAP_DATA
*SFR* 000000F5H   DATA     BYTE      IAP_TPS
*SFR* 000000C6H   DATA     BYTE      IAP_TRIG
*SFR* 000000A8H   DATA     BYTE      IE
*SFR* 000000AFH   DATA     BYTE      IE2
      00FF0021H   CODE     ---       IIC_I
      00FF2D0CH   CODE     ---       IMUupdate
      00000231H   EDATA    BYTE      InEpState
      00FF0049H   CODE     ---       INT0_I
      00FF004AH   CODE     ---       INT1_I
      00FF8290H   CODE     ---       INT1_isr
      00FF7674H   CODE     ---       INT2_I
      00FF001AH   CODE     ---       INT2_isr
      00FF004EH   CODE     ---       INT3_I
      00FF004FH   CODE     ---       INT4_I
      00FF7611H   CODE     ---       INT_init
      000001E0H   EDATA    ---       int_uchar
*SFR* 0000008FH   DATA     BYTE      INTCLKO
*SFR* 000000B8H   DATA     BYTE      IP
*SFR* 000000B5H   DATA     BYTE      IP2
*SFR* 000000B6H   DATA     BYTE      IP2H
*SFR* 000000DFH   DATA     BYTE      IP3
*SFR* 000000EEH   DATA     BYTE      IP3H
*SFR* 000000B7H   DATA     BYTE      IPH
      0000016AH   EDATA    WORD      IR_DAT
      0000016EH   EDATA    CHAR      IR_ERR
      00000169H   EDATA    BYTE      IR_N
      00FF6CC7H   CODE     ---       IR_Object_Detection
*SFR* 0000009DH   DATA     BYTE      IRCBAND
      000100C0H   XDATA    ---       IRLED_dat
*SFR* 0000009FH   DATA     BYTE      IRTRIM
*SFR* 00000088H.0 DATA     BIT       IT0
*SFR* 00000088H.2 DATA     BIT       IT1
      000000B2H   EDATA    FLOAT     K1
      000000A4H   EDATA    FLOAT     K_0
      000000A8H   EDATA    FLOAT     K_1
      00FF3FF8H   CODE     ---       Kalman_Filter
      000001E6H   EDATA    WORD      Key_cnt
      00000155H   EDATA    ---       KEY_dat
      00000021H.2 BIT      BIT       Key_Flag
      00FF72F9H   CODE     ---       Key_Rst
      00FF821CH   CODE     ---       labs?_
      00FF6381H   CODE     ---       LANGIDDESC
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 21


      00FF7ED9H   CODE     ---       LCD12864_AutoWrapOff
      00FF7DA4H   CODE     ---       LCD12864_AutoWrapOn
      00FF7FA1H   CODE     ---       LCD12864_CursorMoveLeft
      00FF7E26H   CODE     ---       LCD12864_CursorMoveRight
      00FF7F88H   CODE     ---       LCD12864_CursorOff
      00FF7E40H   CODE     ---       LCD12864_CursorOn
      00FF7DD8H   CODE     ---       LCD12864_CursorReturnHome
      00FF8159H   CODE     ---       LCD12864_DisplayClear
      00FF7F0BH   CODE     ---       LCD12864_DisplayOff
      00FF7E0CH   CODE     ---       LCD12864_DisplayOn
      00FF7A3BH   CODE     ---       LCD12864_ReverseLine
      00FF7F3DH   CODE     ---       LCD12864_ScrollLeft
      00FF7D8AH   CODE     ---       LCD12864_ScrollRight
      00FF78D9H   CODE     ---       LCD12864_ScrollUp
      00FF6E0BH   CODE     ---       LCD12864_ShowPicture
      00FF7376H   CODE     ---       LCD12864_ShowString
      00FF701BH   CODE     ---       LED40_SendData
      00FF7062H   CODE     ---       LED64_SendData
      00FF79D8H   CODE     ---       Limit_float
      00FF804CH   CODE     ---       Limit_int
      0000029AH   EDATA    ---       LineCoding
*SFR* 000000F9H   DATA     BYTE      LINICR
      00FF04C6H   CODE     NEAR LAB  log10?_
      00FF0A3DH   CODE     NEAR LAB  LOG?_
      000001DCH   EDATA    ---       long_uchar
      000001F6H   EDATA    ---       long_uchar1
      00FF0029H   CODE     ---       LVD_I
      00FF48E6H   CODE     ---       main
      00FF63DEH   CODE     ---       MANUFACTDESC
      00FF794CH   CODE     ---       memcpy?_
      00FF0060H   CODE     ---       MKS_ALL_RUN
      00FF562DH   CODE     ---       MKS_HOME
      000001FAH   EDATA    WORD      MKS_KD
      000001FCH   EDATA    WORD      MKS_KI
      000001FEH   EDATA    WORD      MKS_KP
      00000200H   EDATA    WORD      MKS_KV
      00FF547EH   CODE     ---       MKS_PID_SET
      00000202H   EDATA    ---       MKS_TX
      00FF7A5CH   CODE     ---       MOTOR_control
      0000016FH   EDATA    LONG      motor_pos
      00000157H   EDATA    LONG      motor_pos_old
      0000016CH   EDATA    INT       motor_spd
      000001F4H   EDATA    WORD      MOTOR_state
      00000023H   EDATA    INT       mpu6050_acc_x
      00000025H   EDATA    INT       mpu6050_acc_y
      0000002BH   EDATA    INT       mpu6050_acc_z
      00FF72B9H   CODE     ---       mpu6050_get_acc
      00FF6DBBH   CODE     ---       mpu6050_get_gyro
      0000005DH   EDATA    INT       mpu6050_gyro_x
      0000005FH   EDATA    INT       mpu6050_gyro_y
      00000061H   EDATA    INT       mpu6050_gyro_z
      00FF678AH   CODE     ---       mpu6050_iic_init
      00FF6C20H   CODE     ---       mpu6050_init
      00FF785FH   CODE     ---       mpu6050_simiic_read_reg
      00FF6B77H   CODE     ---       mpu6050_simiic_read_regs
      00000139H   EDATA    ---       MPU_data
      000000ACH   EDATA    INT       mpu_temp
      00000175H   EDATA    INT       NUM
      00000020H.4 BIT      BIT       object_detected
      00FF7F6FH   CODE     ---       OLED12864_DisplayContent
      00FF7DF2H   CODE     ---       OLED12864_DisplayEntire
      00FF7EA7H   CODE     ---       OLED12864_DisplayOff
      00FF7D70H   CODE     ---       OLED12864_DisplayOn
      00FF816DH   CODE     ---       OLED12864_DisplayReverse
      00FF7F56H   CODE     ---       OLED12864_HorizontalMirror
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 22


      00FF749CH   CODE     ---       OLED12864_ScrollLeft
      00FF7428H   CODE     ---       OLED12864_ScrollRight
      00FF7D56H   CODE     ---       OLED12864_ScrollStart
      00FF7EF2H   CODE     ---       OLED12864_ScrollStop
      00FF7462H   CODE     ---       OLED12864_ScrollUp
      00FF79F9H   CODE     ---       OLED12864_SetAddressMode
      00FF7A1AH   CODE     ---       OLED12864_SetContrast
      00FF7279H   CODE     ---       OLED12864_ShowPicture
      00FF7DBEH   CODE     ---       OLED12864_VerticalMirror
      00FF750BH   CODE     ---       OLED_Clear
      00FF7BACH   CODE     ---       OLED_ColorTurn
*SFR* 000000A0H.2 DATA     BIT       OLED_CS
*SFR* 000000A0H.4 DATA     BIT       OLED_DC
      00FF812FH   CODE     ---       OLED_Display_Off
      00FF811AH   CODE     ---       OLED_Display_On
      00FF775BH   CODE     ---       OLED_DisplayTurn
      00FF5F72H   CODE     ---       OLED_DrawBMP
      00000020H.3 BIT      BIT       OLED_flag
      00FF5556H   CODE     ---       OLED_Init
      00FF7E74H   CODE     ---       oled_pow
*SFR* 000000A0H.6 DATA     BIT       OLED_RES
      00FF7888H   CODE     ---       OLED_Set_Pos
      00FF57BAH   CODE     ---       OLED_ShowChar
      00FF6468H   CODE     ---       OLED_ShowChinese
      00FF4FA9H   CODE     ---       OLED_ShowFloat
      00FF5E43H   CODE     ---       OLED_ShowInt
      00FF52B2H   CODE     ---       OLED_ShowNum
      00FF655CH   CODE     ---       OLED_ShowString
      00FF7E5AH   CODE     ---       OLED_WR_Byte
      00FF3BC9H   CODE     ---       Out_IO
      00000232H   EDATA    BYTE      OutEpState
      00000227H   EDATA    BYTE      OutNumber
*SFR* 00000080H   DATA     BYTE      P0
*SFR* 00000080H.0 DATA     BIT       P00
*SFR* 00000080H.1 DATA     BIT       P01
*SFR* 00000080H.2 DATA     BIT       P02
*SFR* 00000080H.3 DATA     BIT       P03
*SFR* 00000080H.4 DATA     BIT       P04
*SFR* 00000080H.5 DATA     BIT       P05
*SFR* 00000080H.6 DATA     BIT       P06
*SFR* 00000080H.7 DATA     BIT       P07
*SFR* 00000094H   DATA     BYTE      P0M0
*SFR* 00000093H   DATA     BYTE      P0M1
*SFR* 00000090H   DATA     BYTE      P1
*SFR* 00000090H.0 DATA     BIT       P10
*SFR* 00000090H.1 DATA     BIT       P11
*SFR* 00000090H.2 DATA     BIT       P12
*SFR* 00000090H.3 DATA     BIT       P13
*SFR* 00000090H.4 DATA     BIT       P14
*SFR* 00000090H.5 DATA     BIT       P15
*SFR* 00000090H.6 DATA     BIT       P16
*SFR* 00000090H.7 DATA     BIT       P17
*SFR* 00000092H   DATA     BYTE      P1M0
*SFR* 00000091H   DATA     BYTE      P1M1
*SFR* 000000A0H   DATA     BYTE      P2
*SFR* 000000A0H.0 DATA     BIT       P20
*SFR* 000000A0H.1 DATA     BIT       P21
*SFR* 000000A0H.2 DATA     BIT       P22
*SFR* 000000A0H.3 DATA     BIT       P23
*SFR* 000000A0H.4 DATA     BIT       P24
*SFR* 000000A0H.5 DATA     BIT       P25
*SFR* 000000A0H.6 DATA     BIT       P26
*SFR* 000000A0H.7 DATA     BIT       P27
*SFR* 00000096H   DATA     BYTE      P2M0
*SFR* 00000095H   DATA     BYTE      P2M1
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 23


*SFR* 000000B0H   DATA     BYTE      P3
*SFR* 000000B0H.0 DATA     BIT       P30
*SFR* 000000B0H.1 DATA     BIT       P31
*SFR* 000000B0H.2 DATA     BIT       P32
*SFR* 000000B0H.3 DATA     BIT       P33
*SFR* 000000B0H.4 DATA     BIT       P34
*SFR* 000000B0H.5 DATA     BIT       P35
*SFR* 000000B0H.6 DATA     BIT       P36
*SFR* 000000B0H.7 DATA     BIT       P37
*SFR* 000000B2H   DATA     BYTE      P3M0
*SFR* 000000B1H   DATA     BYTE      P3M1
*SFR* 000000C0H   DATA     BYTE      P4
*SFR* 000000C0H.0 DATA     BIT       P40
*SFR* 000000C0H.1 DATA     BIT       P41
*SFR* 000000C0H.2 DATA     BIT       P42
*SFR* 000000C0H.3 DATA     BIT       P43
*SFR* 000000C0H.4 DATA     BIT       P44
*SFR* 000000B4H   DATA     BYTE      P4M0
*SFR* 000000B3H   DATA     BYTE      P4M1
*SFR* 000000C8H   DATA     BYTE      P5
*SFR* 000000C8H.0 DATA     BIT       P50
*SFR* 000000C8H.1 DATA     BIT       P51
*SFR* 000000C8H.2 DATA     BIT       P52
*SFR* 000000C8H.3 DATA     BIT       P53
*SFR* 000000C8H.4 DATA     BIT       P54
*SFR* 000000C8H.5 DATA     BIT       P55
*SFR* 000000CAH   DATA     BYTE      P5M0
*SFR* 000000C9H   DATA     BYTE      P5M1
*SFR* 000000E8H   DATA     BYTE      P6
*SFR* 000000E8H.0 DATA     BIT       P60
*SFR* 000000E8H.1 DATA     BIT       P61
*SFR* 000000E8H.2 DATA     BIT       P62
*SFR* 000000E8H.3 DATA     BIT       P63
*SFR* 000000E8H.4 DATA     BIT       P64
*SFR* 000000E8H.5 DATA     BIT       P65
*SFR* 000000E8H.6 DATA     BIT       P66
*SFR* 000000E8H.7 DATA     BIT       P67
*SFR* 000000CCH   DATA     BYTE      P6M0
*SFR* 000000CBH   DATA     BYTE      P6M1
*SFR* 000000F8H   DATA     BYTE      P7
*SFR* 000000F8H.0 DATA     BIT       P70
*SFR* 000000F8H.1 DATA     BIT       P71
*SFR* 000000F8H.2 DATA     BIT       P72
*SFR* 000000F8H.3 DATA     BIT       P73
*SFR* 000000F8H.4 DATA     BIT       P74
*SFR* 000000F8H.5 DATA     BIT       P75
*SFR* 000000F8H.6 DATA     BIT       P76
*SFR* 000000F8H.7 DATA     BIT       P77
*SFR* 000000E2H   DATA     BYTE      P7M0
*SFR* 000000E1H   DATA     BYTE      P7M1
*SFR* 000000A2H   DATA     BYTE      P_SW1
*SFR* 000000BAH   DATA     BYTE      P_SW2
*SFR* 000000BBH   DATA     BYTE      P_SW3
      00FF63DAH   CODE     ---       PACKET0
      00FF63DCH   CODE     ---       PACKET1
*SFR* 00000087H   DATA     BYTE      PCON
      00000045H   EDATA    FLOAT     PCt_0
      00000059H   EDATA    FLOAT     PCt_1
      00000049H   EDATA    ---       Pdot
      00FF7ABCH   CODE     ---       PIN0_I
      00FF7ADAH   CODE     ---       PIN1_I
      00FF7AF8H   CODE     ---       PIN2_I
      00FF7B16H   CODE     ---       PIN3_I
      00FF7B34H   CODE     ---       PIN4_I
      00FF7B52H   CODE     ---       PIN5_I
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 24


      00FF7B70H   CODE     ---       PIN6_I
      00FF7B8EH   CODE     ---       PIN7_I
      00FF6C74H   CODE     ---       PIT_count_clean
      00FF6EA7H   CODE     ---       PIT_count_get
      00FF6EF4H   CODE     ---       PIT_init_encoder
      00FF61C9H   CODE     ---       PIT_init_ms
      00FF6136H   CODE     ---       PIT_init_us
      00000063H   EDATA    ---       PP
      00000020H.0 BIT      BIT       PRINT_flag
      00FF772EH   CODE     ---       printf_hid
      00FF6363H   CODE     ---       PRODUCTDESC
*SFR* 000000D0H   DATA     BYTE      PSW
*SFR* 000000B5H.7 DATA     BIT       PUSB
*SFR* 000000B6H.7 DATA     BIT       PUSBH
      00FF8501H   HCONST   ---       PWM_ARR_ADDR
      00FF8509H   HCONST   ---       PWM_CCER_ADDR
      00FF8519H   HCONST   ---       PWM_CCMR_ADDR
      00FF84E1H   HCONST   ---       PWM_CCR_ADDR
      00FF399FH   CODE     ---       PWM_change
      00FF30BEH   CODE     ---       PWM_init
      0000015BH   EDATA    LONG      pwm_out
      00FF0056H   CODE     ---       PWMA_I
      00FF0058H   CODE     ---       PWMB_I
      00000161H   EDATA    FLOAT     PWR_U
      00000073H   EDATA    FLOAT     q0
      00000077H   EDATA    FLOAT     q1
      0000007BH   EDATA    FLOAT     q2
      0000007FH   EDATA    FLOAT     q3
      0000008BH   EDATA    FLOAT     Q_angle
      0000009FH   EDATA    FLOAT     Q_bias
      000000AEH   EDATA    FLOAT     Q_gyro
      0000008FH   EDATA    FLOAT     R_angle
*SFR* 000000B0H.2 DATA     BIT       Reset_PIN
      00FF7576H   CODE     ---       reverse2
      00FF6A04H   CODE     ---       reverse4
*SFR* 00000098H.0 DATA     BIT       RI
*SFR* 000000FFH   DATA     BYTE      RSTCFG
*SFR* 0000009BH   DATA     BYTE      S2BUF
*SFR* 0000009AH   DATA     BYTE      S2CON
*SFR* 0000009AH.0 DATA     BIT       S2RI
*SFR* 0000009AH.1 DATA     BIT       S2TI
*SFR* 000000ADH   DATA     BYTE      S3BUF
*SFR* 000000ACH   DATA     BYTE      S3CON
*SFR* 000000ACH.0 DATA     BIT       S3RI
*SFR* 000000ACH.1 DATA     BIT       S3TI
*SFR* 000000FEH   DATA     BYTE      S4BUF
*SFR* 000000FDH   DATA     BYTE      S4CON
*SFR* 000000FDH.0 DATA     BIT       S4RI
*SFR* 000000FDH.1 DATA     BIT       S4TI
      00000020H.6 BIT      BIT       S_flag
*SFR* 00000099H   DATA     BYTE      SBUF
*SFR* 00000098H   DATA     BYTE      SCON
      00FF7541H   CODE     ---       SEG7_ShowCode
      00FF75ABH   CODE     ---       SEG7_ShowFloat
      00FF00EEH   CODE     ---       SEG7_ShowLong
      00FF6F8AH   CODE     ---       SEG7_ShowString
      0000011EH   EDATA    FLOAT     SET_ANG
      00FF4B66H   CODE     ---       set_clk
      00FF0006H   CODE     ---       Set_Motor_Speed
      0000017DH   EDATA    INT       set_spd
      0000021FH   EDATA    ---       Setup
      00FF801CH   CODE     ---       sleep_ms
      00FF7C3FH   CODE     ---       sleep_us
*SFR* 000000CEH   DATA     BYTE      SPCTL
*SFR* 000000CFH   DATA     BYTE      SPDAT
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 25


      00FF002AH   CODE     ---       SPI_I
      00FF047BH   CODE     NEAR LAB  sprintf
*SFR* 000000CDH   DATA     BYTE      SPSTAT
      00FF000EH   CODE     ---       sq
      00FF04D4H   CODE     ---       sqrt?_
      00FF8282H   CODE     ---       strlen?_
      000001F0H   EDATA    DWORD     sys_clk
      00FF65CFH   CODE     ---       System_init
      0000024FH   EDATA    WORD      T0_cnt
      00000251H   EDATA    WORD      T1_cnt
      00000253H   EDATA    WORD      T2_cnt
*SFR* 000000D6H   DATA     BYTE      T2H
*SFR* 000000D7H   DATA     BYTE      T2L
*SFR* 0000008EH.4 DATA     BIT       T2R
*SFR* 0000008EH.2 DATA     BIT       T2x12
      00000255H   EDATA    WORD      T3_cnt
*SFR* 000000D4H   DATA     BYTE      T3H
*SFR* 000000D5H   DATA     BYTE      T3L
*SFR* 000000DDH.3 DATA     BIT       T3R
*SFR* 000000DDH.1 DATA     BIT       T3x12
      00000257H   EDATA    WORD      T4_cnt
*SFR* 000000D2H   DATA     BYTE      T4H
*SFR* 000000D3H   DATA     BYTE      T4L
*SFR* 000000DDH.7 DATA     BIT       T4R
*SFR* 000000DDH   DATA     BYTE      T4T3M
*SFR* 000000DDH.5 DATA     BIT       T4x12
      00000027H   EDATA    FLOAT     t_0
      0000002DH   EDATA    FLOAT     t_1
      00000165H   EDATA    LONG      target_pos
*SFR* 00000088H   DATA     BYTE      TCON
*SFR* 0000008CH   DATA     BYTE      TH0
*SFR* 0000008DH   DATA     BYTE      TH1
*SFR* 00000098H.1 DATA     BIT       TI
      00FF74D5H   CODE     ---       TIMER0_I
      00FF0051H   CODE     ---       TIMER1_I
      00FF0052H   CODE     ---       TIMER2_I
      00FF0057H   CODE     ---       TIMER3_I
      00FF0059H   CODE     ---       TIMER4_I
*SFR* 0000008AH   DATA     BYTE      TL0
*SFR* 0000008BH   DATA     BYTE      TL1
      00FF64E4H   CODE     ---       TM0_isr
*SFR* 00000089H   DATA     BYTE      TMOD
*SFR* 00000088H.4 DATA     BIT       TR0
*SFR* 00000088H.6 DATA     BIT       TR1
      000002B6H   EDATA    BYTE      TSG1
      000002B7H   EDATA    BYTE      TSG2
      00FF7FECH   CODE     ---       UART1_I
      000002D3H   EDATA    BYTE      UART1_OK
      00FF7C5CH   CODE     ---       UART2_I
      000002D4H   EDATA    BYTE      UART2_OK
      00FF7C78H   CODE     ---       UART3_I
      000002D5H   EDATA    BYTE      UART3_OK
      00FF7C94H   CODE     ---       UART4_I
      000002D6H   EDATA    BYTE      UART4_OK
      00FF44CAH   CODE     ---       UART_init
      00FF6D19H   CODE     ---       UART_Send_byte
      00FF1793H   CODE     ---       UART_Send_float
      00FF1FB0H   CODE     ---       UART_Send_int
      00FF7BCAH   CODE     ---       UART_Send_string
      00FF00AEH   CODE     ---       usb_bulk_intr_in
      00FF7E8EH   CODE     ---       usb_bulk_intr_out
      00FF5B3CH   CODE     ---       usb_clear_feature
      00FF6937H   CODE     ---       usb_ctrl_in
      00FF6E5AH   CODE     ---       usb_ctrl_out
      00000021H.3 BIT      BIT       USB_flag
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 26


      00FF6BCCH   CODE     ---       usb_get_configuration
      00FF5BDBH   CODE     ---       usb_get_descriptor
      00FF7338H   CODE     ---       usb_get_interface
      00FF7900H   CODE     ---       usb_get_line_coding
      00FF50ADH   CODE     ---       usb_get_status
      00FF76A4H   CODE     ---       usb_IN
      00FF7701H   CODE     ---       usb_in_ep1
      00FF780BH   CODE     ---       usb_in_ep2
      00FF6A65H   CODE     ---       usb_init
      00FF62DFH   CODE     ---       usb_isr
      00FF8004H   CODE     ---       usb_OUT_done
      00FF56F6H   CODE     ---       usb_out_ep1
      00FF73EEH   CODE     ---       usb_read_fifo?_
      00FF0166H   CODE     ---       usb_read_reg
      00FF7D3BH   CODE     ---       usb_req_class
      00FF6AC3H   CODE     ---       usb_req_std
      00FF0046H   CODE     ---       usb_req_vendor
      00FF6FD3H   CODE     ---       usb_reset
      00FF005FH   CODE     ---       usb_resume
      00FF82B3H   CODE     ---       Usb_Rst
      00FF663FH   CODE     ---       USB_SendData
      00FF6F40H   CODE     ---       usb_set_address
      00FF59F7H   CODE     ---       usb_set_configuration
      00FF8104H   CODE     ---       usb_set_ctrl_line_state
      00FF0026H   CODE     ---       usb_set_descriptor
      00FF5C7AH   CODE     ---       usb_set_feature
      00FF78B1H   CODE     ---       usb_set_interface
      00FF7926H   CODE     ---       usb_set_line_coding
      00FF6255H   CODE     ---       usb_setup
      00FF81BAH   CODE     ---       usb_setup_in
      00FF824AH   CODE     ---       usb_setup_out
      00FF8258H   CODE     ---       usb_setup_stall
      00FF0036H   CODE     ---       usb_setup_status
      00FF005EH   CODE     ---       usb_suspend
      00FF002EH   CODE     ---       usb_synch_frame
      00FF7836H   CODE     ---       usb_write_fifo?_
      00FF8181H   CODE     ---       usb_write_reg
*SFR* 000000FCH   DATA     BYTE      USBADR
*SFR* 000000DCH   DATA     BYTE      USBCLK
*SFR* 000000F4H   DATA     BYTE      USBCON
*SFR* 000000ECH   DATA     BYTE      USBDAT
      00010080H   XDATA    ---       UsbFeatureBuffer
      00010000H   XDATA    ---       UsbInBuffer
      00010040H   XDATA    ---       UsbOutBuffer
      000001ECH   EDATA    ---       USER_DEVICEDESC
      000001E8H   EDATA    ---       USER_PRODUCTDESC
      000001E2H   EDATA    ---       USER_STCISPCMD
*SFR* 000000A6H   DATA     BYTE      VRTRIM
      00FF049BH   CODE     NEAR LAB  vsprintf
*SFR* 000000C1H   DATA     BYTE      WDT_CONTR
*SFR* 000000E9H   DATA     BYTE      WTST
      00FF7238H   CODE     ---       Yijielvbo



SYMBOL TABLE OF MODULE:  .\Objects\mode (main)

      VALUE       REP       CLASS    TYPE      SYMBOL NAME
      ====================================================
      ---         MODULE    ---      ---       main
      00FF64E4H   PUBLIC    CODE     ---       TM0_isr
      00FF5EDBH   PUBLIC    CODE     ---       DMA_RXD2_isr
      00FF70EDH   PUBLIC    CODE     ---       EC11_app
      00FF51B0H   PUBLIC    CODE     ---       Gripper_Control
      00FF48E6H   PUBLIC    CODE     ---       main
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 27


      00FF8290H   PUBLIC    CODE     ---       INT1_isr
      00FF001AH   PUBLIC    CODE     ---       INT2_isr
      00FF6CC7H   PUBLIC    CODE     ---       IR_Object_Detection
      00FF4EA3H   PUBLIC    CODE     ---       DIS_OLED
      00FF0006H   PUBLIC    CODE     ---       Set_Motor_Speed
      00FF7A5CH   PUBLIC    CODE     ---       MOTOR_control
      00000153H   PUBLIC    EDATA    INT       gripper_state
      00000155H   PUBLIC    EDATA    ---       KEY_dat
      00000157H   PUBLIC    EDATA    LONG      motor_pos_old
      0000015BH   PUBLIC    EDATA    LONG      pwm_out
      0000015FH   PUBLIC    EDATA    INT       detection_counter
      00000161H   PUBLIC    EDATA    FLOAT     PWR_U
      00000165H   PUBLIC    EDATA    LONG      target_pos
      00000169H   PUBLIC    EDATA    BYTE      IR_N
      0000016AH   PUBLIC    EDATA    WORD      IR_DAT
      0000016CH   PUBLIC    EDATA    INT       motor_spd
      0000016EH   PUBLIC    EDATA    CHAR      IR_ERR
      0000016FH   PUBLIC    EDATA    LONG      motor_pos
      00000173H   PUBLIC    EDATA    INT       gripper_timeout
      00000175H   PUBLIC    EDATA    INT       NUM
      00000177H   PUBLIC    EDATA    ---       err_spd
      0000017DH   PUBLIC    EDATA    INT       set_spd
      00000020H.0 PUBLIC    BIT      BIT       PRINT_flag
      00000020H.1 PUBLIC    BIT      BIT       EC11_R_flag
      00000020H.2 PUBLIC    BIT      BIT       gripper_auto_mode
      00000020H.3 PUBLIC    BIT      BIT       OLED_flag
      00000020H.4 PUBLIC    BIT      BIT       object_detected
      00000020H.5 PUBLIC    BIT      BIT       EC11_DIR
      00000020H.6 PUBLIC    BIT      BIT       S_flag
      000100C0H   PUBLIC    XDATA    ---       IRLED_dat
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      000000B0H.4 SFRSYM    DATA     BIT       P34
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      000000B0H.2 SFRSYM    DATA     BIT       P32
      00000098H   SFRSYM    DATA     BYTE      SCON
      00000090H.1 SFRSYM    DATA     BIT       P11
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000AFH.7 SFRSYM    DATA     BIT       EUSB
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 28


      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF48E6H   BLOCK     CODE     ---       LVL=0
      00FF48E6H   LINE      CODE     ---       #75
      00FF48E6H   LINE      CODE     ---       #77
      00FF48E9H   LINE      CODE     ---       #78
      00FF48F1H   LINE      CODE     ---       #80
      00FF48F4H   LINE      CODE     ---       #82
      00FF4902H   LINE      CODE     ---       #83
      00FF4913H   LINE      CODE     ---       #85
      00FF491EH   LINE      CODE     ---       #87
      00FF4926H   LINE      CODE     ---       #89
      00FF4933H   LINE      CODE     ---       #90
      00FF4935H   LINE      CODE     ---       #92
      00FF4938H   LINE      CODE     ---       #93
      00FF493AH   LINE      CODE     ---       #95
      00FF493AH   LINE      CODE     ---       #97
      00FF493DH   LINE      CODE     ---       #99
      00FF4940H   LINE      CODE     ---       #101
      00FF4943H   LINE      CODE     ---       #103
      00FF4946H   LINE      CODE     ---       #105
      00FF496BH   LINE      CODE     ---       #106
      00FF496DH   LINE      CODE     ---       #107
      00FF496DH   LINE      CODE     ---       #109
      00FF4970H   LINE      CODE     ---       #111
      00FF4973H   LINE      CODE     ---       #112
      00FF4975H   LINE      CODE     ---       #113
      00FF4975H   LINE      CODE     ---       #115
      00FF4978H   LINE      CODE     ---       #117
      00FF497AH   LINE      CODE     ---       #118
      00FF497AH   LINE      CODE     ---       #121
      00FF4988H   LINE      CODE     ---       #123
      00FF499AH   LINE      CODE     ---       #124
      00FF499EH   LINE      CODE     ---       #126
      00FF49AAH   LINE      CODE     ---       #128
      00FF49EAH   LINE      CODE     ---       #130
      00FF49F2H   LINE      CODE     ---       #131
      00FF49F6H   LINE      CODE     ---       #133
      00FF4A08H   LINE      CODE     ---       #134
      00FF4A1AH   LINE      CODE     ---       #136
      00FF4A21H   LINE      CODE     ---       #138
      00FF4A23H   LINE      CODE     ---       #139
      ---         BLOCKEND  ---      ---       LVL=0

      00FF70EDH   BLOCK     CODE     ---       LVL=0
      00FF70EDH   LINE      CODE     ---       #143
      00FF70EDH   LINE      CODE     ---       #145
      00FF70FBH   LINE      CODE     ---       #147
      00FF7104H   LINE      CODE     ---       #148
      00FF7107H   LINE      CODE     ---       #149
      00FF710DH   LINE      CODE     ---       #150
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 29


      00FF7111H   LINE      CODE     ---       #151
      00FF7111H   LINE      CODE     ---       #153
      00FF7115H   LINE      CODE     ---       #155
      00FF7118H   LINE      CODE     ---       #157
      00FF711BH   LINE      CODE     ---       #159
      00FF7121H   LINE      CODE     ---       #160
      00FF7123H   LINE      CODE     ---       #163
      00FF712DH   LINE      CODE     ---       #164
      00FF712DH   LINE      CODE     ---       #165
      00FF712FH   LINE      CODE     ---       #166
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6CC7H   BLOCK     CODE     ---       LVL=0
      WR6         REGSYM    ---      WORD      temp_dat
      R10         REGSYM    ---      BYTE      i
      00FF6CC7H   LINE      CODE     ---       #175
      00FF6CC7H   LINE      CODE     ---       #176
      00FF6CC7H   LINE      CODE     ---       #181
      00FF6CCCH   LINE      CODE     ---       #182
      00FF6CD0H   LINE      CODE     ---       #184
      00FF6CD2H   LINE      CODE     ---       #186
      00FF6CE3H   LINE      CODE     ---       #188
      00FF6CECH   LINE      CODE     ---       #189
      00FF6CF3H   LINE      CODE     ---       #193
      00FF6CFCH   LINE      CODE     ---       #195
      00FF6D06H   LINE      CODE     ---       #197
      00FF6D0CH   LINE      CODE     ---       #198
      00FF6D0DH   LINE      CODE     ---       #201
      00FF6D0FH   LINE      CODE     ---       #202
      00FF6D0FH   LINE      CODE     ---       #203
      00FF6D10H   LINE      CODE     ---       #206
      00FF6D16H   LINE      CODE     ---       #207
      00FF6D18H   LINE      CODE     ---       #208
      00FF6D18H   LINE      CODE     ---       #209
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0006H   BLOCK     CODE     ---       LVL=0
      WR6         REGSYM    ---      INT       speed
      00FF0006H   LINE      CODE     ---       #217
      00FF0006H   LINE      CODE     ---       #219
      00FF000AH   LINE      CODE     ---       #220
      ---         BLOCKEND  ---      ---       LVL=0

      00FF51B0H   BLOCK     CODE     ---       LVL=0
      00FF51B0H   LINE      CODE     ---       #228
      00FF51B0H   LINE      CODE     ---       #230
      00FF51B6H   LINE      CODE     ---       #232
      00FF51D7H   LINE      CODE     ---       #233
      00FF51D7H   LINE      CODE     ---       #234
      00FF51DCH   LINE      CODE     ---       #235
      00FF51E2H   LINE      CODE     ---       #236
      00FF51EAH   LINE      CODE     ---       #237
      00FF51F0H   LINE      CODE     ---       #238
      00FF51F0H   LINE      CODE     ---       #239
      00FF51F1H   LINE      CODE     ---       #241
      00FF51F1H   LINE      CODE     ---       #242
      00FF51FEH   LINE      CODE     ---       #243
      00FF5206H   LINE      CODE     ---       #244
      00FF520EH   LINE      CODE     ---       #245
      00FF520EH   LINE      CODE     ---       #246
      00FF5216H   LINE      CODE     ---       #247
      00FF5216H   LINE      CODE     ---       #248
      00FF5216H   LINE      CODE     ---       #249
      00FF5219H   LINE      CODE     ---       #251
      00FF5219H   LINE      CODE     ---       #252
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 30


      00FF5220H   LINE      CODE     ---       #253
      00FF522AH   LINE      CODE     ---       #256
      00FF5240H   LINE      CODE     ---       #257
      00FF5248H   LINE      CODE     ---       #258
      00FF5248H   LINE      CODE     ---       #259
      00FF5248H   LINE      CODE     ---       #260
      00FF524AH   LINE      CODE     ---       #262
      00FF524AH   LINE      CODE     ---       #263
      00FF5251H   LINE      CODE     ---       #264
      00FF525BH   LINE      CODE     ---       #267
      00FF5261H   LINE      CODE     ---       #268
      00FF5269H   LINE      CODE     ---       #269
      00FF5275H   LINE      CODE     ---       #270
      00FF5277H   LINE      CODE     ---       #271
      00FF5277H   LINE      CODE     ---       #272
      00FF5279H   LINE      CODE     ---       #274
      00FF5279H   LINE      CODE     ---       #275
      00FF5280H   LINE      CODE     ---       #276
      00FF528AH   LINE      CODE     ---       #279
      00FF52A0H   LINE      CODE     ---       #280
      00FF52A6H   LINE      CODE     ---       #281
      00FF52AAH   LINE      CODE     ---       #282
      00FF52AAH   LINE      CODE     ---       #283
      00FF52ABH   LINE      CODE     ---       #285
      00FF52ABH   LINE      CODE     ---       #286
      00FF52B1H   LINE      CODE     ---       #287
      00FF52B1H   LINE      CODE     ---       #288
      00FF52B1H   LINE      CODE     ---       #289
      ---         BLOCKEND  ---      ---       LVL=0

      00FF4EA3H   BLOCK     CODE     ---       LVL=0
      00FF4EA5H   BLOCK     CODE     NEAR LAB  LVL=1
      R15         REGSYM    ---      BYTE      n
      ---         BLOCKEND  ---      ---       LVL=1
      00FF4EA3H   LINE      CODE     ---       #291
      00FF4EA5H   LINE      CODE     ---       #292
      00FF4EA5H   LINE      CODE     ---       #295
      00FF4EB6H   LINE      CODE     ---       #296
      00FF4ECAH   LINE      CODE     ---       #298
      00FF4EDCH   LINE      CODE     ---       #299
      00FF4EF2H   LINE      CODE     ---       #301
      00FF4F04H   LINE      CODE     ---       #302
      00FF4F1AH   LINE      CODE     ---       #304
      00FF4F2CH   LINE      CODE     ---       #305
      00FF4F46H   LINE      CODE     ---       #307
      00FF4F58H   LINE      CODE     ---       #308
      00FF4F6EH   LINE      CODE     ---       #310
      00FF4F70H   LINE      CODE     ---       #312
      00FF4F8FH   LINE      CODE     ---       #313
      00FF4F9FH   LINE      CODE     ---       #314
      00FF4FA6H   LINE      CODE     ---       #315
      ---         BLOCKEND  ---      ---       LVL=0

      00FF8290H   BLOCK     CODE     ---       LVL=0
      00FF8290H   LINE      CODE     ---       #317
      00FF8290H   LINE      CODE     ---       #319
      00FF8292H   LINE      CODE     ---       #320
      00FF829BH   LINE      CODE     ---       #321
      ---         BLOCKEND  ---      ---       LVL=0

      00FF5EDBH   BLOCK     CODE     ---       LVL=0
      R3          REGSYM    ---      BYTE      n
      00FF5EDBH   LINE      CODE     ---       #323
      00FF5EDBH   LINE      CODE     ---       #324
      00FF5EDBH   LINE      CODE     ---       #326
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 31


      00FF5EDDH   LINE      CODE     ---       #328
      00FF5EF1H   LINE      CODE     ---       #330
      00FF5EFDH   LINE      CODE     ---       #331
      00FF5F0DH   LINE      CODE     ---       #333
      00FF5F66H   LINE      CODE     ---       #334
      00FF5F67H   LINE      CODE     ---       #335
      00FF5F71H   LINE      CODE     ---       #337
      ---         BLOCKEND  ---      ---       LVL=0

      00FF64E4H   BLOCK     CODE     ---       LVL=0
      00FF64E4H   LINE      CODE     ---       #339
      00FF64E4H   LINE      CODE     ---       #341
      00FF64FAH   LINE      CODE     ---       #343
      00FF6506H   LINE      CODE     ---       #345
      00FF6510H   LINE      CODE     ---       #346
      00FF651EH   LINE      CODE     ---       #347
      00FF651EH   LINE      CODE     ---       #349
      00FF652CH   LINE      CODE     ---       #351
      00FF653AH   LINE      CODE     ---       #353
      00FF6546H   LINE      CODE     ---       #356
      00FF6551H   LINE      CODE     ---       #358
      00FF655BH   LINE      CODE     ---       #359
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7A5CH   BLOCK     CODE     ---       LVL=0
      DR0         REGSYM    ---      LONG      PWM
      00FF7A60H   BLOCK     CODE     NEAR LAB  LVL=1
      DR12        REGSYM    ---      DWORD     abs_pwm
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7A5CH   LINE      CODE     ---       #368
      00FF7A60H   LINE      CODE     ---       #369
      00FF7A60H   LINE      CODE     ---       #372
      00FF7A66H   LINE      CODE     ---       #374
      00FF7A68H   LINE      CODE     ---       #375
      00FF7A6AH   LINE      CODE     ---       #376
      00FF7A6CH   LINE      CODE     ---       #379
      00FF7A6EH   LINE      CODE     ---       #380
      00FF7A72H   LINE      CODE     ---       #381
      00FF7A72H   LINE      CODE     ---       #383
      00FF7A79H   LINE      CODE     ---       #384
      ---         BLOCKEND  ---      ---       LVL=0

      00FF001AH   BLOCK     CODE     ---       LVL=0
      00FF001AH   LINE      CODE     ---       #392
      00FF001AH   LINE      CODE     ---       #396
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       isr
      00FF0021H   PUBLIC    CODE     ---       IIC_I
      00FF0022H   PUBLIC    CODE     ---       CMP_I
      00FF0029H   PUBLIC    CODE     ---       LVD_I
      00FF002AH   PUBLIC    CODE     ---       SPI_I
      00FF7FECH   PUBLIC    CODE     ---       UART1_I
      00FF7C5CH   PUBLIC    CODE     ---       UART2_I
      00FF7C78H   PUBLIC    CODE     ---       UART3_I
      00FF7C94H   PUBLIC    CODE     ---       UART4_I
      00FF7130H   PUBLIC    CODE     ---       DMA_UART1_RXD_isr
      00FF7172H   PUBLIC    CODE     ---       DMA_UART2_RXD_isr
      00FF71B4H   PUBLIC    CODE     ---       DMA_UART3_RXD_isr
      00FF0066H   PUBLIC    CODE     ---       DMA_UART1_TXD_isr
      00FF71F6H   PUBLIC    CODE     ---       DMA_UART4_RXD_isr
      00FF7BE8H   PUBLIC    CODE     ---       DMA_UART2_TXD_isr
      00FF7C05H   PUBLIC    CODE     ---       DMA_UART3_TXD_isr
      00FF7C22H   PUBLIC    CODE     ---       DMA_UART4_TXD_isr
      00FF0031H   PUBLIC    CODE     ---       DMA_ADC_isr
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 32


      00FF67F6H   PUBLIC    CODE     ---       CAN1_I
      00FF6861H   PUBLIC    CODE     ---       CAN2_I
      00FF0032H   PUBLIC    CODE     ---       DMA_M2M_isr
      00FF7ABCH   PUBLIC    CODE     ---       PIN0_I
      00FF7ADAH   PUBLIC    CODE     ---       PIN1_I
      00FF7AF8H   PUBLIC    CODE     ---       PIN2_I
      00FF7B16H   PUBLIC    CODE     ---       PIN3_I
      00FF7B34H   PUBLIC    CODE     ---       PIN4_I
      00FF0049H   PUBLIC    CODE     ---       INT0_I
      00FF7B52H   PUBLIC    CODE     ---       PIN5_I
      00FF004AH   PUBLIC    CODE     ---       INT1_I
      00FF7B70H   PUBLIC    CODE     ---       PIN6_I
      00FF7674H   PUBLIC    CODE     ---       INT2_I
      00FF7B8EH   PUBLIC    CODE     ---       PIN7_I
      00FF004EH   PUBLIC    CODE     ---       INT3_I
      00FF004FH   PUBLIC    CODE     ---       INT4_I
      00FF0050H   PUBLIC    CODE     ---       DMA_SPI_isr
      00FF74D5H   PUBLIC    CODE     ---       TIMER0_I
      00FF0051H   PUBLIC    CODE     ---       TIMER1_I
      00FF0052H   PUBLIC    CODE     ---       TIMER2_I
      00FF0056H   PUBLIC    CODE     ---       PWMA_I
      00FF0057H   PUBLIC    CODE     ---       TIMER3_I
      00FF0058H   PUBLIC    CODE     ---       PWMB_I
      00FF0059H   PUBLIC    CODE     ---       TIMER4_I
      00FF005AH   PUBLIC    CODE     ---       ADC_I
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000097H.3 SFRSYM    DATA     BIT       CANSEL
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000FDH.1 SFRSYM    DATA     BIT       S4TI
      000000ACH.1 SFRSYM    DATA     BIT       S3TI
      0000009AH.1 SFRSYM    DATA     BIT       S2TI
      000000FDH.0 SFRSYM    DATA     BIT       S4RI
      000000ACH.0 SFRSYM    DATA     BIT       S3RI
      0000009AH.0 SFRSYM    DATA     BIT       S2RI
      00000098H.1 SFRSYM    DATA     BIT       TI
      00000098H.0 SFRSYM    DATA     BIT       RI
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 33


      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF0049H   BLOCK     CODE     ---       LVL=0
      00FF0049H   LINE      CODE     ---       #4
      00FF0049H   LINE      CODE     ---       #7
      ---         BLOCKEND  ---      ---       LVL=0

      00FF74D5H   BLOCK     CODE     ---       LVL=0
      00FF74D5H   LINE      CODE     ---       #8
      00FF74EBH   LINE      CODE     ---       #10
      00FF74EEH   LINE      CODE     ---       #11
      00FF74F1H   LINE      CODE     ---       #12
      00FF74F4H   LINE      CODE     ---       #13
      ---         BLOCKEND  ---      ---       LVL=0

      00FF004AH   BLOCK     CODE     ---       LVL=0
      00FF004AH   LINE      CODE     ---       #14
      00FF004AH   LINE      CODE     ---       #17
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0051H   BLOCK     CODE     ---       LVL=0
      00FF0051H   LINE      CODE     ---       #18
      00FF0051H   LINE      CODE     ---       #21
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7FECH   BLOCK     CODE     ---       LVL=0
      00FF7FECH   LINE      CODE     ---       #22
      00FF7FF0H   LINE      CODE     ---       #24
      00FF7FF3H   LINE      CODE     ---       #26
      00FF7FF5H   LINE      CODE     ---       #27
      00FF7FFAH   LINE      CODE     ---       #28
      00FF7FFAH   LINE      CODE     ---       #29
      00FF7FFDH   LINE      CODE     ---       #32
      00FF7FFFH   LINE      CODE     ---       #33
      ---         BLOCKEND  ---      ---       LVL=0

      00FF005AH   BLOCK     CODE     ---       LVL=0
      00FF005AH   LINE      CODE     ---       #36
      00FF005AH   LINE      CODE     ---       #39
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0029H   BLOCK     CODE     ---       LVL=0
      00FF0029H   LINE      CODE     ---       #40
      00FF0029H   LINE      CODE     ---       #43
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7C5CH   BLOCK     CODE     ---       LVL=0
      00FF7C5CH   LINE      CODE     ---       #44
      00FF7C60H   LINE      CODE     ---       #46
      00FF7C64H   LINE      CODE     ---       #48
      00FF7C67H   LINE      CODE     ---       #49
      00FF7C6CH   LINE      CODE     ---       #50
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 34


      00FF7C6CH   LINE      CODE     ---       #51
      00FF7C70H   LINE      CODE     ---       #54
      00FF7C73H   LINE      CODE     ---       #55
      ---         BLOCKEND  ---      ---       LVL=0

      00FF002AH   BLOCK     CODE     ---       LVL=0
      00FF002AH   LINE      CODE     ---       #57
      00FF002AH   LINE      CODE     ---       #60
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7674H   BLOCK     CODE     ---       LVL=0
      00FF7674H   LINE      CODE     ---       #61
      00FF768AH   LINE      CODE     ---       #63
      00FF768DH   LINE      CODE     ---       #64
      ---         BLOCKEND  ---      ---       LVL=0

      00FF004EH   BLOCK     CODE     ---       LVL=0
      00FF004EH   LINE      CODE     ---       #65
      00FF004EH   LINE      CODE     ---       #68
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0052H   BLOCK     CODE     ---       LVL=0
      00FF0052H   LINE      CODE     ---       #69
      00FF0052H   LINE      CODE     ---       #72
      ---         BLOCKEND  ---      ---       LVL=0

      00FF004FH   BLOCK     CODE     ---       LVL=0
      00FF004FH   LINE      CODE     ---       #73
      00FF004FH   LINE      CODE     ---       #76
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7C78H   BLOCK     CODE     ---       LVL=0
      00FF7C78H   LINE      CODE     ---       #77
      00FF7C7CH   LINE      CODE     ---       #79
      00FF7C80H   LINE      CODE     ---       #81
      00FF7C83H   LINE      CODE     ---       #82
      00FF7C88H   LINE      CODE     ---       #83
      00FF7C88H   LINE      CODE     ---       #84
      00FF7C8CH   LINE      CODE     ---       #87
      00FF7C8FH   LINE      CODE     ---       #88
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7C94H   BLOCK     CODE     ---       LVL=0
      00FF7C94H   LINE      CODE     ---       #90
      00FF7C98H   LINE      CODE     ---       #92
      00FF7C9CH   LINE      CODE     ---       #94
      00FF7C9FH   LINE      CODE     ---       #95
      00FF7CA4H   LINE      CODE     ---       #96
      00FF7CA4H   LINE      CODE     ---       #97
      00FF7CA8H   LINE      CODE     ---       #100
      00FF7CABH   LINE      CODE     ---       #101
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0057H   BLOCK     CODE     ---       LVL=0
      00FF0057H   LINE      CODE     ---       #103
      00FF0057H   LINE      CODE     ---       #106
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0059H   BLOCK     CODE     ---       LVL=0
      00FF0059H   LINE      CODE     ---       #107
      00FF0059H   LINE      CODE     ---       #110
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0022H   BLOCK     CODE     ---       LVL=0
      00FF0022H   LINE      CODE     ---       #111
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 35


      00FF0022H   LINE      CODE     ---       #114
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0021H   BLOCK     CODE     ---       LVL=0
      00FF0021H   LINE      CODE     ---       #115
      00FF0021H   LINE      CODE     ---       #118
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0056H   BLOCK     CODE     ---       LVL=0
      00FF0056H   LINE      CODE     ---       #123
      00FF0056H   LINE      CODE     ---       #126
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0058H   BLOCK     CODE     ---       LVL=0
      00FF0058H   LINE      CODE     ---       #127
      00FF0058H   LINE      CODE     ---       #130
      ---         BLOCKEND  ---      ---       LVL=0

      00FF67F6H   BLOCK     CODE     ---       LVL=0
      00FF680CH   BLOCK     CODE     NEAR LAB  LVL=1
      R10         REGSYM    ---      BYTE      isr
      ---         BLOCKEND  ---      ---       LVL=1
      00FF67F6H   LINE      CODE     ---       #132
      00FF680CH   LINE      CODE     ---       #133
      00FF680CH   LINE      CODE     ---       #136
      00FF680FH   LINE      CODE     ---       #137
      00FF6816H   LINE      CODE     ---       #138
      00FF6823H   LINE      CODE     ---       #139
      00FF682EH   LINE      CODE     ---       #141
      00FF6835H   LINE      CODE     ---       #143
      00FF6837H   LINE      CODE     ---       #144
      00FF6837H   LINE      CODE     ---       #145
      00FF6837H   LINE      CODE     ---       #148
      00FF6837H   LINE      CODE     ---       #149
      00FF6837H   LINE      CODE     ---       #152
      00FF6837H   LINE      CODE     ---       #153
      00FF683EH   LINE      CODE     ---       #155
      00FF6842H   LINE      CODE     ---       #156
      00FF684AH   LINE      CODE     ---       #157
      00FF684AH   LINE      CODE     ---       #158
      00FF684AH   LINE      CODE     ---       #161
      00FF684AH   LINE      CODE     ---       #162
      00FF684AH   LINE      CODE     ---       #165
      00FF684AH   LINE      CODE     ---       #166
      00FF684AH   LINE      CODE     ---       #169
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6861H   BLOCK     CODE     ---       LVL=0
      00FF6877H   BLOCK     CODE     NEAR LAB  LVL=1
      R10         REGSYM    ---      BYTE      isr
      ---         BLOCKEND  ---      ---       LVL=1
      00FF6861H   LINE      CODE     ---       #172
      00FF6877H   LINE      CODE     ---       #173
      00FF6877H   LINE      CODE     ---       #176
      00FF687AH   LINE      CODE     ---       #177
      00FF6881H   LINE      CODE     ---       #178
      00FF688EH   LINE      CODE     ---       #179
      00FF6899H   LINE      CODE     ---       #181
      00FF68A0H   LINE      CODE     ---       #183
      00FF68A2H   LINE      CODE     ---       #184
      00FF68A2H   LINE      CODE     ---       #185
      00FF68A2H   LINE      CODE     ---       #188
      00FF68A2H   LINE      CODE     ---       #189
      00FF68A2H   LINE      CODE     ---       #192
      00FF68A2H   LINE      CODE     ---       #193
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 36


      00FF68A9H   LINE      CODE     ---       #195
      00FF68ADH   LINE      CODE     ---       #196
      00FF68B5H   LINE      CODE     ---       #197
      00FF68B5H   LINE      CODE     ---       #198
      00FF68B5H   LINE      CODE     ---       #201
      00FF68B5H   LINE      CODE     ---       #202
      00FF68B5H   LINE      CODE     ---       #205
      00FF68B5H   LINE      CODE     ---       #206
      00FF68B5H   LINE      CODE     ---       #209
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7ABCH   BLOCK     CODE     ---       LVL=0
      00FF7AC2H   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      intf
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7ABCH   LINE      CODE     ---       #212
      00FF7AC2H   LINE      CODE     ---       #213
      00FF7AC2H   LINE      CODE     ---       #215
      00FF7ACDH   LINE      CODE     ---       #216
      00FF7ACFH   LINE      CODE     ---       #218
      00FF7AD3H   LINE      CODE     ---       #219
      00FF7AD3H   LINE      CODE     ---       #222
      00FF7AD3H   LINE      CODE     ---       #223
      00FF7AD3H   LINE      CODE     ---       #226
      00FF7AD3H   LINE      CODE     ---       #227
      00FF7AD3H   LINE      CODE     ---       #230
      00FF7AD3H   LINE      CODE     ---       #231
      00FF7AD3H   LINE      CODE     ---       #234
      00FF7AD3H   LINE      CODE     ---       #235
      00FF7AD3H   LINE      CODE     ---       #238
      00FF7AD3H   LINE      CODE     ---       #239
      00FF7AD3H   LINE      CODE     ---       #242
      00FF7AD3H   LINE      CODE     ---       #243
      00FF7AD3H   LINE      CODE     ---       #246
      00FF7AD3H   LINE      CODE     ---       #247
      00FF7AD3H   LINE      CODE     ---       #250
      00FF7AD3H   LINE      CODE     ---       #252
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7ADAH   BLOCK     CODE     ---       LVL=0
      00FF7AE0H   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      intf
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7ADAH   LINE      CODE     ---       #253
      00FF7AE0H   LINE      CODE     ---       #254
      00FF7AE0H   LINE      CODE     ---       #256
      00FF7AEBH   LINE      CODE     ---       #257
      00FF7AEDH   LINE      CODE     ---       #259
      00FF7AF1H   LINE      CODE     ---       #260
      00FF7AF1H   LINE      CODE     ---       #263
      00FF7AF1H   LINE      CODE     ---       #264
      00FF7AF1H   LINE      CODE     ---       #267
      00FF7AF1H   LINE      CODE     ---       #268
      00FF7AF1H   LINE      CODE     ---       #271
      00FF7AF1H   LINE      CODE     ---       #272
      00FF7AF1H   LINE      CODE     ---       #275
      00FF7AF1H   LINE      CODE     ---       #276
      00FF7AF1H   LINE      CODE     ---       #279
      00FF7AF1H   LINE      CODE     ---       #280
      00FF7AF1H   LINE      CODE     ---       #283
      00FF7AF1H   LINE      CODE     ---       #284
      00FF7AF1H   LINE      CODE     ---       #287
      00FF7AF1H   LINE      CODE     ---       #288
      00FF7AF1H   LINE      CODE     ---       #291
      00FF7AF1H   LINE      CODE     ---       #293
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 37


      ---         BLOCKEND  ---      ---       LVL=0

      00FF7AF8H   BLOCK     CODE     ---       LVL=0
      00FF7AFEH   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      intf
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7AF8H   LINE      CODE     ---       #294
      00FF7AFEH   LINE      CODE     ---       #295
      00FF7AFEH   LINE      CODE     ---       #297
      00FF7B09H   LINE      CODE     ---       #298
      00FF7B0BH   LINE      CODE     ---       #300
      00FF7B0FH   LINE      CODE     ---       #301
      00FF7B0FH   LINE      CODE     ---       #304
      00FF7B0FH   LINE      CODE     ---       #305
      00FF7B0FH   LINE      CODE     ---       #308
      00FF7B0FH   LINE      CODE     ---       #309
      00FF7B0FH   LINE      CODE     ---       #312
      00FF7B0FH   LINE      CODE     ---       #313
      00FF7B0FH   LINE      CODE     ---       #316
      00FF7B0FH   LINE      CODE     ---       #317
      00FF7B0FH   LINE      CODE     ---       #320
      00FF7B0FH   LINE      CODE     ---       #321
      00FF7B0FH   LINE      CODE     ---       #324
      00FF7B0FH   LINE      CODE     ---       #325
      00FF7B0FH   LINE      CODE     ---       #328
      00FF7B0FH   LINE      CODE     ---       #329
      00FF7B0FH   LINE      CODE     ---       #332
      00FF7B0FH   LINE      CODE     ---       #334
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7B16H   BLOCK     CODE     ---       LVL=0
      00FF7B1CH   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      intf
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7B16H   LINE      CODE     ---       #335
      00FF7B1CH   LINE      CODE     ---       #336
      00FF7B1CH   LINE      CODE     ---       #338
      00FF7B27H   LINE      CODE     ---       #339
      00FF7B29H   LINE      CODE     ---       #341
      00FF7B2DH   LINE      CODE     ---       #342
      00FF7B2DH   LINE      CODE     ---       #345
      00FF7B2DH   LINE      CODE     ---       #346
      00FF7B2DH   LINE      CODE     ---       #349
      00FF7B2DH   LINE      CODE     ---       #350
      00FF7B2DH   LINE      CODE     ---       #353
      00FF7B2DH   LINE      CODE     ---       #354
      00FF7B2DH   LINE      CODE     ---       #357
      00FF7B2DH   LINE      CODE     ---       #358
      00FF7B2DH   LINE      CODE     ---       #361
      00FF7B2DH   LINE      CODE     ---       #362
      00FF7B2DH   LINE      CODE     ---       #365
      00FF7B2DH   LINE      CODE     ---       #366
      00FF7B2DH   LINE      CODE     ---       #369
      00FF7B2DH   LINE      CODE     ---       #370
      00FF7B2DH   LINE      CODE     ---       #373
      00FF7B2DH   LINE      CODE     ---       #375
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7B34H   BLOCK     CODE     ---       LVL=0
      00FF7B3AH   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      intf
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7B34H   LINE      CODE     ---       #376
      00FF7B3AH   LINE      CODE     ---       #377
      00FF7B3AH   LINE      CODE     ---       #379
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 38


      00FF7B45H   LINE      CODE     ---       #380
      00FF7B47H   LINE      CODE     ---       #382
      00FF7B4BH   LINE      CODE     ---       #383
      00FF7B4BH   LINE      CODE     ---       #386
      00FF7B4BH   LINE      CODE     ---       #387
      00FF7B4BH   LINE      CODE     ---       #390
      00FF7B4BH   LINE      CODE     ---       #391
      00FF7B4BH   LINE      CODE     ---       #394
      00FF7B4BH   LINE      CODE     ---       #395
      00FF7B4BH   LINE      CODE     ---       #398
      00FF7B4BH   LINE      CODE     ---       #399
      00FF7B4BH   LINE      CODE     ---       #402
      00FF7B4BH   LINE      CODE     ---       #403
      00FF7B4BH   LINE      CODE     ---       #406
      00FF7B4BH   LINE      CODE     ---       #407
      00FF7B4BH   LINE      CODE     ---       #410
      00FF7B4BH   LINE      CODE     ---       #411
      00FF7B4BH   LINE      CODE     ---       #414
      00FF7B4BH   LINE      CODE     ---       #416
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7B52H   BLOCK     CODE     ---       LVL=0
      00FF7B58H   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      intf
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7B52H   LINE      CODE     ---       #417
      00FF7B58H   LINE      CODE     ---       #418
      00FF7B58H   LINE      CODE     ---       #420
      00FF7B63H   LINE      CODE     ---       #421
      00FF7B65H   LINE      CODE     ---       #423
      00FF7B69H   LINE      CODE     ---       #424
      00FF7B69H   LINE      CODE     ---       #427
      00FF7B69H   LINE      CODE     ---       #428
      00FF7B69H   LINE      CODE     ---       #431
      00FF7B69H   LINE      CODE     ---       #432
      00FF7B69H   LINE      CODE     ---       #435
      00FF7B69H   LINE      CODE     ---       #436
      00FF7B69H   LINE      CODE     ---       #439
      00FF7B69H   LINE      CODE     ---       #440
      00FF7B69H   LINE      CODE     ---       #443
      00FF7B69H   LINE      CODE     ---       #444
      00FF7B69H   LINE      CODE     ---       #447
      00FF7B69H   LINE      CODE     ---       #449
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7B70H   BLOCK     CODE     ---       LVL=0
      00FF7B76H   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      intf
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7B70H   LINE      CODE     ---       #450
      00FF7B76H   LINE      CODE     ---       #451
      00FF7B76H   LINE      CODE     ---       #453
      00FF7B81H   LINE      CODE     ---       #454
      00FF7B83H   LINE      CODE     ---       #456
      00FF7B87H   LINE      CODE     ---       #457
      00FF7B87H   LINE      CODE     ---       #460
      00FF7B87H   LINE      CODE     ---       #461
      00FF7B87H   LINE      CODE     ---       #464
      00FF7B87H   LINE      CODE     ---       #465
      00FF7B87H   LINE      CODE     ---       #468
      00FF7B87H   LINE      CODE     ---       #469
      00FF7B87H   LINE      CODE     ---       #472
      00FF7B87H   LINE      CODE     ---       #473
      00FF7B87H   LINE      CODE     ---       #476
      00FF7B87H   LINE      CODE     ---       #477
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 39


      00FF7B87H   LINE      CODE     ---       #480
      00FF7B87H   LINE      CODE     ---       #481
      00FF7B87H   LINE      CODE     ---       #484
      00FF7B87H   LINE      CODE     ---       #485
      00FF7B87H   LINE      CODE     ---       #488
      00FF7B87H   LINE      CODE     ---       #490
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7B8EH   BLOCK     CODE     ---       LVL=0
      00FF7B94H   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      intf
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7B8EH   LINE      CODE     ---       #491
      00FF7B94H   LINE      CODE     ---       #492
      00FF7B94H   LINE      CODE     ---       #494
      00FF7B9FH   LINE      CODE     ---       #495
      00FF7BA1H   LINE      CODE     ---       #497
      00FF7BA5H   LINE      CODE     ---       #498
      00FF7BA5H   LINE      CODE     ---       #501
      00FF7BA5H   LINE      CODE     ---       #502
      00FF7BA5H   LINE      CODE     ---       #505
      00FF7BA5H   LINE      CODE     ---       #506
      00FF7BA5H   LINE      CODE     ---       #509
      00FF7BA5H   LINE      CODE     ---       #510
      00FF7BA5H   LINE      CODE     ---       #513
      00FF7BA5H   LINE      CODE     ---       #514
      00FF7BA5H   LINE      CODE     ---       #517
      00FF7BA5H   LINE      CODE     ---       #518
      00FF7BA5H   LINE      CODE     ---       #521
      00FF7BA5H   LINE      CODE     ---       #522
      00FF7BA5H   LINE      CODE     ---       #525
      00FF7BA5H   LINE      CODE     ---       #526
      00FF7BA5H   LINE      CODE     ---       #529
      00FF7BA5H   LINE      CODE     ---       #531
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0032H   BLOCK     CODE     ---       LVL=0
      00FF0032H   LINE      CODE     ---       #535
      00FF0032H   LINE      CODE     ---       #538
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0031H   BLOCK     CODE     ---       LVL=0
      00FF0031H   LINE      CODE     ---       #540
      00FF0031H   LINE      CODE     ---       #543
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0050H   BLOCK     CODE     ---       LVL=0
      00FF0050H   LINE      CODE     ---       #545
      00FF0050H   LINE      CODE     ---       #548
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0066H   BLOCK     CODE     ---       LVL=0
      00FF0066H   LINE      CODE     ---       #550
      00FF006CH   LINE      CODE     ---       #552
      00FF007CH   LINE      CODE     ---       #553
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7130H   BLOCK     CODE     ---       LVL=0
      00FF7130H   LINE      CODE     ---       #554
      00FF7138H   LINE      CODE     ---       #556
      00FF7146H   LINE      CODE     ---       #558
      00FF714EH   LINE      CODE     ---       #560
      00FF715BH   LINE      CODE     ---       #561
      00FF715BH   LINE      CODE     ---       #562
      00FF7161H   LINE      CODE     ---       #564
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 40


      00FF7169H   LINE      CODE     ---       #565
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7BE8H   BLOCK     CODE     ---       LVL=0
      00FF7BE8H   LINE      CODE     ---       #568
      00FF7BEEH   LINE      CODE     ---       #570
      00FF7BFEH   LINE      CODE     ---       #571
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7172H   BLOCK     CODE     ---       LVL=0
      00FF7172H   LINE      CODE     ---       #572
      00FF717AH   LINE      CODE     ---       #574
      00FF7188H   LINE      CODE     ---       #576
      00FF7190H   LINE      CODE     ---       #578
      00FF719DH   LINE      CODE     ---       #579
      00FF719DH   LINE      CODE     ---       #580
      00FF71A3H   LINE      CODE     ---       #582
      00FF71ABH   LINE      CODE     ---       #583
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7C05H   BLOCK     CODE     ---       LVL=0
      00FF7C05H   LINE      CODE     ---       #585
      00FF7C0BH   LINE      CODE     ---       #587
      00FF7C1BH   LINE      CODE     ---       #588
      ---         BLOCKEND  ---      ---       LVL=0

      00FF71B4H   BLOCK     CODE     ---       LVL=0
      00FF71B4H   LINE      CODE     ---       #589
      00FF71BCH   LINE      CODE     ---       #591
      00FF71CAH   LINE      CODE     ---       #593
      00FF71D2H   LINE      CODE     ---       #595
      00FF71DFH   LINE      CODE     ---       #596
      00FF71DFH   LINE      CODE     ---       #597
      00FF71E5H   LINE      CODE     ---       #599
      00FF71EDH   LINE      CODE     ---       #600
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7C22H   BLOCK     CODE     ---       LVL=0
      00FF7C22H   LINE      CODE     ---       #602
      00FF7C28H   LINE      CODE     ---       #604
      00FF7C38H   LINE      CODE     ---       #605
      ---         BLOCKEND  ---      ---       LVL=0

      00FF71F6H   BLOCK     CODE     ---       LVL=0
      00FF71F6H   LINE      CODE     ---       #606
      00FF71FEH   LINE      CODE     ---       #608
      00FF720CH   LINE      CODE     ---       #610
      00FF7214H   LINE      CODE     ---       #612
      00FF7221H   LINE      CODE     ---       #613
      00FF7221H   LINE      CODE     ---       #614
      00FF7227H   LINE      CODE     ---       #616
      00FF722FH   LINE      CODE     ---       #617
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       config
      00FF001EH   PUBLIC    CODE     ---       HID_isr
      00FF65CFH   PUBLIC    CODE     ---       System_init
      00FF822CH   PUBLIC    CODE     ---       fmax
      00FF804CH   PUBLIC    CODE     ---       Limit_int
      00FF72F9H   PUBLIC    CODE     ---       Key_Rst
      00FF82B3H   PUBLIC    CODE     ---       Usb_Rst
      00FF8063H   PUBLIC    CODE     ---       HEX2BCD
      00FF7D20H   PUBLIC    CODE     ---       BCD2HEX
      00FF000EH   PUBLIC    CODE     ---       sq
      00FF4B66H   PUBLIC    CODE     ---       set_clk
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 41


      00FF79D8H   PUBLIC    CODE     ---       Limit_float
      00000021H.2 PUBLIC    BIT      BIT       Key_Flag
      00000021H.3 PUBLIC    BIT      BIT       USB_flag
      000001D7H   PUBLIC    EDATA    ---       float_uchar
      000001DBH   PUBLIC    EDATA    ---       char_uchar
      000001DCH   PUBLIC    EDATA    ---       long_uchar
      000001E0H   PUBLIC    EDATA    ---       int_uchar
      000001E2H   PUBLIC    EDATA    ---       USER_STCISPCMD
      000001E6H   PUBLIC    EDATA    WORD      Key_cnt
      000001E8H   PUBLIC    EDATA    ---       USER_PRODUCTDESC
      000001ECH   PUBLIC    EDATA    ---       USER_DEVICEDESC
      000001F0H   PUBLIC    EDATA    DWORD     sys_clk
      000002B3H   PUBLIC    EDATA    BYTE      ?Limit_int?BYTE
      000002B7H   PUBLIC    EDATA    BYTE      ?Limit_float?BYTE
      00000094H   SFRSYM    DATA     BYTE      P0M0
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000BAH.7 SFRSYM    DATA     BIT       EAXFR
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EAH   SFRSYM    DATA     BYTE      CKCON
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000A6H   SFRSYM    DATA     BYTE      VRTRIM
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      0000009FH   SFRSYM    DATA     BYTE      IRTRIM
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000E9H   SFRSYM    DATA     BYTE      WTST
      000000B0H.2 SFRSYM    DATA     BIT       Reset_PIN
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000DCH   SFRSYM    DATA     BYTE      USBCLK
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      000000B5H.7 SFRSYM    DATA     BIT       PUSB
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000AFH.7 SFRSYM    DATA     BIT       EUSB
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E1H   SFRSYM    DATA     BYTE      P7M1
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 42


      000000CBH   SFRSYM    DATA     BYTE      P6M1
      000000E2H   SFRSYM    DATA     BYTE      P7M0
      000000B6H.7 SFRSYM    DATA     BIT       PUSBH
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000C9H   SFRSYM    DATA     BYTE      P5M1
      000000CCH   SFRSYM    DATA     BYTE      P6M0
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000B3H   SFRSYM    DATA     BYTE      P4M1
      000000CAH   SFRSYM    DATA     BYTE      P5M0
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B1H   SFRSYM    DATA     BYTE      P3M1
      000000B4H   SFRSYM    DATA     BYTE      P4M0
      00000090H   SFRSYM    DATA     BYTE      P1
      00000095H   SFRSYM    DATA     BYTE      P2M1
      000000B2H   SFRSYM    DATA     BYTE      P3M0
      00000080H   SFRSYM    DATA     BYTE      P0
      00000091H   SFRSYM    DATA     BYTE      P1M1
      00000096H   SFRSYM    DATA     BYTE      P2M0
      00000092H   SFRSYM    DATA     BYTE      P1M0
      00000093H   SFRSYM    DATA     BYTE      P0M1

      00FF65CFH   BLOCK     CODE     ---       LVL=0
      00FF65CFH   LINE      CODE     ---       #31
      00FF65CFH   LINE      CODE     ---       #36
      00FF65D2H   LINE      CODE     ---       #37
      00FF65D5H   LINE      CODE     ---       #38
      00FF65D8H   LINE      CODE     ---       #40
      00FF65DEH   LINE      CODE     ---       #41
      00FF65E4H   LINE      CODE     ---       #42
      00FF65EAH   LINE      CODE     ---       #43
      00FF65F0H   LINE      CODE     ---       #44
      00FF65F6H   LINE      CODE     ---       #45
      00FF65FCH   LINE      CODE     ---       #46
      00FF6602H   LINE      CODE     ---       #47
      00FF6608H   LINE      CODE     ---       #50
      00FF660BH   LINE      CODE     ---       #51
      00FF660EH   LINE      CODE     ---       #52
      00FF6611H   LINE      CODE     ---       #53
      00FF661EH   LINE      CODE     ---       #54
      00FF662CH   LINE      CODE     ---       #55
      00FF662FH   LINE      CODE     ---       #56
      00FF6632H   LINE      CODE     ---       #57
      00FF6635H   LINE      CODE     ---       #60
      00FF6638H   LINE      CODE     ---       #61
      00FF663BH   LINE      CODE     ---       #62
      00FF663EH   LINE      CODE     ---       #63
      ---         BLOCKEND  ---      ---       LVL=0

      00FF001EH   BLOCK     CODE     ---       LVL=0
      00FF001EH   LINE      CODE     ---       #65
      00FF001EH   LINE      CODE     ---       #67
      00FF0020H   LINE      CODE     ---       #68
      ---         BLOCKEND  ---      ---       LVL=0

      00FF82B3H   BLOCK     CODE     ---       LVL=0
      00FF82B3H   LINE      CODE     ---       #70
      00FF82B3H   LINE      CODE     ---       #72
      00FF82B6H   LINE      CODE     ---       #74
      00FF82B9H   LINE      CODE     ---       #76
      00FF82BCH   LINE      CODE     ---       #77
      ---         BLOCKEND  ---      ---       LVL=0

      00FF72F9H   BLOCK     CODE     ---       LVL=0
      00FF72F9H   LINE      CODE     ---       #80
      00FF72F9H   LINE      CODE     ---       #82
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 43


      00FF72FCH   LINE      CODE     ---       #84
      00FF72FFH   LINE      CODE     ---       #86
      00FF7309H   LINE      CODE     ---       #87
      00FF730FH   LINE      CODE     ---       #89
      00FF7311H   LINE      CODE     ---       #91
      00FF7314H   LINE      CODE     ---       #92
      00FF7317H   LINE      CODE     ---       #93
      00FF7323H   LINE      CODE     ---       #95
      00FF732AH   LINE      CODE     ---       #96
      00FF732DH   LINE      CODE     ---       #97
      00FF732FH   LINE      CODE     ---       #103
      00FF7335H   LINE      CODE     ---       #104
      00FF7337H   LINE      CODE     ---       #105
      00FF7337H   LINE      CODE     ---       #106
      ---         BLOCKEND  ---      ---       LVL=0

      00FF822CH   BLOCK     CODE     ---       LVL=0
      DR24        REGSYM    ---      FLOAT     a
      DR28        REGSYM    ---      FLOAT     b
      00FF822CH   LINE      CODE     ---       #109
      00FF8230H   LINE      CODE     ---       #110
      00FF823AH   LINE      CODE     ---       #111
      ---         BLOCKEND  ---      ---       LVL=0

      00FF804CH   BLOCK     CODE     ---       LVL=0
      DR28        REGSYM    ---      LONG      min
      DR8         REGSYM    ---      LONG      num
      000002BBH   SYMBOL    EDATA    LONG      max
      00FF804CH   LINE      CODE     ---       #113
      00FF8050H   LINE      CODE     ---       #115
      00FF805AH   LINE      CODE     ---       #116
      00FF8060H   LINE      CODE     ---       #117
      00FF8062H   LINE      CODE     ---       #118
      ---         BLOCKEND  ---      ---       LVL=0

      00FF79D8H   BLOCK     CODE     ---       LVL=0
      DR24        REGSYM    ---      FLOAT     min
      DR28        REGSYM    ---      FLOAT     num
      000002BFH   SYMBOL    EDATA    FLOAT     max
      00FF79D8H   LINE      CODE     ---       #120
      00FF79DCH   LINE      CODE     ---       #122
      00FF79EBH   LINE      CODE     ---       #123
      00FF79F6H   LINE      CODE     ---       #124
      00FF79F8H   LINE      CODE     ---       #125
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7D20H   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      val
      R11         REGSYM    ---      BYTE      L
      00FF7D20H   LINE      CODE     ---       #127
      00FF7D20H   LINE      CODE     ---       #128
      00FF7D20H   LINE      CODE     ---       #130
      00FF7D3AH   LINE      CODE     ---       #131
      00FF7D3AH   LINE      CODE     ---       #132
      ---         BLOCKEND  ---      ---       LVL=0

      00FF8063H   BLOCK     CODE     ---       LVL=0
      R7          REGSYM    ---      BYTE      val
      R11         REGSYM    ---      BYTE      L
      00FF8063H   LINE      CODE     ---       #134
      00FF8063H   LINE      CODE     ---       #135
      00FF8063H   LINE      CODE     ---       #137
      00FF8079H   LINE      CODE     ---       #138
      00FF8079H   LINE      CODE     ---       #139
      ---         BLOCKEND  ---      ---       LVL=0
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 44



      00FF000EH   BLOCK     CODE     ---       LVL=0
      DR4         REGSYM    ---      FLOAT     num
      00FF000EH   LINE      CODE     ---       #141
      00FF000EH   LINE      CODE     ---       #143
      ---         BLOCKEND  ---      ---       LVL=0

      00FF4B66H   BLOCK     CODE     ---       LVL=0
      00FF4B66H   LINE      CODE     ---       #146
      00FF4B66H   LINE      CODE     ---       #149
      00FF4B69H   LINE      CODE     ---       #151
      00FF4B79H   LINE      CODE     ---       #154
      00FF4B86H   LINE      CODE     ---       #155
      00FF4B8AH   LINE      CODE     ---       #156
      00FF4B8AH   LINE      CODE     ---       #157
      00FF4B8AH   LINE      CODE     ---       #158
      00FF4B8AH   LINE      CODE     ---       #159
      00FF4B8CH   LINE      CODE     ---       #160
      00FF4B98H   LINE      CODE     ---       #163
      00FF4BA5H   LINE      CODE     ---       #164
      00FF4BA9H   LINE      CODE     ---       #165
      00FF4BA9H   LINE      CODE     ---       #166
      00FF4BA9H   LINE      CODE     ---       #167
      00FF4BA9H   LINE      CODE     ---       #168
      00FF4BABH   LINE      CODE     ---       #169
      00FF4BB7H   LINE      CODE     ---       #172
      00FF4BC4H   LINE      CODE     ---       #173
      00FF4BC8H   LINE      CODE     ---       #174
      00FF4BC8H   LINE      CODE     ---       #175
      00FF4BC8H   LINE      CODE     ---       #176
      00FF4BC8H   LINE      CODE     ---       #177
      00FF4BCAH   LINE      CODE     ---       #178
      00FF4BD6H   LINE      CODE     ---       #182
      00FF4BE3H   LINE      CODE     ---       #183
      00FF4BE7H   LINE      CODE     ---       #184
      00FF4BE7H   LINE      CODE     ---       #185
      00FF4BE7H   LINE      CODE     ---       #186
      00FF4BE7H   LINE      CODE     ---       #187
      00FF4BE9H   LINE      CODE     ---       #188
      00FF4BF5H   LINE      CODE     ---       #191
      00FF4C02H   LINE      CODE     ---       #192
      00FF4C0FH   LINE      CODE     ---       #193
      00FF4C18H   LINE      CODE     ---       #194
      00FF4C1BH   LINE      CODE     ---       #195
      00FF4C1BH   LINE      CODE     ---       #196
      00FF4C1DH   LINE      CODE     ---       #197
      00FF4C29H   LINE      CODE     ---       #200
      00FF4C36H   LINE      CODE     ---       #201
      00FF4C43H   LINE      CODE     ---       #202
      00FF4C4CH   LINE      CODE     ---       #203
      00FF4C4FH   LINE      CODE     ---       #204
      00FF4C53H   LINE      CODE     ---       #205
      00FF4C54H   LINE      CODE     ---       #208
      00FF4C58H   LINE      CODE     ---       #210
      00FF4C65H   LINE      CODE     ---       #211
      00FF4C72H   LINE      CODE     ---       #212
      00FF4C7BH   LINE      CODE     ---       #213
      00FF4C7EH   LINE      CODE     ---       #214
      00FF4C82H   LINE      CODE     ---       #215
      00FF4C82H   LINE      CODE     ---       #216
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       usb
      00FF81BAH   PUBLIC    CODE     ---       usb_setup_in
      00FF824AH   PUBLIC    CODE     ---       usb_setup_out
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 45


      00FF6937H   PUBLIC    CODE     ---       usb_ctrl_in
      00FF6E5AH   PUBLIC    CODE     ---       usb_ctrl_out
      00FF8004H   PUBLIC    CODE     ---       usb_OUT_done
      00FF005EH   PUBLIC    CODE     ---       usb_suspend
      00FF00AEH   PUBLIC    CODE     ---       usb_bulk_intr_in
      00FF7E8EH   PUBLIC    CODE     ---       usb_bulk_intr_out
      00FF005FH   PUBLIC    CODE     ---       usb_resume
      00FF76A4H   PUBLIC    CODE     ---       usb_IN
      00FF73EEH   PUBLIC    CODE     ---       usb_read_fifo?_
      00FF6FD3H   PUBLIC    CODE     ---       usb_reset
      00FF663FH   PUBLIC    CODE     ---       USB_SendData
      00FF6255H   PUBLIC    CODE     ---       usb_setup
      00FF7836H   PUBLIC    CODE     ---       usb_write_fifo?_
      00FF6A65H   PUBLIC    CODE     ---       usb_init
      00FF8258H   PUBLIC    CODE     ---       usb_setup_stall
      00FF0036H   PUBLIC    CODE     ---       usb_setup_status
      00FF0166H   PUBLIC    CODE     ---       usb_read_reg
      00FF7701H   PUBLIC    CODE     ---       usb_in_ep1
      00FF780BH   PUBLIC    CODE     ---       usb_in_ep2
      00FF56F6H   PUBLIC    CODE     ---       usb_out_ep1
      00FF8181H   PUBLIC    CODE     ---       usb_write_reg
      00FF62DFH   PUBLIC    CODE     ---       usb_isr
      0000021FH   PUBLIC    EDATA    ---       Setup
      00000227H   PUBLIC    EDATA    BYTE      OutNumber
      00000229H   PUBLIC    EDATA    BYTE      DeviceState
      0000022AH   PUBLIC    EDATA    ---       Ep0State
      00000231H   PUBLIC    EDATA    BYTE      InEpState
      00000232H   PUBLIC    EDATA    BYTE      OutEpState
      00000020H.7 PUBLIC    BIT      BIT       bUsbOutReady
      00000021H.0 PUBLIC    BIT      BIT       bUsbFeatureReady
      00000021H.1 PUBLIC    BIT      BIT       bUsbInBusy
      00010000H   PUBLIC    XDATA    ---       UsbInBuffer
      00010040H   PUBLIC    XDATA    ---       UsbOutBuffer
      00010080H   PUBLIC    XDATA    ---       UsbFeatureBuffer
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000DCH   SFRSYM    DATA     BYTE      USBCLK
      000000ECH   SFRSYM    DATA     BYTE      USBDAT
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      000000FCH   SFRSYM    DATA     BYTE      USBADR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000AFH.7 SFRSYM    DATA     BIT       EUSB
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 46


      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      ---         MODULE    ---      ---       usb_req_class
      00FF8104H   PUBLIC    CODE     ---       usb_set_ctrl_line_state
      00FF7900H   PUBLIC    CODE     ---       usb_get_line_coding
      00FF7926H   PUBLIC    CODE     ---       usb_set_line_coding
      00FF7D3BH   PUBLIC    CODE     ---       usb_req_class
      0000029AH   PUBLIC    EDATA    ---       LineCoding
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 47



      ---         MODULE    ---      ---       usb_req_std
      00FF7338H   PUBLIC    CODE     ---       usb_get_interface
      00FF5B3CH   PUBLIC    CODE     ---       usb_clear_feature
      00FF78B1H   PUBLIC    CODE     ---       usb_set_interface
      00FF5BDBH   PUBLIC    CODE     ---       usb_get_descriptor
      00FF0026H   PUBLIC    CODE     ---       usb_set_descriptor
      00FF6AC3H   PUBLIC    CODE     ---       usb_req_std
      00FF002EH   PUBLIC    CODE     ---       usb_synch_frame
      00FF6F40H   PUBLIC    CODE     ---       usb_set_address
      00FF5C7AH   PUBLIC    CODE     ---       usb_set_feature
      00FF6BCCH   PUBLIC    CODE     ---       usb_get_configuration
      00FF59F7H   PUBLIC    CODE     ---       usb_set_configuration
      00FF50ADH   PUBLIC    CODE     ---       usb_get_status
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      ---         MODULE    ---      ---       usb_req_vendor
      00FF0046H   PUBLIC    CODE     ---       usb_req_vendor
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 48


      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      ---         MODULE    ---      ---       util
      00FF8159H   PUBLIC    CODE     ---       LCD12864_DisplayClear
      00FF7428H   PUBLIC    CODE     ---       OLED12864_ScrollRight
      00FF7EA7H   PUBLIC    CODE     ---       OLED12864_DisplayOff
      00FF749CH   PUBLIC    CODE     ---       OLED12864_ScrollLeft
      00FF79F9H   PUBLIC    CODE     ---       OLED12864_SetAddressMode
      00FF7A1AH   PUBLIC    CODE     ---       OLED12864_SetContrast
      00FF7ED9H   PUBLIC    CODE     ---       LCD12864_AutoWrapOff
      00FF7279H   PUBLIC    CODE     ---       OLED12864_ShowPicture
      00FF7D56H   PUBLIC    CODE     ---       OLED12864_ScrollStart
      00FF75ABH   PUBLIC    CODE     ---       SEG7_ShowFloat
      00FF6F8AH   PUBLIC    CODE     ---       SEG7_ShowString
      00FF7541H   PUBLIC    CODE     ---       SEG7_ShowCode
      00FF701BH   PUBLIC    CODE     ---       LED40_SendData
      00FF7D70H   PUBLIC    CODE     ---       OLED12864_DisplayOn
      00FF7A3BH   PUBLIC    CODE     ---       LCD12864_ReverseLine
      00FF7EF2H   PUBLIC    CODE     ---       OLED12864_ScrollStop
      00FF7062H   PUBLIC    CODE     ---       LED64_SendData
      00FF772EH   PUBLIC    CODE     ---       printf_hid
      00FF00EEH   PUBLIC    CODE     ---       SEG7_ShowLong
      00FF7D8AH   PUBLIC    CODE     ---       LCD12864_ScrollRight
      00FF7DA4H   PUBLIC    CODE     ---       LCD12864_AutoWrapOn
      00FF7F0BH   PUBLIC    CODE     ---       LCD12864_DisplayOff
      00FF7576H   PUBLIC    CODE     ---       reverse2
      00FF6A04H   PUBLIC    CODE     ---       reverse4
      00FF7F3DH   PUBLIC    CODE     ---       LCD12864_ScrollLeft
      00FF7F56H   PUBLIC    CODE     ---       OLED12864_HorizontalMirror
      00FF6E0BH   PUBLIC    CODE     ---       LCD12864_ShowPicture
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 49


      00FF7462H   PUBLIC    CODE     ---       OLED12864_ScrollUp
      00FF7F6FH   PUBLIC    CODE     ---       OLED12864_DisplayContent
      00FF816DH   PUBLIC    CODE     ---       OLED12864_DisplayReverse
      00FF7DBEH   PUBLIC    CODE     ---       OLED12864_VerticalMirror
      00FF7DD8H   PUBLIC    CODE     ---       LCD12864_CursorReturnHome
      00FF7DF2H   PUBLIC    CODE     ---       OLED12864_DisplayEntire
      00FF7E0CH   PUBLIC    CODE     ---       LCD12864_DisplayOn
      00FF7E26H   PUBLIC    CODE     ---       LCD12864_CursorMoveRight
      00FF7376H   PUBLIC    CODE     ---       LCD12864_ShowString
      00FF7F88H   PUBLIC    CODE     ---       LCD12864_CursorOff
      00FF801CH   PUBLIC    CODE     ---       sleep_ms
      00FF7FA1H   PUBLIC    CODE     ---       LCD12864_CursorMoveLeft
      00FF7C3FH   PUBLIC    CODE     ---       sleep_us
      00FF78D9H   PUBLIC    CODE     ---       LCD12864_ScrollUp
      00FF7E40H   PUBLIC    CODE     ---       LCD12864_CursorOn
      000001A7H   PUBLIC    EDATA    BYTE      ?SEG7_ShowString?BYTE
      000001D3H   PUBLIC    EDATA    BYTE      ?printf_hid?BYTE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      ---         MODULE    ---      ---       usb_desc
      00FF6363H   PUBLIC    CODE     ---       PRODUCTDESC
      00FF6381H   PUBLIC    CODE     ---       LANGIDDESC
      00FF6385H   PUBLIC    CODE     ---       DEVICEDESC
      00FF6397H   PUBLIC    CODE     ---       CONFIGDESC
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 50


      00FF63DAH   PUBLIC    CODE     ---       PACKET0
      00FF63DCH   PUBLIC    CODE     ---       PACKET1
      00FF63DEH   PUBLIC    CODE     ---       MANUFACTDESC
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      ---         MODULE    ---      ---       MPU6050
      00FF6B77H   PUBLIC    CODE     ---       mpu6050_simiic_read_regs
      00FF785FH   PUBLIC    CODE     ---       mpu6050_simiic_read_reg
      00FF3FF8H   PUBLIC    CODE     ---       Kalman_Filter
      00FF7238H   PUBLIC    CODE     ---       Yijielvbo
      00FF6C20H   PUBLIC    CODE     ---       mpu6050_init
      00FF72B9H   PUBLIC    CODE     ---       mpu6050_get_acc
      00FF678AH   PUBLIC    CODE     ---       mpu6050_iic_init
      00FF6DBBH   PUBLIC    CODE     ---       mpu6050_get_gyro
      00FF2D0CH   PUBLIC    CODE     ---       IMUupdate
      00000023H   PUBLIC    EDATA    INT       mpu6050_acc_x
      00000025H   PUBLIC    EDATA    INT       mpu6050_acc_y
      00000027H   PUBLIC    EDATA    FLOAT     t_0
      0000002BH   PUBLIC    EDATA    INT       mpu6050_acc_z
      0000002DH   PUBLIC    EDATA    FLOAT     t_1
      00000031H   PUBLIC    EDATA    FLOAT     angle
      00000035H   PUBLIC    EDATA    FLOAT     exInt
      00000039H   PUBLIC    EDATA    FLOAT     eyInt
      0000003DH   PUBLIC    EDATA    FLOAT     ezInt
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 51


      00000041H   PUBLIC    EDATA    FLOAT     Angle_err
      00000045H   PUBLIC    EDATA    FLOAT     PCt_0
      00000049H   PUBLIC    EDATA    ---       Pdot
      00000059H   PUBLIC    EDATA    FLOAT     PCt_1
      0000005DH   PUBLIC    EDATA    INT       mpu6050_gyro_x
      0000005FH   PUBLIC    EDATA    INT       mpu6050_gyro_y
      00000061H   PUBLIC    EDATA    INT       mpu6050_gyro_z
      00000063H   PUBLIC    EDATA    ---       PP
      00000073H   PUBLIC    EDATA    FLOAT     q0
      00000077H   PUBLIC    EDATA    FLOAT     q1
      0000007BH   PUBLIC    EDATA    FLOAT     q2
      0000007FH   PUBLIC    EDATA    FLOAT     q3
      00000083H   PUBLIC    EDATA    FLOAT     angle_dot
      00000087H   PUBLIC    EDATA    FLOAT     d_t
      0000008BH   PUBLIC    EDATA    FLOAT     Q_angle
      0000008FH   PUBLIC    EDATA    FLOAT     R_angle
      00000093H   PUBLIC    EDATA    FLOAT     AngleX
      00000097H   PUBLIC    EDATA    FLOAT     AngleY
      0000009BH   PUBLIC    EDATA    FLOAT     E
      0000009FH   PUBLIC    EDATA    FLOAT     Q_bias
      000000A3H   PUBLIC    EDATA    CHAR      C_0
      000000A4H   PUBLIC    EDATA    FLOAT     K_0
      000000A8H   PUBLIC    EDATA    FLOAT     K_1
      000000ACH   PUBLIC    EDATA    INT       mpu_temp
      000000AEH   PUBLIC    EDATA    FLOAT     Q_gyro
      000000B2H   PUBLIC    EDATA    FLOAT     K1
      00000004H   PUBLIC    EDATA    BYTE      ?IMUupdate?BYTE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 52


      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0
      00FF823BH   SYMBOL    CODE     ---       mpu6050_simiic_start
      00FF81CBH   SYMBOL    CODE     ---       mpu6050_simiic_stop
      00FF7994H   SYMBOL    CODE     ---       mpu6050_simiic_write_reg
      00FF7A7CH   SYMBOL    CODE     ---       mpu6050_simiic_sendack
      00FF73B3H   SYMBOL    CODE     ---       mpu6050_read_ch
      00FF76D3H   SYMBOL    CODE     ---       mpu6050_send_ch
      00FF7CB0H   SYMBOL    CODE     ---       mpu6050_sccb_waitack
      00FF82A8H   SYMBOL    CODE     ---       mpu6050_simiic_delay

      00FF82A8H   BLOCK     CODE     ---       LVL=0
      WR6         REGSYM    ---      WORD      j
      00FF82A8H   LINE      CODE     ---       #21
      00FF82A8H   LINE      CODE     ---       #22
      00FF82A8H   LINE      CODE     ---       #23
      00FF82AAH   LINE      CODE     ---       #24
      00FF82B2H   LINE      CODE     ---       #25
      ---         BLOCKEND  ---      ---       LVL=0

      00FF823BH   BLOCK     CODE     ---       LVL=0
      00FF823BH   LINE      CODE     ---       #28
      00FF823BH   LINE      CODE     ---       #30
      00FF823DH   LINE      CODE     ---       #31
      00FF823FH   LINE      CODE     ---       #32
      00FF8242H   LINE      CODE     ---       #33
      00FF8244H   LINE      CODE     ---       #34
      00FF8247H   LINE      CODE     ---       #35
      00FF8249H   LINE      CODE     ---       #36
      ---         BLOCKEND  ---      ---       LVL=0

      00FF81CBH   BLOCK     CODE     ---       LVL=0
      00FF81CBH   LINE      CODE     ---       #39
      00FF81CBH   LINE      CODE     ---       #41
      00FF81CDH   LINE      CODE     ---       #42
      00FF81CFH   LINE      CODE     ---       #43
      00FF81D2H   LINE      CODE     ---       #44
      00FF81D4H   LINE      CODE     ---       #45
      00FF81D7H   LINE      CODE     ---       #46
      00FF81D9H   LINE      CODE     ---       #47
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7A7CH   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      ack_dat
      00FF7A7CH   LINE      CODE     ---       #52
      00FF7A80H   LINE      CODE     ---       #54
      00FF7A82H   LINE      CODE     ---       #55
      00FF7A85H   LINE      CODE     ---       #56
      00FF7A8DH   LINE      CODE     ---       #57
      00FF7A8FH   LINE      CODE     ---       #59
      00FF7A91H   LINE      CODE     ---       #60
      00FF7A94H   LINE      CODE     ---       #61
      00FF7A96H   LINE      CODE     ---       #62
      00FF7A99H   LINE      CODE     ---       #63
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7CB0H   BLOCK     CODE     ---       LVL=0
      00FF7CB0H   LINE      CODE     ---       #66
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 53


      00FF7CB0H   LINE      CODE     ---       #68
      00FF7CB2H   LINE      CODE     ---       #70
      00FF7CB5H   LINE      CODE     ---       #72
      00FF7CB7H   LINE      CODE     ---       #73
      00FF7CBAH   LINE      CODE     ---       #75
      00FF7CBDH   LINE      CODE     ---       #78
      00FF7CBFH   LINE      CODE     ---       #79
      00FF7CC2H   LINE      CODE     ---       #80
      00FF7CC2H   LINE      CODE     ---       #82
      00FF7CC4H   LINE      CODE     ---       #83
      00FF7CC7H   LINE      CODE     ---       #84
      00FF7CCBH   LINE      CODE     ---       #85
      ---         BLOCKEND  ---      ---       LVL=0

      00FF76D3H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      c
      00FF76D7H   BLOCK     CODE     NEAR LAB  LVL=1
      R14         REGSYM    ---      BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      00FF76D3H   LINE      CODE     ---       #91
      00FF76D7H   LINE      CODE     ---       #92
      00FF76D7H   LINE      CODE     ---       #93
      00FF76DAH   LINE      CODE     ---       #94
      00FF76DCH   LINE      CODE     ---       #96
      00FF76E5H   LINE      CODE     ---       #97
      00FF76E7H   LINE      CODE     ---       #98
      00FF76E9H   LINE      CODE     ---       #99
      00FF76ECH   LINE      CODE     ---       #100
      00FF76EEH   LINE      CODE     ---       #101
      00FF76F1H   LINE      CODE     ---       #102
      00FF76F3H   LINE      CODE     ---       #103
      00FF76FBH   LINE      CODE     ---       #104
      00FF76FEH   LINE      CODE     ---       #105
      ---         BLOCKEND  ---      ---       LVL=0

      00FF73B3H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      ack_x
      00FF73B9H   BLOCK     CODE     NEAR LAB  LVL=1
      R13         REGSYM    ---      BYTE      i
      R14         REGSYM    ---      BYTE      c
      ---         BLOCKEND  ---      ---       LVL=1
      00FF73B3H   LINE      CODE     ---       #111
      00FF73B9H   LINE      CODE     ---       #112
      00FF73B9H   LINE      CODE     ---       #115
      00FF73BBH   LINE      CODE     ---       #116
      00FF73BDH   LINE      CODE     ---       #117
      00FF73C0H   LINE      CODE     ---       #118
      00FF73C2H   LINE      CODE     ---       #120
      00FF73C5H   LINE      CODE     ---       #122
      00FF73C8H   LINE      CODE     ---       #123
      00FF73CAH   LINE      CODE     ---       #124
      00FF73CDH   LINE      CODE     ---       #125
      00FF73CFH   LINE      CODE     ---       #126
      00FF73D2H   LINE      CODE     ---       #127
      00FF73D4H   LINE      CODE     ---       #128
      00FF73D7H   LINE      CODE     ---       #130
      00FF73D9H   LINE      CODE     ---       #131
      00FF73DDH   LINE      CODE     ---       #134
      00FF73DFH   LINE      CODE     ---       #135
      00FF73E2H   LINE      CODE     ---       #136
      00FF73E7H   LINE      CODE     ---       #138
      00FF73E9H   LINE      CODE     ---       #139
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7994H   BLOCK     CODE     ---       LVL=0
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 54


      R13         REGSYM    ---      BYTE      dev_add
      R14         REGSYM    ---      BYTE      reg
      R15         REGSYM    ---      BYTE      dat
      00FF7994H   LINE      CODE     ---       #151
      00FF799CH   LINE      CODE     ---       #153
      00FF799FH   LINE      CODE     ---       #154
      00FF79A4H   LINE      CODE     ---       #155
      00FF79A9H   LINE      CODE     ---       #156
      00FF79AEH   LINE      CODE     ---       #157
      00FF79B1H   LINE      CODE     ---       #158
      ---         BLOCKEND  ---      ---       LVL=0

      00FF785FH   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      dev_add
      R14         REGSYM    ---      BYTE      reg
      00FF7865H   BLOCK     CODE     NEAR LAB  LVL=1
      R15         REGSYM    ---      BYTE      dat
      ---         BLOCKEND  ---      ---       LVL=1
      00FF785FH   LINE      CODE     ---       #169
      00FF7865H   LINE      CODE     ---       #170
      00FF7865H   LINE      CODE     ---       #172
      00FF7868H   LINE      CODE     ---       #173
      00FF786DH   LINE      CODE     ---       #174
      00FF7872H   LINE      CODE     ---       #177
      00FF7875H   LINE      CODE     ---       #178
      00FF787EH   LINE      CODE     ---       #179
      00FF7882H   LINE      CODE     ---       #180
      00FF7885H   LINE      CODE     ---       #182
      00FF7885H   LINE      CODE     ---       #183
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6B77H   BLOCK     CODE     ---       LVL=0
      000002DBH   SYMBOL    EDATA    BYTE      dev_add
      000002DCH   SYMBOL    EDATA    BYTE      reg
      REG=3       REGSYM    ---      ---       dat_add
      000002DDH   SYMBOL    EDATA    BYTE      num
      00FF6B77H   LINE      CODE     ---       #196
      00FF6B87H   LINE      CODE     ---       #198
      00FF6B8AH   LINE      CODE     ---       #199
      00FF6B93H   LINE      CODE     ---       #200
      00FF6B9AH   LINE      CODE     ---       #203
      00FF6B9DH   LINE      CODE     ---       #204
      00FF6BA8H   LINE      CODE     ---       #205
      00FF6BAAH   LINE      CODE     ---       #207
      00FF6BB2H   LINE      CODE     ---       #208
      00FF6BB4H   LINE      CODE     ---       #209
      00FF6BBFH   LINE      CODE     ---       #210
      00FF6BC6H   LINE      CODE     ---       #211
      00FF6BC9H   LINE      CODE     ---       #212
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6C20H   BLOCK     CODE     ---       LVL=0
      00FF6C20H   LINE      CODE     ---       #220
      00FF6C20H   LINE      CODE     ---       #222
      00FF6C27H   LINE      CODE     ---       #224
      00FF6C31H   LINE      CODE     ---       #225
      00FF6C3CH   LINE      CODE     ---       #226
      00FF6C47H   LINE      CODE     ---       #227
      00FF6C52H   LINE      CODE     ---       #228
      00FF6C5DH   LINE      CODE     ---       #229
      00FF6C67H   LINE      CODE     ---       #230
      00FF6C72H   LINE      CODE     ---       #231
      00FF6C73H   LINE      CODE     ---       #232
      ---         BLOCKEND  ---      ---       LVL=0

L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 55


      00FF678AH   BLOCK     CODE     ---       LVL=0
      00FF678AH   LINE      CODE     ---       #234
      00FF678AH   LINE      CODE     ---       #236
      00FF6791H   LINE      CODE     ---       #237
      00FF679EH   LINE      CODE     ---       #238
      00FF67ADH   LINE      CODE     ---       #239
      00FF67BCH   LINE      CODE     ---       #240
      00FF67CBH   LINE      CODE     ---       #241
      00FF67DAH   LINE      CODE     ---       #242
      00FF67E7H   LINE      CODE     ---       #243
      ---         BLOCKEND  ---      ---       LVL=0

      00FF72B9H   BLOCK     CODE     ---       LVL=0
      000002A1H   SYMBOL    EDATA    ---       dat
      00FF72B9H   LINE      CODE     ---       #254
      00FF72B9H   LINE      CODE     ---       #255
      00FF72B9H   LINE      CODE     ---       #258
      00FF72C8H   LINE      CODE     ---       #259
      00FF72D8H   LINE      CODE     ---       #260
      00FF72E8H   LINE      CODE     ---       #261
      00FF72F8H   LINE      CODE     ---       #262
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6DBBH   BLOCK     CODE     ---       LVL=0
      0000026AH   SYMBOL    EDATA    ---       dat
      00FF6DBBH   LINE      CODE     ---       #270
      00FF6DBBH   LINE      CODE     ---       #271
      00FF6DBBH   LINE      CODE     ---       #274
      00FF6DCAH   LINE      CODE     ---       #275
      00FF6DDAH   LINE      CODE     ---       #276
      00FF6DEAH   LINE      CODE     ---       #277
      00FF6DFAH   LINE      CODE     ---       #278
      00FF6E0AH   LINE      CODE     ---       #279
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3FF8H   BLOCK     CODE     ---       LVL=0
      DR24        REGSYM    ---      FLOAT     Accel
      DR28        REGSYM    ---      FLOAT     Gyro
      00FF3FF8H   LINE      CODE     ---       #304
      00FF3FFEH   LINE      CODE     ---       #306
      00FF401BH   LINE      CODE     ---       #307
      00FF4035H   LINE      CODE     ---       #309
      00FF4040H   LINE      CODE     ---       #310
      00FF4044H   LINE      CODE     ---       #311
      00FF404CH   LINE      CODE     ---       #312
      00FF4060H   LINE      CODE     ---       #313
      00FF4072H   LINE      CODE     ---       #314
      00FF4084H   LINE      CODE     ---       #315
      00FF4098H   LINE      CODE     ---       #317
      00FF40A5H   LINE      CODE     ---       #319
      00FF40BBH   LINE      CODE     ---       #320
      00FF40CAH   LINE      CODE     ---       #322
      00FF40E0H   LINE      CODE     ---       #324
      00FF40EFH   LINE      CODE     ---       #325
      00FF40FCH   LINE      CODE     ---       #327
      00FF4100H   LINE      CODE     ---       #328
      00FF410FH   LINE      CODE     ---       #330
      00FF412BH   LINE      CODE     ---       #331
      00FF413FH   LINE      CODE     ---       #332
      00FF4155H   LINE      CODE     ---       #333
      00FF416BH   LINE      CODE     ---       #335
      00FF4183H   LINE      CODE     ---       #336
      00FF4195H   LINE      CODE     ---       #337
      00FF41A2H   LINE      CODE     ---       #338
      ---         BLOCKEND  ---      ---       LVL=0
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 56



      00FF7238H   BLOCK     CODE     ---       LVL=0
      DR24        REGSYM    ---      FLOAT     angle_m
      DR28        REGSYM    ---      FLOAT     gyro_m
      00FF7238H   LINE      CODE     ---       #345
      00FF723CH   LINE      CODE     ---       #347
      00FF7278H   LINE      CODE     ---       #348
      ---         BLOCKEND  ---      ---       LVL=0

      00FF2D0CH   BLOCK     CODE     ---       LVL=0
      00000008H   SYMBOL    EDATA    FLOAT     gx
      DR12        REGSYM    ---      FLOAT     gy
      0000000CH   SYMBOL    EDATA    FLOAT     gz
      00000010H   SYMBOL    EDATA    FLOAT     ax
      00000014H   SYMBOL    EDATA    FLOAT     ay
      00000018H   SYMBOL    EDATA    FLOAT     az
      00FF2D14H   BLOCK     CODE     NEAR LAB  LVL=1
      DR28        REGSYM    ---      FLOAT     norm
      0000001CH   SYMBOL    EDATA    FLOAT     vx
      DR24        REGSYM    ---      FLOAT     vy
      DR28        REGSYM    ---      FLOAT     vz
      DR16        REGSYM    ---      FLOAT     ex
      DR20        REGSYM    ---      FLOAT     ey
      DR24        REGSYM    ---      FLOAT     ez
      ---         BLOCKEND  ---      ---       LVL=1
      00FF2D0CH   LINE      CODE     ---       #366
      00FF2D14H   LINE      CODE     ---       #367
      00FF2D14H   LINE      CODE     ---       #372
      00FF2D42H   LINE      CODE     ---       #373
      00FF2D4FH   LINE      CODE     ---       #374
      00FF2D5CH   LINE      CODE     ---       #375
      00FF2D69H   LINE      CODE     ---       #381
      00FF2D99H   LINE      CODE     ---       #382
      00FF2DBBH   LINE      CODE     ---       #383
      00FF2DF8H   LINE      CODE     ---       #385
      00FF2E13H   LINE      CODE     ---       #386
      00FF2E30H   LINE      CODE     ---       #387
      00FF2E4DH   LINE      CODE     ---       #389
      00FF2E67H   LINE      CODE     ---       #390
      00FF2E79H   LINE      CODE     ---       #391
      00FF2E8BH   LINE      CODE     ---       #393
      00FF2EACH   LINE      CODE     ---       #394
      00FF2EC1H   LINE      CODE     ---       #395
      00FF2EDAH   LINE      CODE     ---       #397
      00FF2F2AH   LINE      CODE     ---       #398
      00FF2F65H   LINE      CODE     ---       #399
      00FF2F9AH   LINE      CODE     ---       #400
      00FF2FD7H   LINE      CODE     ---       #402
      00FF3017H   LINE      CODE     ---       #403
      00FF3024H   LINE      CODE     ---       #404
      00FF3031H   LINE      CODE     ---       #405
      00FF303EH   LINE      CODE     ---       #406
      00FF304BH   LINE      CODE     ---       #408
      00FF3083H   LINE      CODE     ---       #409
      00FF30BBH   LINE      CODE     ---       #410
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       OLED_SPI
      00FF811AH   PUBLIC    CODE     ---       OLED_Display_On
      00FF775BH   PUBLIC    CODE     ---       OLED_DisplayTurn
      00FF5556H   PUBLIC    CODE     ---       OLED_Init
      00FF4FA9H   PUBLIC    CODE     ---       OLED_ShowFloat
      00FF655CH   PUBLIC    CODE     ---       OLED_ShowString
      00FF7E5AH   PUBLIC    CODE     ---       OLED_WR_Byte
      00FF57BAH   PUBLIC    CODE     ---       OLED_ShowChar
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 57


      00FF7BACH   PUBLIC    CODE     ---       OLED_ColorTurn
      00FF7888H   PUBLIC    CODE     ---       OLED_Set_Pos
      00FF5E43H   PUBLIC    CODE     ---       OLED_ShowInt
      00FF52B2H   PUBLIC    CODE     ---       OLED_ShowNum
      00FF812FH   PUBLIC    CODE     ---       OLED_Display_Off
      00FF7E74H   PUBLIC    CODE     ---       oled_pow
      00FF6468H   PUBLIC    CODE     ---       OLED_ShowChinese
      00FF750BH   PUBLIC    CODE     ---       OLED_Clear
      00FF5F72H   PUBLIC    CODE     ---       OLED_DrawBMP
      00FF0E5BH   PUBLIC    CODE     ---       asc2_0806
      00FF1083H   PUBLIC    CODE     ---       asc2_1608
      00FF1673H   PUBLIC    CODE     ---       Hzk
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000A0H.6 SFRSYM    DATA     BIT       OLED_RES
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000A0H.2 SFRSYM    DATA     BIT       OLED_CS
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A0H.4 SFRSYM    DATA     BIT       OLED_DC
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF7BACH   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      i
      00FF7BACH   LINE      CODE     ---       #222
      00FF7BB0H   LINE      CODE     ---       #224
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 58


      00FF7BB4H   LINE      CODE     ---       #226
      00FF7BBBH   LINE      CODE     ---       #227
      00FF7BBBH   LINE      CODE     ---       #228
      00FF7BC0H   LINE      CODE     ---       #230
      00FF7BC7H   LINE      CODE     ---       #231
      ---         BLOCKEND  ---      ---       LVL=0

      00FF775BH   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      i
      00FF775BH   LINE      CODE     ---       #235
      00FF775FH   LINE      CODE     ---       #237
      00FF7763H   LINE      CODE     ---       #239
      00FF776AH   LINE      CODE     ---       #240
      00FF7771H   LINE      CODE     ---       #241
      00FF7771H   LINE      CODE     ---       #242
      00FF7776H   LINE      CODE     ---       #244
      00FF777DH   LINE      CODE     ---       #245
      00FF7784H   LINE      CODE     ---       #246
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7E5AH   BLOCK     CODE     ---       LVL=0
      R7          REGSYM    ---      BYTE      dat
      R10         REGSYM    ---      BYTE      cmd
      00FF7E5AH   LINE      CODE     ---       #276
      00FF7E5EH   LINE      CODE     ---       #278
      00FF7E62H   LINE      CODE     ---       #279
      00FF7E66H   LINE      CODE     ---       #281
      00FF7E68H   LINE      CODE     ---       #282
      00FF7E6AH   LINE      CODE     ---       #283
      00FF7E6FH   LINE      CODE     ---       #284
      00FF7E71H   LINE      CODE     ---       #285
      00FF7E73H   LINE      CODE     ---       #286
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7888H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      x
      R10         REGSYM    ---      BYTE      y
      00FF7888H   LINE      CODE     ---       #290
      00FF788CH   LINE      CODE     ---       #292
      00FF7897H   LINE      CODE     ---       #293
      00FF78A5H   LINE      CODE     ---       #294
      00FF78AEH   LINE      CODE     ---       #295
      ---         BLOCKEND  ---      ---       LVL=0

      00FF811AH   BLOCK     CODE     ---       LVL=0
      00FF811AH   LINE      CODE     ---       #297
      00FF811AH   LINE      CODE     ---       #299
      00FF8121H   LINE      CODE     ---       #300
      00FF8128H   LINE      CODE     ---       #301
      ---         BLOCKEND  ---      ---       LVL=0

      00FF812FH   BLOCK     CODE     ---       LVL=0
      00FF812FH   LINE      CODE     ---       #304
      00FF812FH   LINE      CODE     ---       #306
      00FF8136H   LINE      CODE     ---       #307
      00FF813DH   LINE      CODE     ---       #308
      ---         BLOCKEND  ---      ---       LVL=0

      00FF750BH   BLOCK     CODE     ---       LVL=0
      00FF750DH   BLOCK     CODE     NEAR LAB  LVL=1
      R15         REGSYM    ---      BYTE      i
      R14         REGSYM    ---      BYTE      n
      ---         BLOCKEND  ---      ---       LVL=1
      00FF750BH   LINE      CODE     ---       #311
      00FF750DH   LINE      CODE     ---       #312
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 59


      00FF750DH   LINE      CODE     ---       #314
      00FF750FH   LINE      CODE     ---       #316
      00FF751AH   LINE      CODE     ---       #317
      00FF7520H   LINE      CODE     ---       #318
      00FF7527H   LINE      CODE     ---       #319
      00FF7537H   LINE      CODE     ---       #320
      00FF753EH   LINE      CODE     ---       #321
      ---         BLOCKEND  ---      ---       LVL=0

      00FF57BAH   BLOCK     CODE     ---       LVL=0
      R13         REGSYM    ---      BYTE      x
      R14         REGSYM    ---      BYTE      y
      R10         REGSYM    ---      BYTE      chr
      R15         REGSYM    ---      BYTE      sizey
      00FF57C4H   BLOCK     CODE     NEAR LAB  LVL=1
      R12         REGSYM    ---      BYTE      c
      000000E8H   SYMBOL    EDATA    BYTE      sizex
      000000E9H   SYMBOL    EDATA    WORD      i
      000000EBH   SYMBOL    EDATA    WORD      size1
      ---         BLOCKEND  ---      ---       LVL=1
      00FF57BAH   LINE      CODE     ---       #327
      00FF57C4H   LINE      CODE     ---       #328
      00FF57C4H   LINE      CODE     ---       #329
      00FF57CEH   LINE      CODE     ---       #330
      00FF57D4H   LINE      CODE     ---       #331
      00FF57E3H   LINE      CODE     ---       #332
      00FF5801H   LINE      CODE     ---       #333
      00FF5809H   LINE      CODE     ---       #334
      00FF5810H   LINE      CODE     ---       #335
      00FF5814H   LINE      CODE     ---       #337
      00FF5832H   LINE      CODE     ---       #338
      00FF5846H   LINE      CODE     ---       #339
      00FF5866H   LINE      CODE     ---       #341
      00FF5866H   LINE      CODE     ---       #342
      00FF587AH   LINE      CODE     ---       #343
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7E74H   BLOCK     CODE     ---       LVL=0
      R8          REGSYM    ---      BYTE      m
      R9          REGSYM    ---      BYTE      n
      00FF7E78H   BLOCK     CODE     NEAR LAB  LVL=1
      DR4         REGSYM    ---      DWORD     result
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7E74H   LINE      CODE     ---       #345
      00FF7E78H   LINE      CODE     ---       #346
      00FF7E78H   LINE      CODE     ---       #347
      00FF7E7CH   LINE      CODE     ---       #348
      00FF7E8DH   LINE      CODE     ---       #349
      00FF7E8DH   LINE      CODE     ---       #350
      ---         BLOCKEND  ---      ---       LVL=0

      00FF52B2H   BLOCK     CODE     ---       LVL=0
      00000272H   SYMBOL    EDATA    BYTE      x
      00000273H   SYMBOL    EDATA    BYTE      y
      DR12        REGSYM    ---      DWORD     num
      00000274H   SYMBOL    EDATA    BYTE      len
      00000275H   SYMBOL    EDATA    BYTE      sizey
      00FF52C6H   BLOCK     CODE     NEAR LAB  LVL=1
      00000276H   SYMBOL    EDATA    BYTE      t
      00000277H   SYMBOL    EDATA    BYTE      temp
      00000278H   SYMBOL    EDATA    BYTE      m
      00000279H   SYMBOL    EDATA    BYTE      enshow
      ---         BLOCKEND  ---      ---       LVL=1
      00FF52B2H   LINE      CODE     ---       #356
      00FF52C6H   LINE      CODE     ---       #357
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 60


      00FF52C6H   LINE      CODE     ---       #358
      00FF52CBH   LINE      CODE     ---       #359
      00FF52CFH   LINE      CODE     ---       #360
      00FF52DCH   LINE      CODE     ---       #361
      00FF52E0H   LINE      CODE     ---       #363
      00FF5307H   LINE      CODE     ---       #364
      00FF531FH   LINE      CODE     ---       #366
      00FF5325H   LINE      CODE     ---       #368
      00FF5346H   LINE      CODE     ---       #369
      00FF5348H   LINE      CODE     ---       #370
      00FF534EH   LINE      CODE     ---       #371
      00FF534EH   LINE      CODE     ---       #372
      00FF5381H   LINE      CODE     ---       #373
      00FF5397H   LINE      CODE     ---       #374
      ---         BLOCKEND  ---      ---       LVL=0

      00FF655CH   BLOCK     CODE     ---       LVL=0
      000000E4H   SYMBOL    EDATA    BYTE      x
      000000E5H   SYMBOL    EDATA    BYTE      y
      REG=3       REGSYM    ---      ---       chr
      000000E6H   SYMBOL    EDATA    BYTE      sizey
      00FF656CH   BLOCK     CODE     NEAR LAB  LVL=1
      000000E7H   SYMBOL    EDATA    BYTE      j
      ---         BLOCKEND  ---      ---       LVL=1
      00FF655CH   LINE      CODE     ---       #376
      00FF656CH   LINE      CODE     ---       #377
      00FF656CH   LINE      CODE     ---       #378
      00FF6571H   LINE      CODE     ---       #379
      00FF6573H   LINE      CODE     ---       #381
      00FF6597H   LINE      CODE     ---       #382
      00FF65ACH   LINE      CODE     ---       #383
      00FF65BDH   LINE      CODE     ---       #384
      00FF65CCH   LINE      CODE     ---       #385
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6468H   BLOCK     CODE     ---       LVL=0
      R12         REGSYM    ---      BYTE      x
      R14         REGSYM    ---      BYTE      y
      R13         REGSYM    ---      BYTE      no
      R15         REGSYM    ---      BYTE      sizey
      00FF6472H   BLOCK     CODE     NEAR LAB  LVL=1
      000002C7H   SYMBOL    EDATA    WORD      i
      000002C9H   SYMBOL    EDATA    WORD      size1
      ---         BLOCKEND  ---      ---       LVL=1
      00FF6468H   LINE      CODE     ---       #387
      00FF6472H   LINE      CODE     ---       #388
      00FF6472H   LINE      CODE     ---       #389
      00FF6494H   LINE      CODE     ---       #390
      00FF6498H   LINE      CODE     ---       #392
      00FF64ADH   LINE      CODE     ---       #393
      00FF64CDH   LINE      CODE     ---       #395
      00FF64CDH   LINE      CODE     ---       #396
      00FF64E1H   LINE      CODE     ---       #397
      ---         BLOCKEND  ---      ---       LVL=0

      00FF5F72H   BLOCK     CODE     ---       LVL=0
      0000027AH   SYMBOL    EDATA    BYTE      x
      0000027BH   SYMBOL    EDATA    BYTE      y
      0000027CH   SYMBOL    EDATA    BYTE      sizex
      0000027DH   SYMBOL    EDATA    BYTE      sizey
      REG=3       REGSYM    ---      ---       BMP
      00FF5F86H   BLOCK     CODE     NEAR LAB  LVL=1
      0000027EH   SYMBOL    EDATA    WORD      j
      00000280H   SYMBOL    EDATA    BYTE      i
      00000281H   SYMBOL    EDATA    BYTE      m
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 61


      ---         BLOCKEND  ---      ---       LVL=1
      00FF5F72H   LINE      CODE     ---       #404
      00FF5F86H   LINE      CODE     ---       #405
      00FF5F86H   LINE      CODE     ---       #406
      00FF5F8CH   LINE      CODE     ---       #408
      00FF5FACH   LINE      CODE     ---       #409
      00FF5FAFH   LINE      CODE     ---       #411
      00FF5FC4H   LINE      CODE     ---       #412
      00FF5FC7H   LINE      CODE     ---       #414
      00FF5FE0H   LINE      CODE     ---       #415
      00FF5FF3H   LINE      CODE     ---       #416
      00FF6006H   LINE      CODE     ---       #417
      ---         BLOCKEND  ---      ---       LVL=0

      00FF4FA9H   BLOCK     CODE     ---       LVL=0
      000000BAH   SYMBOL    EDATA    BYTE      x
      000000BBH   SYMBOL    EDATA    BYTE      y
      DR12        REGSYM    ---      FLOAT     dat
      000000BCH   SYMBOL    EDATA    BYTE      num
      000000BDH   SYMBOL    EDATA    BYTE      pointnum
      000000BEH   SYMBOL    EDATA    BYTE      size2
      00FF4FC1H   BLOCK     CODE     NEAR LAB  LVL=1
      000000BFH   SYMBOL    EDATA    BYTE      length
      000000C0H   SYMBOL    EDATA    ---       buff
      000000E2H   SYMBOL    EDATA    CHAR      start
      000000E3H   SYMBOL    EDATA    CHAR      end
      R7          REGSYM    ---      CHAR      point
      ---         BLOCKEND  ---      ---       LVL=1
      00FF4FA9H   LINE      CODE     ---       #429
      00FF4FC1H   LINE      CODE     ---       #430
      00FF4FC1H   LINE      CODE     ---       #435
      00FF4FD0H   LINE      CODE     ---       #436
      00FF4FDFH   LINE      CODE     ---       #438
      00FF4FE8H   LINE      CODE     ---       #439
      00FF4FFFH   LINE      CODE     ---       #440
      00FF5001H   LINE      CODE     ---       #441
      00FF501AH   LINE      CODE     ---       #442
      00FF501FH   LINE      CODE     ---       #443
      00FF501FH   LINE      CODE     ---       #445
      00FF5029H   LINE      CODE     ---       #446
      00FF5039H   LINE      CODE     ---       #447
      00FF5049H   LINE      CODE     ---       #449
      00FF504EH   LINE      CODE     ---       #450
      00FF5058H   LINE      CODE     ---       #451
      00FF5061H   LINE      CODE     ---       #452
      00FF506AH   LINE      CODE     ---       #453
      00FF5073H   LINE      CODE     ---       #455
      00FF507CH   LINE      CODE     ---       #456
      00FF5080H   LINE      CODE     ---       #458
      00FF508CH   LINE      CODE     ---       #460
      00FF5097H   LINE      CODE     ---       #462
      00FF50AAH   LINE      CODE     ---       #463
      ---         BLOCKEND  ---      ---       LVL=0

      00FF5E43H   BLOCK     CODE     ---       LVL=0
      R12         REGSYM    ---      BYTE      x
      R13         REGSYM    ---      BYTE      y
      000000BAH   SYMBOL    EDATA    LONG      dat
      R15         REGSYM    ---      BYTE      num
      R14         REGSYM    ---      BYTE      size2
      00FF5E51H   BLOCK     CODE     NEAR LAB  LVL=1
      000000BEH   SYMBOL    EDATA    ---       buff
      000000E0H   SYMBOL    EDATA    BYTE      length
      ---         BLOCKEND  ---      ---       LVL=1
      00FF5E43H   LINE      CODE     ---       #475
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 62


      00FF5E51H   LINE      CODE     ---       #476
      00FF5E51H   LINE      CODE     ---       #480
      00FF5E59H   LINE      CODE     ---       #481
      00FF5E5BH   LINE      CODE     ---       #483
      00FF5E65H   LINE      CODE     ---       #484
      00FF5E7CH   LINE      CODE     ---       #485
      00FF5E7EH   LINE      CODE     ---       #486
      00FF5E84H   LINE      CODE     ---       #487
      00FF5EA1H   LINE      CODE     ---       #488
      00FF5EA6H   LINE      CODE     ---       #489
      00FF5EA6H   LINE      CODE     ---       #491
      00FF5EABH   LINE      CODE     ---       #492
      00FF5EB5H   LINE      CODE     ---       #493
      00FF5EBEH   LINE      CODE     ---       #494
      00FF5EC4H   LINE      CODE     ---       #496
      00FF5ECBH   LINE      CODE     ---       #498
      00FF5ED8H   LINE      CODE     ---       #499
      ---         BLOCKEND  ---      ---       LVL=0

      00FF5556H   BLOCK     CODE     ---       LVL=0
      00FF5556H   LINE      CODE     ---       #502
      00FF5556H   LINE      CODE     ---       #505
      00FF555DH   LINE      CODE     ---       #507
      00FF555FH   LINE      CODE     ---       #508
      00FF5566H   LINE      CODE     ---       #509
      00FF5568H   LINE      CODE     ---       #510
      00FF556FH   LINE      CODE     ---       #511
      00FF5575H   LINE      CODE     ---       #512
      00FF557CH   LINE      CODE     ---       #513
      00FF5583H   LINE      CODE     ---       #515
      00FF558AH   LINE      CODE     ---       #516
      00FF5591H   LINE      CODE     ---       #518
      00FF5598H   LINE      CODE     ---       #519
      00FF559FH   LINE      CODE     ---       #521
      00FF55A6H   LINE      CODE     ---       #523
      00FF55ADH   LINE      CODE     ---       #524
      00FF55B4H   LINE      CODE     ---       #526
      00FF55BBH   LINE      CODE     ---       #527
      00FF55C1H   LINE      CODE     ---       #529
      00FF55C8H   LINE      CODE     ---       #530
      00FF55CFH   LINE      CODE     ---       #532
      00FF55D6H   LINE      CODE     ---       #533
      00FF55DDH   LINE      CODE     ---       #535
      00FF55E4H   LINE      CODE     ---       #536
      00FF55EBH   LINE      CODE     ---       #538
      00FF55F2H   LINE      CODE     ---       #539
      00FF55F9H   LINE      CODE     ---       #541
      00FF5600H   LINE      CODE     ---       #542
      00FF5607H   LINE      CODE     ---       #544
      00FF560EH   LINE      CODE     ---       #545
      00FF5615H   LINE      CODE     ---       #547
      00FF561CH   LINE      CODE     ---       #548
      00FF5623H   LINE      CODE     ---       #550
      00FF5626H   LINE      CODE     ---       #551
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       MKS
      00FF539AH   PUBLIC    CODE     ---       CAN_MKS_CONTROL
      00FF6009H   PUBLIC    CODE     ---       CAN_MKS_SPD_CONTROL
      00FF562DH   PUBLIC    CODE     ---       MKS_HOME
      00FF547EH   PUBLIC    CODE     ---       MKS_PID_SET
      00FF0060H   PUBLIC    CODE     ---       MKS_ALL_RUN
      000001F4H   PUBLIC    EDATA    WORD      MOTOR_state
      000001F6H   PUBLIC    EDATA    ---       long_uchar1
      000001FAH   PUBLIC    EDATA    WORD      MKS_KD
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 63


      000001FCH   PUBLIC    EDATA    WORD      MKS_KI
      000001FEH   PUBLIC    EDATA    WORD      MKS_KP
      00000200H   PUBLIC    EDATA    WORD      MKS_KV
      00000202H   PUBLIC    EDATA    ---       MKS_TX
      00000021H.4 PUBLIC    BIT      BIT       ?CAN_MKS_CONTROL?BIT
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF539AH   BLOCK     CODE     ---       LVL=0
      WR2         REGSYM    ---      WORD      Motor
      DR28        REGSYM    ---      LONG      Pos
      WR8         REGSYM    ---      WORD      Spd
      R10         REGSYM    ---      BYTE      Acc
      00000021H.4 SYMBOL    BIT      BIT       Mode
      00FF53A4H   BLOCK     CODE     NEAR LAB  LVL=1
      R15         REGSYM    ---      BYTE      crc
      ---         BLOCKEND  ---      ---       LVL=1
      00FF539AH   LINE      CODE     ---       #22
      00FF53A4H   LINE      CODE     ---       #23
      00FF53A4H   LINE      CODE     ---       #25
      00FF53A4H   LINE      CODE     ---       #27
      00FF53A8H   LINE      CODE     ---       #29
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 64


      00FF53ABH   LINE      CODE     ---       #31
      00FF53B1H   LINE      CODE     ---       #32
      00FF53B7H   LINE      CODE     ---       #33
      00FF53BDH   LINE      CODE     ---       #34
      00FF53C1H   LINE      CODE     ---       #36
      00FF53D2H   LINE      CODE     ---       #37
      00FF53D2H   LINE      CODE     ---       #38
      00FF53D2H   LINE      CODE     ---       #39
      00FF53D2H   LINE      CODE     ---       #41
      00FF53D2H   LINE      CODE     ---       #42
      00FF53D2H   LINE      CODE     ---       #43
      00FF53D4H   LINE      CODE     ---       #46
      00FF53DAH   LINE      CODE     ---       #48
      00FF53E6H   LINE      CODE     ---       #49
      00FF53ECH   LINE      CODE     ---       #50
      00FF53F0H   LINE      CODE     ---       #52
      00FF5401H   LINE      CODE     ---       #53
      00FF5409H   LINE      CODE     ---       #54
      00FF5411H   LINE      CODE     ---       #55
      00FF5419H   LINE      CODE     ---       #57
      00FF544DH   LINE      CODE     ---       #58
      00FF5451H   LINE      CODE     ---       #59
      00FF5451H   LINE      CODE     ---       #61
      00FF5466H   LINE      CODE     ---       #62
      00FF547BH   LINE      CODE     ---       #64
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6009H   BLOCK     CODE     ---       LVL=0
      WR14        REGSYM    ---      WORD      Motor
      WR2         REGSYM    ---      INT       Spd
      R13         REGSYM    ---      BYTE      Acc
      00FF6013H   BLOCK     CODE     NEAR LAB  LVL=1
      R7          REGSYM    ---      BYTE      crc
      ---         BLOCKEND  ---      ---       LVL=1
      00FF6009H   LINE      CODE     ---       #65
      00FF6013H   LINE      CODE     ---       #66
      00FF6013H   LINE      CODE     ---       #68
      00FF6013H   LINE      CODE     ---       #69
      00FF6019H   LINE      CODE     ---       #70
      00FF601EH   LINE      CODE     ---       #71
      00FF602CH   LINE      CODE     ---       #72
      00FF6036H   LINE      CODE     ---       #73
      00FF603DH   LINE      CODE     ---       #74
      00FF6057H   LINE      CODE     ---       #75
      00FF6062H   LINE      CODE     ---       #76
      00FF6066H   LINE      CODE     ---       #77
      00FF6082H   LINE      CODE     ---       #78
      00FF6086H   LINE      CODE     ---       #79
      00FF609BH   LINE      CODE     ---       #81
      ---         BLOCKEND  ---      ---       LVL=0

      00FF562DH   BLOCK     CODE     ---       LVL=0
      WR14        REGSYM    ---      WORD      Motor
      WR2         REGSYM    ---      INT       Spd
      00FF5633H   BLOCK     CODE     NEAR LAB  LVL=1
      R7          REGSYM    ---      BYTE      crc
      ---         BLOCKEND  ---      ---       LVL=1
      00FF562DH   LINE      CODE     ---       #82
      00FF5633H   LINE      CODE     ---       #83
      00FF5633H   LINE      CODE     ---       #85
      00FF5639H   LINE      CODE     ---       #86
      00FF563EH   LINE      CODE     ---       #87
      00FF5648H   LINE      CODE     ---       #88
      00FF564DH   LINE      CODE     ---       #89
      00FF5654H   LINE      CODE     ---       #90
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 65


      00FF5661H   LINE      CODE     ---       #91
      00FF566CH   LINE      CODE     ---       #92
      00FF5672H   LINE      CODE     ---       #93
      00FF56A0H   LINE      CODE     ---       #94
      00FF56A4H   LINE      CODE     ---       #95
      00FF56B9H   LINE      CODE     ---       #96
      00FF56BFH   LINE      CODE     ---       #97
      00FF56C5H   LINE      CODE     ---       #98
      00FF56C9H   LINE      CODE     ---       #99
      00FF56E0H   LINE      CODE     ---       #100
      00FF56F3H   LINE      CODE     ---       #101
      ---         BLOCKEND  ---      ---       LVL=0

      00FF547EH   BLOCK     CODE     ---       LVL=0
      WR14        REGSYM    ---      WORD      Motor
      00FF5482H   BLOCK     CODE     NEAR LAB  LVL=1
      R7          REGSYM    ---      BYTE      crc
      ---         BLOCKEND  ---      ---       LVL=1
      00FF547EH   LINE      CODE     ---       #104
      00FF5482H   LINE      CODE     ---       #105
      00FF5482H   LINE      CODE     ---       #107
      00FF5482H   LINE      CODE     ---       #109
      00FF5488H   LINE      CODE     ---       #110
      00FF548DH   LINE      CODE     ---       #111
      00FF5495H   LINE      CODE     ---       #112
      00FF5499H   LINE      CODE     ---       #113
      00FF54A1H   LINE      CODE     ---       #114
      00FF54A5H   LINE      CODE     ---       #115
      00FF54D1H   LINE      CODE     ---       #116
      00FF54D5H   LINE      CODE     ---       #117
      00FF54EAH   LINE      CODE     ---       #120
      00FF54F0H   LINE      CODE     ---       #121
      00FF54F6H   LINE      CODE     ---       #122
      00FF54FEH   LINE      CODE     ---       #123
      00FF5502H   LINE      CODE     ---       #124
      00FF550AH   LINE      CODE     ---       #125
      00FF550EH   LINE      CODE     ---       #126
      00FF553AH   LINE      CODE     ---       #127
      00FF553EH   LINE      CODE     ---       #128
      00FF5553H   LINE      CODE     ---       #130
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0060H   BLOCK     CODE     ---       LVL=0
      00FF0060H   LINE      CODE     ---       #132
      00FF0060H   LINE      CODE     ---       #135
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       D2Car
      00FF75DEH   PUBLIC    CODE     ---       ESD_M1_SPD_CONTROL
      00FF66AEH   PUBLIC    CODE     ---       ESD_M1_POS_CONTROL
      00FF7643H   PUBLIC    CODE     ---       ESD_M1_PWM_CONTROL
      00FF587DH   PUBLIC    CODE     ---       CAR_PID_CONTROL
      00FF4C83H   PUBLIC    CODE     ---       CAR_ADD_ANGLE
      0000010CH   PUBLIC    EDATA    INT       ACC_X
      0000010EH   PUBLIC    EDATA    INT       ACC_Y
      00000110H   PUBLIC    EDATA    INT       ACC_Z
      00000112H   PUBLIC    EDATA    FLOAT     CAR_ANG
      00000116H   PUBLIC    EDATA    ---       D2long_uchar
      0000011AH   PUBLIC    EDATA    FLOAT     ANG_ERR
      0000011EH   PUBLIC    EDATA    FLOAT     SET_ANG
      00000122H   PUBLIC    EDATA    INT       GYRO_Z_OFFSET
      00000124H   PUBLIC    EDATA    FLOAT     ANG_OUT
      00000128H   PUBLIC    EDATA    ---       D2_CAN_TX
      00000130H   PUBLIC    EDATA    ---       D2int_uchar
      00000132H   PUBLIC    EDATA    INT       GYRO_Z_OFFSET_ADD
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 66


      00000134H   PUBLIC    EDATA    BYTE      GYRO_Z_OFFSET_N
      00000135H   PUBLIC    EDATA    FLOAT     ANG_ERR_OLD
      00000139H   PUBLIC    EDATA    ---       MPU_data
      00000147H   PUBLIC    EDATA    FLOAT     GYRO_X
      0000014BH   PUBLIC    EDATA    FLOAT     GYRO_Y
      0000014FH   PUBLIC    EDATA    FLOAT     GYRO_Z
      00000021H.5 PUBLIC    BIT      BIT       ?ESD_M1_POS_CONTROL?BIT
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF587DH   BLOCK     CODE     ---       LVL=0
      00FF587DH   LINE      CODE     ---       #38
      00FF587DH   LINE      CODE     ---       #40
      00FF5886H   LINE      CODE     ---       #42
      00FF588BH   LINE      CODE     ---       #43
      00FF5897H   LINE      CODE     ---       #44
      00FF5898H   LINE      CODE     ---       #47
      00FF58B2H   LINE      CODE     ---       #48
      00FF58DCH   LINE      CODE     ---       #49
      00FF58EDH   LINE      CODE     ---       #50
      00FF5915H   LINE      CODE     ---       #52
      00FF5920H   LINE      CODE     ---       #53
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 67


      00FF5935H   LINE      CODE     ---       #55
      00FF593DH   LINE      CODE     ---       #56
      00FF593DH   LINE      CODE     ---       #57
      ---         BLOCKEND  ---      ---       LVL=0

      00FF4C83H   BLOCK     CODE     ---       LVL=0
      DR28        REGSYM    ---      FLOAT     SET_ANGLE
      00FF4C87H   BLOCK     CODE     NEAR LAB  LVL=1
      R15         REGSYM    ---      BYTE      n
      ---         BLOCKEND  ---      ---       LVL=1
      00FF4C83H   LINE      CODE     ---       #60
      00FF4C87H   LINE      CODE     ---       #61
      00FF4C87H   LINE      CODE     ---       #62
      00FF4C8AH   LINE      CODE     ---       #64
      00FF4C97H   LINE      CODE     ---       #65
      00FF4C97H   LINE      CODE     ---       #66
      00FF4C99H   LINE      CODE     ---       #68
      00FF4CAEH   LINE      CODE     ---       #69
      00FF4CC3H   LINE      CODE     ---       #70
      00FF4CD8H   LINE      CODE     ---       #71
      00FF4CEDH   LINE      CODE     ---       #72
      00FF4CF4H   LINE      CODE     ---       #74
      00FF4D06H   LINE      CODE     ---       #75
      00FF4D08H   LINE      CODE     ---       #77
      00FF4D1DH   LINE      CODE     ---       #78
      00FF4D32H   LINE      CODE     ---       #79
      00FF4D47H   LINE      CODE     ---       #80
      00FF4D5CH   LINE      CODE     ---       #81
      00FF4D63H   LINE      CODE     ---       #83
      00FF4D6BH   LINE      CODE     ---       #84
      00FF4D75H   LINE      CODE     ---       #85
      00FF4D7FH   LINE      CODE     ---       #86
      00FF4D89H   LINE      CODE     ---       #87
      00FF4D93H   LINE      CODE     ---       #88
      ---         BLOCKEND  ---      ---       LVL=0

      00FF66AEH   BLOCK     CODE     ---       LVL=0
      WR2         REGSYM    ---      WORD      Motor
      DR28        REGSYM    ---      LONG      Pos
      WR4         REGSYM    ---      WORD      Spd
      R10         REGSYM    ---      BYTE      Acc
      00000021H.5 SYMBOL    BIT      BIT       Mode
      00FF66AEH   LINE      CODE     ---       #91
      00FF66B4H   LINE      CODE     ---       #93
      00FF66BBH   LINE      CODE     ---       #94
      00FF66C1H   LINE      CODE     ---       #96
      00FF66C5H   LINE      CODE     ---       #97
      00FF66CBH   LINE      CODE     ---       #99
      00FF66CFH   LINE      CODE     ---       #101
      00FF66D3H   LINE      CODE     ---       #103
      00FF66DBH   LINE      CODE     ---       #104
      00FF66E3H   LINE      CODE     ---       #105
      00FF66EBH   LINE      CODE     ---       #106
      00FF66F3H   LINE      CODE     ---       #108
      00FF6708H   LINE      CODE     ---       #110
      ---         BLOCKEND  ---      ---       LVL=0

      00FF75DEH   BLOCK     CODE     ---       LVL=0
      WR8         REGSYM    ---      WORD      Motor
      WR4         REGSYM    ---      INT       Spd
      R10         REGSYM    ---      BYTE      Acc
      00FF75DEH   LINE      CODE     ---       #114
      00FF75E0H   LINE      CODE     ---       #116
      00FF75E6H   LINE      CODE     ---       #118
      00FF75EAH   LINE      CODE     ---       #120
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 68


      00FF75F2H   LINE      CODE     ---       #121
      00FF75FAH   LINE      CODE     ---       #122
      00FF75FEH   LINE      CODE     ---       #124
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7643H   BLOCK     CODE     ---       LVL=0
      R10         REGSYM    ---      BYTE      Motor
      WR6         REGSYM    ---      INT       PWM_dat
      00FF7643H   LINE      CODE     ---       #127
      00FF7645H   LINE      CODE     ---       #129
      00FF764BH   LINE      CODE     ---       #131
      00FF764FH   LINE      CODE     ---       #133
      00FF7657H   LINE      CODE     ---       #134
      00FF765FH   LINE      CODE     ---       #136
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ADC
      00FF671DH   PUBLIC    CODE     ---       ADC_init
      00FF70A9H   PUBLIC    CODE     ---       ADC_get
      00000094H   SFRSYM    DATA     BYTE      P0M0
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000BEH   SFRSYM    DATA     BYTE      ADC_RESL
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000BCH.5 SFRSYM    DATA     BIT       ADC_FLAG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      000000BCH.6 SFRSYM    DATA     BIT       ADC_START
      000000BCH.7 SFRSYM    DATA     BIT       ADC_POWER
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000BDH   SFRSYM    DATA     BYTE      ADC_RES
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 69


      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0
      00000091H   SFRSYM    DATA     BYTE      P1M1
      00000092H   SFRSYM    DATA     BYTE      P1M0
      00000093H   SFRSYM    DATA     BYTE      P0M1

      00FF671DH   BLOCK     CODE     ---       LVL=0
      WR2         REGSYM    ---      INT       adcn
      WR0         REGSYM    ---      INT       speed
      00FF671DH   LINE      CODE     ---       #6
      00FF6721H   LINE      CODE     ---       #8
      00FF6724H   LINE      CODE     ---       #10
      00FF6727H   LINE      CODE     ---       #11
      00FF672DH   LINE      CODE     ---       #12
      00FF673AH   LINE      CODE     ---       #15
      00FF6748H   LINE      CODE     ---       #18
      00FF675DH   LINE      CODE     ---       #19
      00FF6761H   LINE      CODE     ---       #20
      00FF6763H   LINE      CODE     ---       #21
      00FF6767H   LINE      CODE     ---       #24
      00FF677CH   LINE      CODE     ---       #25
      00FF6780H   LINE      CODE     ---       #26
      00FF6780H   LINE      CODE     ---       #28
      00FF6786H   LINE      CODE     ---       #30
      00FF6789H   LINE      CODE     ---       #31
      ---         BLOCKEND  ---      ---       LVL=0

      00FF70A9H   BLOCK     CODE     ---       LVL=0
      WR6         REGSYM    ---      INT       adcn
      WR6         REGSYM    ---      WORD      adc_value
      00FF70A9H   LINE      CODE     ---       #33
      00FF70A9H   LINE      CODE     ---       #34
      00FF70A9H   LINE      CODE     ---       #37
      00FF70ACH   LINE      CODE     ---       #38
      00FF70B0H   LINE      CODE     ---       #40
      00FF70B3H   LINE      CODE     ---       #41
      00FF70B7H   LINE      CODE     ---       #42
      00FF70BBH   LINE      CODE     ---       #43
      00FF70BEH   LINE      CODE     ---       #45
      00FF70C0H   LINE      CODE     ---       #46
      00FF70C4H   LINE      CODE     ---       #47
      00FF70CAH   LINE      CODE     ---       #49
      00FF70CDH   LINE      CODE     ---       #50
      00FF70D0H   LINE      CODE     ---       #54
      00FF70EDH   LINE      CODE     ---       #56
      00FF70EDH   LINE      CODE     ---       #57
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       Delay
      00FF807AH   PUBLIC    CODE     ---       Delay_X_mS
      00FF8091H   PUBLIC    CODE     ---       Delay_X_uS
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 70


      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF807AH   BLOCK     CODE     ---       LVL=0
      WR4         REGSYM    ---      WORD      ms
      00FF807CH   BLOCK     CODE     NEAR LAB  LVL=1
      WR6         REGSYM    ---      WORD      i
      ---         BLOCKEND  ---      ---       LVL=1
      00FF807AH   LINE      CODE     ---       #3
      00FF807CH   LINE      CODE     ---       #4
      00FF807CH   LINE      CODE     ---       #6
      00FF807CH   LINE      CODE     ---       #7
      00FF8080H   LINE      CODE     ---       #8
      00FF8088H   LINE      CODE     ---       #9
      00FF8090H   LINE      CODE     ---       #10
      ---         BLOCKEND  ---      ---       LVL=0

      00FF8091H   BLOCK     CODE     ---       LVL=0
      WR4         REGSYM    ---      WORD      us
      00FF8093H   BLOCK     CODE     NEAR LAB  LVL=1
      WR6         REGSYM    ---      WORD      i
      ---         BLOCKEND  ---      ---       LVL=1
      00FF8091H   LINE      CODE     ---       #11
      00FF8093H   LINE      CODE     ---       #12
      00FF8093H   LINE      CODE     ---       #14
      00FF8093H   LINE      CODE     ---       #15
      00FF8097H   LINE      CODE     ---       #16
      00FF809FH   LINE      CODE     ---       #17
      00FF80A7H   LINE      CODE     ---       #18
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       GPIO
      00FF3718H   PUBLIC    CODE     ---       Get_IO
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 71


      00FF434CH   PUBLIC    CODE     ---       GPIO_init_8pin
      00FF699EH   PUBLIC    CODE     ---       GPIO_init_allpin
      00FF3BC9H   PUBLIC    CODE     ---       Out_IO
      00FF4D96H   PUBLIC    CODE     ---       GPIO_isr_deinit
      00FF340AH   PUBLIC    CODE     ---       GPIO_init_pin
      00FF2739H   PUBLIC    CODE     ---       GPIO_isr_init
      00FF41A5H   PUBLIC    CODE     ---       GPIO_pull_pin
      00000021H.7 PUBLIC    BIT      BIT       ?Out_IO?BIT
      00000094H   SFRSYM    DATA     BYTE      P0M0
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      000000F8H.7 SFRSYM    DATA     BIT       P77
      000000E8H.7 SFRSYM    DATA     BIT       P67
      000000F8H.6 SFRSYM    DATA     BIT       P76
      000000E8H.6 SFRSYM    DATA     BIT       P66
      000000F8H.5 SFRSYM    DATA     BIT       P75
      000000E8H.5 SFRSYM    DATA     BIT       P65
      000000F8H.4 SFRSYM    DATA     BIT       P74
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000C8H.5 SFRSYM    DATA     BIT       P55
      000000E8H.4 SFRSYM    DATA     BIT       P64
      000000F8H.3 SFRSYM    DATA     BIT       P73
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000C8H.4 SFRSYM    DATA     BIT       P54
      000000E8H.3 SFRSYM    DATA     BIT       P63
      000000F8H.2 SFRSYM    DATA     BIT       P72
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000A0H.6 SFRSYM    DATA     BIT       P26
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000C0H.4 SFRSYM    DATA     BIT       P44
      000000C8H.3 SFRSYM    DATA     BIT       P53
      000000E8H.2 SFRSYM    DATA     BIT       P62
      000000F8H.1 SFRSYM    DATA     BIT       P71
      00000080H.7 SFRSYM    DATA     BIT       P07
      00000090H.6 SFRSYM    DATA     BIT       P16
      000000A0H.5 SFRSYM    DATA     BIT       P25
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000C0H.3 SFRSYM    DATA     BIT       P43
      000000C8H.2 SFRSYM    DATA     BIT       P52
      000000E8H.1 SFRSYM    DATA     BIT       P61
      000000F8H.0 SFRSYM    DATA     BIT       P70
      00000080H.6 SFRSYM    DATA     BIT       P06
      00000090H.5 SFRSYM    DATA     BIT       P15
      000000A0H.4 SFRSYM    DATA     BIT       P24
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000C0H.2 SFRSYM    DATA     BIT       P42
      000000C8H.1 SFRSYM    DATA     BIT       P51
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 72


      000000E8H.0 SFRSYM    DATA     BIT       P60
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000090H.4 SFRSYM    DATA     BIT       P14
      000000A0H.3 SFRSYM    DATA     BIT       P23
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000C0H.1 SFRSYM    DATA     BIT       P41
      000000C8H.0 SFRSYM    DATA     BIT       P50
      00000080H.4 SFRSYM    DATA     BIT       P04
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000A0H.2 SFRSYM    DATA     BIT       P22
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000C0H.0 SFRSYM    DATA     BIT       P40
      00000080H.3 SFRSYM    DATA     BIT       P03
      00000090H.2 SFRSYM    DATA     BIT       P12
      000000A0H.1 SFRSYM    DATA     BIT       P21
      000000B0H.0 SFRSYM    DATA     BIT       P30
      00000080H.2 SFRSYM    DATA     BIT       P02
      00000090H.1 SFRSYM    DATA     BIT       P11
      000000A0H.0 SFRSYM    DATA     BIT       P20
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000080H.1 SFRSYM    DATA     BIT       P01
      00000087H   SFRSYM    DATA     BYTE      PCON
      00000090H.0 SFRSYM    DATA     BIT       P10
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      00000080H.0 SFRSYM    DATA     BIT       P00
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E1H   SFRSYM    DATA     BYTE      P7M1
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000CBH   SFRSYM    DATA     BYTE      P6M1
      000000E2H   SFRSYM    DATA     BYTE      P7M0
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000C9H   SFRSYM    DATA     BYTE      P5M1
      000000CCH   SFRSYM    DATA     BYTE      P6M0
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000B3H   SFRSYM    DATA     BYTE      P4M1
      000000CAH   SFRSYM    DATA     BYTE      P5M0
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B1H   SFRSYM    DATA     BYTE      P3M1
      000000B4H   SFRSYM    DATA     BYTE      P4M0
      00000090H   SFRSYM    DATA     BYTE      P1
      00000095H   SFRSYM    DATA     BYTE      P2M1
      000000B2H   SFRSYM    DATA     BYTE      P3M0
      00000080H   SFRSYM    DATA     BYTE      P0
      00000091H   SFRSYM    DATA     BYTE      P1M1
      00000096H   SFRSYM    DATA     BYTE      P2M0
      00000092H   SFRSYM    DATA     BYTE      P1M0
      00000093H   SFRSYM    DATA     BYTE      P0M1
      00FF8539H   SYMBOL    HCONST   ---       ?tpl?0001
      00FF8541H   SYMBOL    HCONST   ---       ?tpl?0002
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 73


      00FF8549H   SYMBOL    HCONST   ---       ?tpl?0003

      00FF340AH   BLOCK     CODE     ---       LVL=0
      R14         REGSYM    ---      BYTE      pin
      R15         REGSYM    ---      BYTE      mode
      00FF3410H   BLOCK     CODE     NEAR LAB  LVL=1
      R10         REGSYM    ---      BYTE      a
      R7          REGSYM    ---      BYTE      b
      00000282H   SYMBOL    EDATA    ---       c
      ---         BLOCKEND  ---      ---       LVL=1
      00FF340AH   LINE      CODE     ---       #2
      00FF3410H   LINE      CODE     ---       #3
      00FF3410H   LINE      CODE     ---       #5
      00FF342FH   LINE      CODE     ---       #6
      00FF3439H   LINE      CODE     ---       #7
      00FF3444H   LINE      CODE     ---       #8
      00FF346EH   LINE      CODE     ---       #10
      00FF346EH   LINE      CODE     ---       #11
      00FF3480H   LINE      CODE     ---       #13
      00FF348CH   LINE      CODE     ---       #14
      00FF349FH   LINE      CODE     ---       #15
      00FF34B0H   LINE      CODE     ---       #16
      00FF34C1H   LINE      CODE     ---       #17
      00FF34C1H   LINE      CODE     ---       #18
      00FF34C4H   LINE      CODE     ---       #19
      00FF34C4H   LINE      CODE     ---       #20
      00FF34D6H   LINE      CODE     ---       #22
      00FF34E2H   LINE      CODE     ---       #23
      00FF34F5H   LINE      CODE     ---       #24
      00FF3506H   LINE      CODE     ---       #25
      00FF3517H   LINE      CODE     ---       #26
      00FF3517H   LINE      CODE     ---       #27
      00FF351AH   LINE      CODE     ---       #28
      00FF351AH   LINE      CODE     ---       #29
      00FF352CH   LINE      CODE     ---       #31
      00FF3538H   LINE      CODE     ---       #32
      00FF354BH   LINE      CODE     ---       #33
      00FF355CH   LINE      CODE     ---       #34
      00FF356DH   LINE      CODE     ---       #35
      00FF356DH   LINE      CODE     ---       #36
      00FF3570H   LINE      CODE     ---       #37
      00FF3570H   LINE      CODE     ---       #38
      00FF3582H   LINE      CODE     ---       #40
      00FF358EH   LINE      CODE     ---       #41
      00FF35A1H   LINE      CODE     ---       #42
      00FF35B2H   LINE      CODE     ---       #43
      00FF35C3H   LINE      CODE     ---       #44
      00FF35C3H   LINE      CODE     ---       #45
      00FF35C6H   LINE      CODE     ---       #46
      00FF35C6H   LINE      CODE     ---       #47
      00FF35D8H   LINE      CODE     ---       #49
      00FF35E4H   LINE      CODE     ---       #50
      00FF35F7H   LINE      CODE     ---       #51
      00FF3608H   LINE      CODE     ---       #52
      00FF3619H   LINE      CODE     ---       #53
      00FF3619H   LINE      CODE     ---       #54
      00FF361CH   LINE      CODE     ---       #55
      00FF361CH   LINE      CODE     ---       #56
      00FF362EH   LINE      CODE     ---       #58
      00FF363AH   LINE      CODE     ---       #59
      00FF364DH   LINE      CODE     ---       #60
      00FF365EH   LINE      CODE     ---       #61
      00FF366FH   LINE      CODE     ---       #62
      00FF366FH   LINE      CODE     ---       #63
      00FF3672H   LINE      CODE     ---       #64
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 74


      00FF3672H   LINE      CODE     ---       #65
      00FF3684H   LINE      CODE     ---       #67
      00FF3690H   LINE      CODE     ---       #68
      00FF36A2H   LINE      CODE     ---       #69
      00FF36B2H   LINE      CODE     ---       #70
      00FF36C3H   LINE      CODE     ---       #71
      00FF36C3H   LINE      CODE     ---       #72
      00FF36C5H   LINE      CODE     ---       #73
      00FF36C5H   LINE      CODE     ---       #74
      00FF36D6H   LINE      CODE     ---       #76
      00FF36E2H   LINE      CODE     ---       #77
      00FF36F4H   LINE      CODE     ---       #78
      00FF3704H   LINE      CODE     ---       #79
      00FF3715H   LINE      CODE     ---       #80
      00FF3715H   LINE      CODE     ---       #81
      00FF3715H   LINE      CODE     ---       #82
      00FF3715H   LINE      CODE     ---       #83
      ---         BLOCKEND  ---      ---       LVL=0

      00FF434CH   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      pin
      R3          REGSYM    ---      BYTE      mode
      00FF434CH   LINE      CODE     ---       #86
      00FF434EH   LINE      CODE     ---       #88
      00FF4381H   LINE      CODE     ---       #90
      00FF4381H   LINE      CODE     ---       #92
      00FF4393H   LINE      CODE     ---       #94
      00FF4398H   LINE      CODE     ---       #95
      00FF439FH   LINE      CODE     ---       #96
      00FF43A4H   LINE      CODE     ---       #97
      00FF43AAH   LINE      CODE     ---       #98
      00FF43AAH   LINE      CODE     ---       #99
      00FF43ABH   LINE      CODE     ---       #100
      00FF43ABH   LINE      CODE     ---       #102
      00FF43BDH   LINE      CODE     ---       #104
      00FF43C2H   LINE      CODE     ---       #105
      00FF43C9H   LINE      CODE     ---       #106
      00FF43CEH   LINE      CODE     ---       #107
      00FF43D4H   LINE      CODE     ---       #108
      00FF43D4H   LINE      CODE     ---       #109
      00FF43D5H   LINE      CODE     ---       #110
      00FF43D5H   LINE      CODE     ---       #112
      00FF43E7H   LINE      CODE     ---       #114
      00FF43ECH   LINE      CODE     ---       #115
      00FF43F3H   LINE      CODE     ---       #116
      00FF43F8H   LINE      CODE     ---       #117
      00FF43FEH   LINE      CODE     ---       #118
      00FF43FEH   LINE      CODE     ---       #119
      00FF43FFH   LINE      CODE     ---       #120
      00FF43FFH   LINE      CODE     ---       #122
      00FF4411H   LINE      CODE     ---       #124
      00FF4416H   LINE      CODE     ---       #125
      00FF441DH   LINE      CODE     ---       #126
      00FF4422H   LINE      CODE     ---       #127
      00FF4428H   LINE      CODE     ---       #128
      00FF4428H   LINE      CODE     ---       #129
      00FF4429H   LINE      CODE     ---       #130
      00FF4429H   LINE      CODE     ---       #132
      00FF443BH   LINE      CODE     ---       #134
      00FF4440H   LINE      CODE     ---       #135
      00FF4447H   LINE      CODE     ---       #136
      00FF444CH   LINE      CODE     ---       #137
      00FF4452H   LINE      CODE     ---       #138
      00FF4452H   LINE      CODE     ---       #139
      00FF4453H   LINE      CODE     ---       #140
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 75


      00FF4453H   LINE      CODE     ---       #142
      00FF4462H   LINE      CODE     ---       #144
      00FF4467H   LINE      CODE     ---       #145
      00FF446EH   LINE      CODE     ---       #146
      00FF4473H   LINE      CODE     ---       #147
      00FF4479H   LINE      CODE     ---       #148
      00FF4479H   LINE      CODE     ---       #149
      00FF447AH   LINE      CODE     ---       #150
      00FF447AH   LINE      CODE     ---       #152
      00FF4489H   LINE      CODE     ---       #154
      00FF448EH   LINE      CODE     ---       #155
      00FF4495H   LINE      CODE     ---       #156
      00FF449AH   LINE      CODE     ---       #157
      00FF44A0H   LINE      CODE     ---       #158
      00FF44A0H   LINE      CODE     ---       #159
      00FF44A1H   LINE      CODE     ---       #160
      00FF44A1H   LINE      CODE     ---       #162
      00FF44B2H   LINE      CODE     ---       #164
      00FF44B7H   LINE      CODE     ---       #165
      00FF44BEH   LINE      CODE     ---       #166
      00FF44C3H   LINE      CODE     ---       #167
      00FF44C9H   LINE      CODE     ---       #168
      00FF44C9H   LINE      CODE     ---       #169
      00FF44C9H   LINE      CODE     ---       #170
      00FF44C9H   LINE      CODE     ---       #171
      ---         BLOCKEND  ---      ---       LVL=0

      00FF699EH   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      mode
      00FF69A4H   BLOCK     CODE     NEAR LAB  LVL=1
      R13         REGSYM    ---      BYTE      a
      R14         REGSYM    ---      BYTE      b
      ---         BLOCKEND  ---      ---       LVL=1
      00FF699EH   LINE      CODE     ---       #173
      00FF69A4H   LINE      CODE     ---       #174
      00FF69A4H   LINE      CODE     ---       #176
      00FF69A7H   LINE      CODE     ---       #177
      00FF69B8H   LINE      CODE     ---       #179
      00FF69B8H   LINE      CODE     ---       #181
      00FF69BAH   LINE      CODE     ---       #182
      00FF69BCH   LINE      CODE     ---       #183
      00FF69BEH   LINE      CODE     ---       #184
      00FF69BEH   LINE      CODE     ---       #186
      00FF69C1H   LINE      CODE     ---       #187
      00FF69C3H   LINE      CODE     ---       #188
      00FF69C5H   LINE      CODE     ---       #189
      00FF69C5H   LINE      CODE     ---       #191
      00FF69C7H   LINE      CODE     ---       #192
      00FF69C7H   LINE      CODE     ---       #193
      00FF69C9H   LINE      CODE     ---       #194
      00FF69C9H   LINE      CODE     ---       #196
      00FF69CCH   LINE      CODE     ---       #197
      00FF69CFH   LINE      CODE     ---       #198
      00FF69CFH   LINE      CODE     ---       #199
      00FF69CFH   LINE      CODE     ---       #200
      00FF69D2H   LINE      CODE     ---       #201
      00FF69D5H   LINE      CODE     ---       #202
      00FF69D8H   LINE      CODE     ---       #203
      00FF69DBH   LINE      CODE     ---       #204
      00FF69DEH   LINE      CODE     ---       #205
      00FF69E1H   LINE      CODE     ---       #206
      00FF69E4H   LINE      CODE     ---       #207
      00FF69E7H   LINE      CODE     ---       #208
      00FF69EAH   LINE      CODE     ---       #209
      00FF69EDH   LINE      CODE     ---       #210
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 76


      00FF69F0H   LINE      CODE     ---       #211
      00FF69F3H   LINE      CODE     ---       #212
      00FF69F6H   LINE      CODE     ---       #213
      00FF69F9H   LINE      CODE     ---       #214
      00FF69FCH   LINE      CODE     ---       #215
      00FF69FFH   LINE      CODE     ---       #217
      ---         BLOCKEND  ---      ---       LVL=0

      00FF41A5H   BLOCK     CODE     ---       LVL=0
      R14         REGSYM    ---      BYTE      pin
      R15         REGSYM    ---      BYTE      mode
      00FF41ABH   BLOCK     CODE     NEAR LAB  LVL=1
      R10         REGSYM    ---      BYTE      a
      R7          REGSYM    ---      BYTE      b
      0000028AH   SYMBOL    EDATA    ---       c
      ---         BLOCKEND  ---      ---       LVL=1
      00FF41A5H   LINE      CODE     ---       #219
      00FF41ABH   LINE      CODE     ---       #220
      00FF41ABH   LINE      CODE     ---       #222
      00FF41CAH   LINE      CODE     ---       #223
      00FF41D4H   LINE      CODE     ---       #224
      00FF41DFH   LINE      CODE     ---       #226
      00FF41E2H   LINE      CODE     ---       #227
      00FF420CH   LINE      CODE     ---       #229
      00FF420CH   LINE      CODE     ---       #230
      00FF4217H   LINE      CODE     ---       #232
      00FF4224H   LINE      CODE     ---       #233
      00FF4230H   LINE      CODE     ---       #234
      00FF4230H   LINE      CODE     ---       #235
      00FF4233H   LINE      CODE     ---       #236
      00FF4233H   LINE      CODE     ---       #237
      00FF423EH   LINE      CODE     ---       #239
      00FF424BH   LINE      CODE     ---       #240
      00FF4257H   LINE      CODE     ---       #241
      00FF4257H   LINE      CODE     ---       #242
      00FF425AH   LINE      CODE     ---       #243
      00FF425AH   LINE      CODE     ---       #244
      00FF4265H   LINE      CODE     ---       #246
      00FF4272H   LINE      CODE     ---       #247
      00FF427EH   LINE      CODE     ---       #248
      00FF427EH   LINE      CODE     ---       #249
      00FF4281H   LINE      CODE     ---       #250
      00FF4281H   LINE      CODE     ---       #251
      00FF428CH   LINE      CODE     ---       #253
      00FF4299H   LINE      CODE     ---       #254
      00FF42A5H   LINE      CODE     ---       #255
      00FF42A5H   LINE      CODE     ---       #256
      00FF42A8H   LINE      CODE     ---       #257
      00FF42A8H   LINE      CODE     ---       #258
      00FF42B3H   LINE      CODE     ---       #260
      00FF42BFH   LINE      CODE     ---       #261
      00FF42CBH   LINE      CODE     ---       #262
      00FF42CBH   LINE      CODE     ---       #263
      00FF42CDH   LINE      CODE     ---       #264
      00FF42CDH   LINE      CODE     ---       #265
      00FF42D5H   LINE      CODE     ---       #267
      00FF42E1H   LINE      CODE     ---       #268
      00FF42EDH   LINE      CODE     ---       #269
      00FF42EDH   LINE      CODE     ---       #270
      00FF42EFH   LINE      CODE     ---       #271
      00FF42EFH   LINE      CODE     ---       #272
      00FF42F7H   LINE      CODE     ---       #274
      00FF4303H   LINE      CODE     ---       #275
      00FF430FH   LINE      CODE     ---       #276
      00FF430FH   LINE      CODE     ---       #277
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 77


      00FF4311H   LINE      CODE     ---       #278
      00FF4311H   LINE      CODE     ---       #279
      00FF4319H   LINE      CODE     ---       #281
      00FF432EH   LINE      CODE     ---       #282
      00FF4346H   LINE      CODE     ---       #283
      00FF4346H   LINE      CODE     ---       #284
      00FF4346H   LINE      CODE     ---       #285
      00FF4346H   LINE      CODE     ---       #286
      00FF4349H   LINE      CODE     ---       #287
      ---         BLOCKEND  ---      ---       LVL=0

      00FF2739H   BLOCK     CODE     ---       LVL=0
      R14         REGSYM    ---      BYTE      pin
      R15         REGSYM    ---      BYTE      mode
      00FF273FH   BLOCK     CODE     NEAR LAB  LVL=1
      R7          REGSYM    ---      BYTE      psw2_old
      R10         REGSYM    ---      BYTE      a
      R6          REGSYM    ---      BYTE      b
      00000292H   SYMBOL    EDATA    ---       c
      ---         BLOCKEND  ---      ---       LVL=1
      00FF2739H   LINE      CODE     ---       #290
      00FF273FH   LINE      CODE     ---       #291
      00FF273FH   LINE      CODE     ---       #294
      00FF275EH   LINE      CODE     ---       #295
      00FF2768H   LINE      CODE     ---       #296
      00FF2773H   LINE      CODE     ---       #299
      00FF2776H   LINE      CODE     ---       #300
      00FF2779H   LINE      CODE     ---       #302
      00FF27A3H   LINE      CODE     ---       #304
      00FF27A3H   LINE      CODE     ---       #305
      00FF27B5H   LINE      CODE     ---       #307
      00FF27CAH   LINE      CODE     ---       #308
      00FF27F3H   LINE      CODE     ---       #309
      00FF2816H   LINE      CODE     ---       #310
      00FF283AH   LINE      CODE     ---       #311
      00FF283AH   LINE      CODE     ---       #312
      00FF284BH   LINE      CODE     ---       #313
      00FF284EH   LINE      CODE     ---       #314
      00FF284EH   LINE      CODE     ---       #315
      00FF2860H   LINE      CODE     ---       #317
      00FF2875H   LINE      CODE     ---       #318
      00FF289EH   LINE      CODE     ---       #319
      00FF28C1H   LINE      CODE     ---       #320
      00FF28E5H   LINE      CODE     ---       #321
      00FF28E5H   LINE      CODE     ---       #322
      00FF28F6H   LINE      CODE     ---       #323
      00FF28F9H   LINE      CODE     ---       #324
      00FF28F9H   LINE      CODE     ---       #325
      00FF290BH   LINE      CODE     ---       #327
      00FF2920H   LINE      CODE     ---       #328
      00FF2949H   LINE      CODE     ---       #329
      00FF296CH   LINE      CODE     ---       #330
      00FF2990H   LINE      CODE     ---       #331
      00FF2990H   LINE      CODE     ---       #332
      00FF29A1H   LINE      CODE     ---       #333
      00FF29A4H   LINE      CODE     ---       #334
      00FF29A4H   LINE      CODE     ---       #335
      00FF29B6H   LINE      CODE     ---       #337
      00FF29CBH   LINE      CODE     ---       #338
      00FF29F4H   LINE      CODE     ---       #339
      00FF2A17H   LINE      CODE     ---       #340
      00FF2A3BH   LINE      CODE     ---       #341
      00FF2A3BH   LINE      CODE     ---       #342
      00FF2A4CH   LINE      CODE     ---       #343
      00FF2A4FH   LINE      CODE     ---       #344
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 78


      00FF2A4FH   LINE      CODE     ---       #345
      00FF2A61H   LINE      CODE     ---       #347
      00FF2A76H   LINE      CODE     ---       #348
      00FF2A9FH   LINE      CODE     ---       #349
      00FF2AC2H   LINE      CODE     ---       #350
      00FF2AE6H   LINE      CODE     ---       #351
      00FF2AE6H   LINE      CODE     ---       #352
      00FF2AF7H   LINE      CODE     ---       #353
      00FF2AFAH   LINE      CODE     ---       #354
      00FF2AFAH   LINE      CODE     ---       #355
      00FF2B0CH   LINE      CODE     ---       #357
      00FF2B21H   LINE      CODE     ---       #358
      00FF2B4AH   LINE      CODE     ---       #359
      00FF2B6DH   LINE      CODE     ---       #360
      00FF2B91H   LINE      CODE     ---       #361
      00FF2B91H   LINE      CODE     ---       #362
      00FF2BA2H   LINE      CODE     ---       #363
      00FF2BA5H   LINE      CODE     ---       #364
      00FF2BA5H   LINE      CODE     ---       #365
      00FF2BB7H   LINE      CODE     ---       #367
      00FF2BCCH   LINE      CODE     ---       #368
      00FF2BF5H   LINE      CODE     ---       #369
      00FF2C18H   LINE      CODE     ---       #370
      00FF2C3CH   LINE      CODE     ---       #371
      00FF2C3CH   LINE      CODE     ---       #372
      00FF2C4DH   LINE      CODE     ---       #373
      00FF2C50H   LINE      CODE     ---       #374
      00FF2C50H   LINE      CODE     ---       #375
      00FF2C64H   LINE      CODE     ---       #377
      00FF2C79H   LINE      CODE     ---       #378
      00FF2CA2H   LINE      CODE     ---       #379
      00FF2CC5H   LINE      CODE     ---       #380
      00FF2CE9H   LINE      CODE     ---       #381
      00FF2CE9H   LINE      CODE     ---       #382
      00FF2D06H   LINE      CODE     ---       #383
      00FF2D06H   LINE      CODE     ---       #385
      00FF2D06H   LINE      CODE     ---       #387
      00FF2D09H   LINE      CODE     ---       #389
      ---         BLOCKEND  ---      ---       LVL=0

      00FF4D96H   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      pin
      R7          REGSYM    ---      BYTE      psw2_old
      R2          REGSYM    ---      BYTE      a
      R6          REGSYM    ---      BYTE      b
      00FF4D96H   LINE      CODE     ---       #391
      00FF4D96H   LINE      CODE     ---       #392
      00FF4D96H   LINE      CODE     ---       #396
      00FF4DA0H   LINE      CODE     ---       #397
      00FF4DABH   LINE      CODE     ---       #400
      00FF4DAEH   LINE      CODE     ---       #401
      00FF4DB1H   LINE      CODE     ---       #403
      00FF4DDBH   LINE      CODE     ---       #405
      00FF4DF3H   LINE      CODE     ---       #406
      00FF4E0BH   LINE      CODE     ---       #407
      00FF4E22H   LINE      CODE     ---       #408
      00FF4E39H   LINE      CODE     ---       #409
      00FF4E50H   LINE      CODE     ---       #410
      00FF4E67H   LINE      CODE     ---       #411
      00FF4E7EH   LINE      CODE     ---       #412
      00FF4E9FH   LINE      CODE     ---       #413
      00FF4E9FH   LINE      CODE     ---       #414
      00FF4EA2H   LINE      CODE     ---       #415
      ---         BLOCKEND  ---      ---       LVL=0

L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 79


      00FF3718H   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      IO
      00000021H.6 SYMBOL    BIT      BIT       status
      00FF3718H   LINE      CODE     ---       #417
      00FF3718H   LINE      CODE     ---       #418
      00FF3718H   LINE      CODE     ---       #420
      00FF3812H   LINE      CODE     ---       #422
      00FF3819H   LINE      CODE     ---       #423
      00FF3820H   LINE      CODE     ---       #424
      00FF3827H   LINE      CODE     ---       #425
      00FF382EH   LINE      CODE     ---       #426
      00FF3835H   LINE      CODE     ---       #427
      00FF383CH   LINE      CODE     ---       #428
      00FF3843H   LINE      CODE     ---       #429
      00FF384AH   LINE      CODE     ---       #431
      00FF3851H   LINE      CODE     ---       #432
      00FF3858H   LINE      CODE     ---       #433
      00FF385FH   LINE      CODE     ---       #434
      00FF3866H   LINE      CODE     ---       #435
      00FF386DH   LINE      CODE     ---       #436
      00FF3874H   LINE      CODE     ---       #437
      00FF387BH   LINE      CODE     ---       #438
      00FF3882H   LINE      CODE     ---       #440
      00FF3889H   LINE      CODE     ---       #441
      00FF3890H   LINE      CODE     ---       #442
      00FF3897H   LINE      CODE     ---       #443
      00FF389EH   LINE      CODE     ---       #444
      00FF38A5H   LINE      CODE     ---       #445
      00FF38ACH   LINE      CODE     ---       #446
      00FF38B3H   LINE      CODE     ---       #447
      00FF38BAH   LINE      CODE     ---       #449
      00FF38C1H   LINE      CODE     ---       #450
      00FF38C8H   LINE      CODE     ---       #451
      00FF38CFH   LINE      CODE     ---       #452
      00FF38D6H   LINE      CODE     ---       #453
      00FF38DDH   LINE      CODE     ---       #454
      00FF38E4H   LINE      CODE     ---       #455
      00FF38EBH   LINE      CODE     ---       #456
      00FF38F2H   LINE      CODE     ---       #458
      00FF38F9H   LINE      CODE     ---       #459
      00FF3900H   LINE      CODE     ---       #460
      00FF3907H   LINE      CODE     ---       #461
      00FF390EH   LINE      CODE     ---       #462
      00FF3915H   LINE      CODE     ---       #464
      00FF391CH   LINE      CODE     ---       #465
      00FF3922H   LINE      CODE     ---       #466
      00FF3928H   LINE      CODE     ---       #467
      00FF392EH   LINE      CODE     ---       #468
      00FF3934H   LINE      CODE     ---       #469
      00FF393AH   LINE      CODE     ---       #471
      00FF3940H   LINE      CODE     ---       #472
      00FF3946H   LINE      CODE     ---       #473
      00FF394CH   LINE      CODE     ---       #474
      00FF3952H   LINE      CODE     ---       #475
      00FF3958H   LINE      CODE     ---       #476
      00FF395EH   LINE      CODE     ---       #477
      00FF3964H   LINE      CODE     ---       #478
      00FF396AH   LINE      CODE     ---       #480
      00FF3970H   LINE      CODE     ---       #481
      00FF3976H   LINE      CODE     ---       #482
      00FF397CH   LINE      CODE     ---       #483
      00FF3982H   LINE      CODE     ---       #484
      00FF3988H   LINE      CODE     ---       #485
      00FF398EH   LINE      CODE     ---       #486
      00FF3994H   LINE      CODE     ---       #487
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 80


      00FF399AH   LINE      CODE     ---       #488
      00FF399CH   LINE      CODE     ---       #489
      00FF399CH   LINE      CODE     ---       #490
      00FF399EH   LINE      CODE     ---       #491
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3BC9H   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      IO
      00000021H.7 SYMBOL    BIT      BIT       status
      00FF3BC9H   LINE      CODE     ---       #492
      00FF3BC9H   LINE      CODE     ---       #494
      00FF3CC3H   LINE      CODE     ---       #496
      00FF3CC8H   LINE      CODE     ---       #497
      00FF3CCDH   LINE      CODE     ---       #498
      00FF3CD2H   LINE      CODE     ---       #499
      00FF3CD7H   LINE      CODE     ---       #500
      00FF3CDCH   LINE      CODE     ---       #501
      00FF3CE1H   LINE      CODE     ---       #502
      00FF3CE6H   LINE      CODE     ---       #503
      00FF3CEBH   LINE      CODE     ---       #505
      00FF3CF0H   LINE      CODE     ---       #506
      00FF3CF5H   LINE      CODE     ---       #507
      00FF3CFAH   LINE      CODE     ---       #508
      00FF3CFFH   LINE      CODE     ---       #509
      00FF3D04H   LINE      CODE     ---       #510
      00FF3D09H   LINE      CODE     ---       #511
      00FF3D0EH   LINE      CODE     ---       #512
      00FF3D13H   LINE      CODE     ---       #514
      00FF3D18H   LINE      CODE     ---       #515
      00FF3D1DH   LINE      CODE     ---       #516
      00FF3D22H   LINE      CODE     ---       #517
      00FF3D27H   LINE      CODE     ---       #518
      00FF3D2CH   LINE      CODE     ---       #519
      00FF3D31H   LINE      CODE     ---       #520
      00FF3D36H   LINE      CODE     ---       #521
      00FF3D3BH   LINE      CODE     ---       #523
      00FF3D40H   LINE      CODE     ---       #524
      00FF3D45H   LINE      CODE     ---       #525
      00FF3D4AH   LINE      CODE     ---       #526
      00FF3D4FH   LINE      CODE     ---       #527
      00FF3D54H   LINE      CODE     ---       #528
      00FF3D59H   LINE      CODE     ---       #529
      00FF3D5EH   LINE      CODE     ---       #530
      00FF3D63H   LINE      CODE     ---       #532
      00FF3D68H   LINE      CODE     ---       #533
      00FF3D6DH   LINE      CODE     ---       #534
      00FF3D72H   LINE      CODE     ---       #535
      00FF3D77H   LINE      CODE     ---       #536
      00FF3D7CH   LINE      CODE     ---       #538
      00FF3D81H   LINE      CODE     ---       #539
      00FF3D86H   LINE      CODE     ---       #540
      00FF3D8BH   LINE      CODE     ---       #541
      00FF3D90H   LINE      CODE     ---       #542
      00FF3D95H   LINE      CODE     ---       #543
      00FF3D9AH   LINE      CODE     ---       #545
      00FF3D9FH   LINE      CODE     ---       #546
      00FF3DA4H   LINE      CODE     ---       #547
      00FF3DA9H   LINE      CODE     ---       #548
      00FF3DAEH   LINE      CODE     ---       #549
      00FF3DB3H   LINE      CODE     ---       #550
      00FF3DB8H   LINE      CODE     ---       #551
      00FF3DBDH   LINE      CODE     ---       #552
      00FF3DC2H   LINE      CODE     ---       #554
      00FF3DC7H   LINE      CODE     ---       #555
      00FF3DCCH   LINE      CODE     ---       #556
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 81


      00FF3DD1H   LINE      CODE     ---       #557
      00FF3DD6H   LINE      CODE     ---       #558
      00FF3DDBH   LINE      CODE     ---       #559
      00FF3DE0H   LINE      CODE     ---       #560
      00FF3DE5H   LINE      CODE     ---       #561
      00FF3DE9H   LINE      CODE     ---       #562
      00FF3DE9H   LINE      CODE     ---       #563
      00FF3DE9H   LINE      CODE     ---       #564
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       IIC
      00FF7FD3H   PUBLIC    CODE     ---       ESD_IIC_Send_nack
      00FF6B1FH   PUBLIC    CODE     ---       ESD_Write_IIC
      00FF7CCCH   PUBLIC    CODE     ---       ESD_IIC_Recv_data
      00FF81DCH   PUBLIC    CODE     ---       ESD_IIC_Start
      00FF80A8H   PUBLIC    CODE     ---       ESD_IIC_WRITE_START_BYTE
      00FF5A9CH   PUBLIC    CODE     ---       ESD_Init_IIC
      00FF80BFH   PUBLIC    CODE     ---       ESD_IIC_Wait
      00FF8034H   PUBLIC    CODE     ---       ESD_IIC_Send_ack
      00FF81ECH   PUBLIC    CODE     ---       ESD_IIC_Recv_ack
      00FF81FCH   PUBLIC    CODE     ---       ESD_IIC_Stop
      00FF7CE8H   PUBLIC    CODE     ---       ESD_IIC_READ_NACK_BYTE
      00FF7D04H   PUBLIC    CODE     ---       ESD_IIC_READ_ACK_BYTE
      00FF80D6H   PUBLIC    CODE     ---       ESD_IIC_WRITE_ONE_BYTE
      00FF63E6H   PUBLIC    CODE     ---       ESD_Read_IIC
      00FF80EDH   PUBLIC    CODE     ---       ESD_IIC_Send_data
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E1H   SFRSYM    DATA     BYTE      P7M1
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 82


      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000E2H   SFRSYM    DATA     BYTE      P7M0
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B1H   SFRSYM    DATA     BYTE      P3M1
      00000090H   SFRSYM    DATA     BYTE      P1
      00000095H   SFRSYM    DATA     BYTE      P2M1
      000000B2H   SFRSYM    DATA     BYTE      P3M0
      00000080H   SFRSYM    DATA     BYTE      P0
      00000091H   SFRSYM    DATA     BYTE      P1M1
      00000096H   SFRSYM    DATA     BYTE      P2M0
      00000092H   SFRSYM    DATA     BYTE      P1M0

      00FF80BFH   BLOCK     CODE     ---       LVL=0
      00FF80BFH   LINE      CODE     ---       #9
      00FF80BFH   LINE      CODE     ---       #11
      00FF80CDH   LINE      CODE     ---       #12
      00FF80D5H   LINE      CODE     ---       #13
      ---         BLOCKEND  ---      ---       LVL=0

      00FF81DCH   BLOCK     CODE     ---       LVL=0
      00FF81DCH   LINE      CODE     ---       #18
      00FF81DCH   LINE      CODE     ---       #20
      00FF81E9H   LINE      CODE     ---       #21
      ---         BLOCKEND  ---      ---       LVL=0

      00FF80EDH   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      dat
      00FF80EDH   LINE      CODE     ---       #26
      00FF80EDH   LINE      CODE     ---       #28
      00FF80F8H   LINE      CODE     ---       #29
      00FF8101H   LINE      CODE     ---       #30
      ---         BLOCKEND  ---      ---       LVL=0

      00FF81ECH   BLOCK     CODE     ---       LVL=0
      00FF81ECH   LINE      CODE     ---       #35
      00FF81ECH   LINE      CODE     ---       #37
      00FF81F9H   LINE      CODE     ---       #38
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7CCCH   BLOCK     CODE     ---       LVL=0
      00FF7CCCH   LINE      CODE     ---       #43
      00FF7CCCH   LINE      CODE     ---       #45
      00FF7CD9H   LINE      CODE     ---       #46
      00FF7CDCH   LINE      CODE     ---       #47
      00FF7CE7H   LINE      CODE     ---       #48
      ---         BLOCKEND  ---      ---       LVL=0

      00FF8034H   BLOCK     CODE     ---       LVL=0
      00FF8034H   LINE      CODE     ---       #53
      00FF8034H   LINE      CODE     ---       #55
      00FF8040H   LINE      CODE     ---       #56
      00FF8049H   LINE      CODE     ---       #57
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7FD3H   BLOCK     CODE     ---       LVL=0
      00FF7FD3H   LINE      CODE     ---       #62
      00FF7FD3H   LINE      CODE     ---       #64
      00FF7FE0H   LINE      CODE     ---       #65
      00FF7FE9H   LINE      CODE     ---       #66
      ---         BLOCKEND  ---      ---       LVL=0

      00FF81FCH   BLOCK     CODE     ---       LVL=0
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 83


      00FF81FCH   LINE      CODE     ---       #72
      00FF81FCH   LINE      CODE     ---       #74
      00FF8209H   LINE      CODE     ---       #75
      ---         BLOCKEND  ---      ---       LVL=0

      00FF80A8H   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      dat
      00FF80A8H   LINE      CODE     ---       #81
      00FF80A8H   LINE      CODE     ---       #83
      00FF80B3H   LINE      CODE     ---       #84
      00FF80BCH   LINE      CODE     ---       #85
      ---         BLOCKEND  ---      ---       LVL=0

      00FF80D6H   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      dat
      00FF80D6H   LINE      CODE     ---       #91
      00FF80D6H   LINE      CODE     ---       #93
      00FF80E1H   LINE      CODE     ---       #94
      00FF80EAH   LINE      CODE     ---       #95
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7D04H   BLOCK     CODE     ---       LVL=0
      00FF7D04H   LINE      CODE     ---       #101
      00FF7D04H   LINE      CODE     ---       #103
      00FF7D11H   LINE      CODE     ---       #104
      00FF7D14H   LINE      CODE     ---       #105
      00FF7D1FH   LINE      CODE     ---       #106
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7CE8H   BLOCK     CODE     ---       LVL=0
      00FF7CE8H   LINE      CODE     ---       #111
      00FF7CE8H   LINE      CODE     ---       #113
      00FF7CF5H   LINE      CODE     ---       #114
      00FF7CF8H   LINE      CODE     ---       #115
      00FF7D03H   LINE      CODE     ---       #116
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6B1FH   BLOCK     CODE     ---       LVL=0
      000002CBH   SYMBOL    EDATA    BYTE      dev
      000002CCH   SYMBOL    EDATA    BYTE      reg
      000002CDH   SYMBOL    EDATA    BYTE      length
      REG=3       REGSYM    ---      ---       dat
      00FF6B2FH   BLOCK     CODE     NEAR LAB  LVL=1
      000002CEH   SYMBOL    EDATA    BYTE      count
      ---         BLOCKEND  ---      ---       LVL=1
      00FF6B1FH   LINE      CODE     ---       #128
      00FF6B2FH   LINE      CODE     ---       #129
      00FF6B2FH   LINE      CODE     ---       #130
      00FF6B2FH   LINE      CODE     ---       #132
      00FF6B32H   LINE      CODE     ---       #133
      00FF6B3BH   LINE      CODE     ---       #134
      00FF6B3EH   LINE      CODE     ---       #135
      00FF6B45H   LINE      CODE     ---       #136
      00FF6B48H   LINE      CODE     ---       #137
      00FF6B4BH   LINE      CODE     ---       #139
      00FF6B5BH   LINE      CODE     ---       #140
      00FF6B5EH   LINE      CODE     ---       #141
      00FF6B71H   LINE      CODE     ---       #142
      00FF6B74H   LINE      CODE     ---       #143
      ---         BLOCKEND  ---      ---       LVL=0

      00FF63E6H   BLOCK     CODE     ---       LVL=0
      000002CFH   SYMBOL    EDATA    BYTE      dev
      000002D0H   SYMBOL    EDATA    BYTE      reg
      000002D1H   SYMBOL    EDATA    BYTE      length
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 84


      REG=3       REGSYM    ---      ---       dat
      00FF63F6H   BLOCK     CODE     NEAR LAB  LVL=1
      000002D2H   SYMBOL    EDATA    BYTE      count
      ---         BLOCKEND  ---      ---       LVL=1
      00FF63E6H   LINE      CODE     ---       #153
      00FF63F6H   LINE      CODE     ---       #154
      00FF63F6H   LINE      CODE     ---       #155
      00FF63F6H   LINE      CODE     ---       #157
      00FF63F9H   LINE      CODE     ---       #158
      00FF6402H   LINE      CODE     ---       #159
      00FF6405H   LINE      CODE     ---       #160
      00FF640CH   LINE      CODE     ---       #161
      00FF640FH   LINE      CODE     ---       #163
      00FF6412H   LINE      CODE     ---       #164
      00FF641FH   LINE      CODE     ---       #165
      00FF6422H   LINE      CODE     ---       #167
      00FF6425H   LINE      CODE     ---       #169
      00FF6435H   LINE      CODE     ---       #170
      00FF644CH   LINE      CODE     ---       #171
      00FF644FH   LINE      CODE     ---       #173
      00FF6462H   LINE      CODE     ---       #174
      00FF6465H   LINE      CODE     ---       #176
      ---         BLOCKEND  ---      ---       LVL=0

      00FF5A9CH   BLOCK     CODE     ---       LVL=0
      WR6         REGSYM    ---      INT       iic_n
      00FF5A9CH   LINE      CODE     ---       #180
      00FF5A9CH   LINE      CODE     ---       #183
      00FF5AAEH   LINE      CODE     ---       #185
      00FF5AAEH   LINE      CODE     ---       #188
      00FF5AB1H   LINE      CODE     ---       #189
      00FF5AC1H   LINE      CODE     ---       #190
      00FF5AC4H   LINE      CODE     ---       #191
      00FF5AC7H   LINE      CODE     ---       #193
      00FF5AC9H   LINE      CODE     ---       #194
      00FF5AC9H   LINE      CODE     ---       #197
      00FF5ACCH   LINE      CODE     ---       #198
      00FF5ACFH   LINE      CODE     ---       #199
      00FF5ADFH   LINE      CODE     ---       #200
      00FF5AE2H   LINE      CODE     ---       #201
      00FF5AE5H   LINE      CODE     ---       #202
      00FF5AE7H   LINE      CODE     ---       #203
      00FF5AE7H   LINE      CODE     ---       #206
      00FF5AEAH   LINE      CODE     ---       #207
      00FF5AEDH   LINE      CODE     ---       #208
      00FF5AFDH   LINE      CODE     ---       #209
      00FF5B00H   LINE      CODE     ---       #210
      00FF5B03H   LINE      CODE     ---       #211
      00FF5B05H   LINE      CODE     ---       #212
      00FF5B05H   LINE      CODE     ---       #215
      00FF5B08H   LINE      CODE     ---       #216
      00FF5B18H   LINE      CODE     ---       #217
      00FF5B1BH   LINE      CODE     ---       #218
      00FF5B1EH   LINE      CODE     ---       #219
      00FF5B1EH   LINE      CODE     ---       #220
      00FF5B1EH   LINE      CODE     ---       #222
      00FF5B2BH   LINE      CODE     ---       #223
      00FF5B33H   LINE      CODE     ---       #224
      00FF5B3BH   LINE      CODE     ---       #225
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       INT
      00FF7611H   PUBLIC    CODE     ---       INT_init
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 85


      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.0 SFRSYM    DATA     BIT       IT0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF7611H   BLOCK     CODE     ---       LVL=0
      R6          REGSYM    ---      BYTE      int_n
      R7          REGSYM    ---      BYTE      mode
      00FF7611H   LINE      CODE     ---       #2
      00FF7613H   LINE      CODE     ---       #4
      00FF7617H   LINE      CODE     ---       #6
      00FF7620H   LINE      CODE     ---       #7
      00FF7622H   LINE      CODE     ---       #8
      00FF7622H   LINE      CODE     ---       #9
      00FF7626H   LINE      CODE     ---       #11
      00FF762BH   LINE      CODE     ---       #12
      00FF762DH   LINE      CODE     ---       #13
      00FF762DH   LINE      CODE     ---       #14
      00FF7631H   LINE      CODE     ---       #16
      00FF7634H   LINE      CODE     ---       #17
      00FF7634H   LINE      CODE     ---       #18
      00FF7638H   LINE      CODE     ---       #20
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 86


      00FF763BH   LINE      CODE     ---       #21
      00FF763BH   LINE      CODE     ---       #22
      00FF763FH   LINE      CODE     ---       #24
      00FF7642H   LINE      CODE     ---       #25
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       PIT
      00FF6EA7H   PUBLIC    CODE     ---       PIT_count_get
      00FF61C9H   PUBLIC    CODE     ---       PIT_init_ms
      00FF6EF4H   PUBLIC    CODE     ---       PIT_init_encoder
      00FF6136H   PUBLIC    CODE     ---       PIT_init_us
      00FF6C74H   PUBLIC    CODE     ---       PIT_count_clean
      0000024FH   PUBLIC    EDATA    WORD      T0_cnt
      00000251H   PUBLIC    EDATA    WORD      T1_cnt
      00000253H   PUBLIC    EDATA    WORD      T2_cnt
      00000255H   PUBLIC    EDATA    WORD      T3_cnt
      00000257H   PUBLIC    EDATA    WORD      T4_cnt
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      00000088H.6 SFRSYM    DATA     BIT       TR1
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D3H   SFRSYM    DATA     BYTE      T4L
      000000D5H   SFRSYM    DATA     BYTE      T3L
      000000D7H   SFRSYM    DATA     BYTE      T2L
      0000008BH   SFRSYM    DATA     BYTE      TL1
      0000008AH   SFRSYM    DATA     BYTE      TL0
      000000D2H   SFRSYM    DATA     BYTE      T4H
      000000D4H   SFRSYM    DATA     BYTE      T3H
      000000D6H   SFRSYM    DATA     BYTE      T2H
      0000008DH   SFRSYM    DATA     BYTE      TH1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 87


      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF61C9H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      tim_n
      WR2         REGSYM    ---      WORD      time_ms
      00FF61CFH   BLOCK     CODE     NEAR LAB  LVL=1
      WR6         REGSYM    ---      WORD      temp
      ---         BLOCKEND  ---      ---       LVL=1
      00FF61C9H   LINE      CODE     ---       #10
      00FF61CFH   LINE      CODE     ---       #11
      00FF61CFH   LINE      CODE     ---       #13
      00FF61E8H   LINE      CODE     ---       #14
      00FF61ECH   LINE      CODE     ---       #16
      00FF61F0H   LINE      CODE     ---       #17
      00FF61F4H   LINE      CODE     ---       #18
      00FF61F8H   LINE      CODE     ---       #19
      00FF61FAH   LINE      CODE     ---       #20
      00FF61FCH   LINE      CODE     ---       #21
      00FF61FEH   LINE      CODE     ---       #22
      00FF6203H   LINE      CODE     ---       #24
      00FF6207H   LINE      CODE     ---       #25
      00FF620BH   LINE      CODE     ---       #26
      00FF620FH   LINE      CODE     ---       #27
      00FF6211H   LINE      CODE     ---       #28
      00FF6213H   LINE      CODE     ---       #29
      00FF6215H   LINE      CODE     ---       #30
      00FF621AH   LINE      CODE     ---       #32
      00FF621EH   LINE      CODE     ---       #33
      00FF6222H   LINE      CODE     ---       #34
      00FF6225H   LINE      CODE     ---       #35
      00FF6228H   LINE      CODE     ---       #36
      00FF622AH   LINE      CODE     ---       #37
      00FF622FH   LINE      CODE     ---       #39
      00FF6233H   LINE      CODE     ---       #40
      00FF6237H   LINE      CODE     ---       #41
      00FF623AH   LINE      CODE     ---       #42
      00FF623DH   LINE      CODE     ---       #43
      00FF623FH   LINE      CODE     ---       #44
      00FF6244H   LINE      CODE     ---       #46
      00FF6248H   LINE      CODE     ---       #47
      00FF624CH   LINE      CODE     ---       #48
      00FF624FH   LINE      CODE     ---       #49
      00FF6252H   LINE      CODE     ---       #50
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6136H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      tim_n
      WR30        REGSYM    ---      WORD      time_us
      00FF613CH   BLOCK     CODE     NEAR LAB  LVL=1
      WR6         REGSYM    ---      WORD      temp
      ---         BLOCKEND  ---      ---       LVL=1
      00FF6136H   LINE      CODE     ---       #52
      00FF613CH   LINE      CODE     ---       #53
      00FF613CH   LINE      CODE     ---       #55
      00FF615CH   LINE      CODE     ---       #56
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 88


      00FF6160H   LINE      CODE     ---       #58
      00FF6164H   LINE      CODE     ---       #59
      00FF6168H   LINE      CODE     ---       #60
      00FF616CH   LINE      CODE     ---       #61
      00FF616EH   LINE      CODE     ---       #62
      00FF6170H   LINE      CODE     ---       #63
      00FF6172H   LINE      CODE     ---       #64
      00FF6177H   LINE      CODE     ---       #66
      00FF617BH   LINE      CODE     ---       #67
      00FF617FH   LINE      CODE     ---       #68
      00FF6183H   LINE      CODE     ---       #69
      00FF6185H   LINE      CODE     ---       #70
      00FF6187H   LINE      CODE     ---       #71
      00FF6189H   LINE      CODE     ---       #72
      00FF618EH   LINE      CODE     ---       #74
      00FF6192H   LINE      CODE     ---       #75
      00FF6196H   LINE      CODE     ---       #76
      00FF6199H   LINE      CODE     ---       #77
      00FF619CH   LINE      CODE     ---       #78
      00FF619EH   LINE      CODE     ---       #79
      00FF61A3H   LINE      CODE     ---       #81
      00FF61A7H   LINE      CODE     ---       #82
      00FF61ABH   LINE      CODE     ---       #83
      00FF61AEH   LINE      CODE     ---       #84
      00FF61B1H   LINE      CODE     ---       #85
      00FF61B3H   LINE      CODE     ---       #86
      00FF61B8H   LINE      CODE     ---       #88
      00FF61BCH   LINE      CODE     ---       #89
      00FF61C0H   LINE      CODE     ---       #90
      00FF61C3H   LINE      CODE     ---       #91
      00FF61C6H   LINE      CODE     ---       #92
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6EF4H   BLOCK     CODE     ---       LVL=0
      R7          REGSYM    ---      BYTE      tim_n
      00FF6EF4H   LINE      CODE     ---       #94
      00FF6EF6H   LINE      CODE     ---       #96
      00FF6F0AH   LINE      CODE     ---       #98
      00FF6F0AH   LINE      CODE     ---       #100
      00FF6F0DH   LINE      CODE     ---       #101
      00FF6F10H   LINE      CODE     ---       #102
      00FF6F13H   LINE      CODE     ---       #103
      00FF6F15H   LINE      CODE     ---       #104
      00FF6F16H   LINE      CODE     ---       #106
      00FF6F16H   LINE      CODE     ---       #108
      00FF6F19H   LINE      CODE     ---       #109
      00FF6F1CH   LINE      CODE     ---       #110
      00FF6F1FH   LINE      CODE     ---       #111
      00FF6F21H   LINE      CODE     ---       #112
      00FF6F22H   LINE      CODE     ---       #114
      00FF6F22H   LINE      CODE     ---       #116
      00FF6F25H   LINE      CODE     ---       #117
      00FF6F28H   LINE      CODE     ---       #118
      00FF6F2BH   LINE      CODE     ---       #119
      00FF6F2CH   LINE      CODE     ---       #121
      00FF6F2CH   LINE      CODE     ---       #123
      00FF6F2FH   LINE      CODE     ---       #124
      00FF6F32H   LINE      CODE     ---       #125
      00FF6F35H   LINE      CODE     ---       #126
      00FF6F36H   LINE      CODE     ---       #128
      00FF6F36H   LINE      CODE     ---       #130
      00FF6F39H   LINE      CODE     ---       #131
      00FF6F3CH   LINE      CODE     ---       #132
      00FF6F3FH   LINE      CODE     ---       #133
      00FF6F3FH   LINE      CODE     ---       #135
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 89


      00FF6F3FH   LINE      CODE     ---       #136
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6EA7H   BLOCK     CODE     ---       LVL=0
      R5          REGSYM    ---      BYTE      tim_n
      00FF6EA9H   BLOCK     CODE     NEAR LAB  LVL=1
      WR6         REGSYM    ---      WORD      dat
      ---         BLOCKEND  ---      ---       LVL=1
      00FF6EA7H   LINE      CODE     ---       #137
      00FF6EA9H   LINE      CODE     ---       #138
      00FF6EA9H   LINE      CODE     ---       #139
      00FF6EABH   LINE      CODE     ---       #140
      00FF6EBFH   LINE      CODE     ---       #142
      00FF6EBFH   LINE      CODE     ---       #144
      00FF6EC5H   LINE      CODE     ---       #145
      00FF6EC7H   LINE      CODE     ---       #146
      00FF6EC9H   LINE      CODE     ---       #148
      00FF6EC9H   LINE      CODE     ---       #150
      00FF6ECFH   LINE      CODE     ---       #151
      00FF6ED1H   LINE      CODE     ---       #152
      00FF6ED3H   LINE      CODE     ---       #154
      00FF6ED3H   LINE      CODE     ---       #156
      00FF6ED9H   LINE      CODE     ---       #157
      00FF6EDBH   LINE      CODE     ---       #158
      00FF6EDDH   LINE      CODE     ---       #160
      00FF6EDDH   LINE      CODE     ---       #162
      00FF6EE3H   LINE      CODE     ---       #163
      00FF6EE5H   LINE      CODE     ---       #164
      00FF6EE7H   LINE      CODE     ---       #166
      00FF6EE7H   LINE      CODE     ---       #168
      00FF6EEDH   LINE      CODE     ---       #169
      00FF6EF3H   LINE      CODE     ---       #170
      00FF6EF3H   LINE      CODE     ---       #172
      00FF6EF3H   LINE      CODE     ---       #173
      00FF6EF3H   LINE      CODE     ---       #174
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6C74H   BLOCK     CODE     ---       LVL=0
      R7          REGSYM    ---      BYTE      tim_n
      00FF6C74H   LINE      CODE     ---       #175
      00FF6C76H   LINE      CODE     ---       #177
      00FF6C8AH   LINE      CODE     ---       #179
      00FF6C8AH   LINE      CODE     ---       #181
      00FF6C8CH   LINE      CODE     ---       #182
      00FF6C8FH   LINE      CODE     ---       #183
      00FF6C92H   LINE      CODE     ---       #184
      00FF6C94H   LINE      CODE     ---       #185
      00FF6C95H   LINE      CODE     ---       #187
      00FF6C95H   LINE      CODE     ---       #189
      00FF6C97H   LINE      CODE     ---       #190
      00FF6C9AH   LINE      CODE     ---       #191
      00FF6C9DH   LINE      CODE     ---       #192
      00FF6C9FH   LINE      CODE     ---       #193
      00FF6CA0H   LINE      CODE     ---       #195
      00FF6CA0H   LINE      CODE     ---       #197
      00FF6CA3H   LINE      CODE     ---       #198
      00FF6CA6H   LINE      CODE     ---       #199
      00FF6CA9H   LINE      CODE     ---       #200
      00FF6CACH   LINE      CODE     ---       #201
      00FF6CADH   LINE      CODE     ---       #203
      00FF6CADH   LINE      CODE     ---       #205
      00FF6CB0H   LINE      CODE     ---       #206
      00FF6CB3H   LINE      CODE     ---       #207
      00FF6CB6H   LINE      CODE     ---       #208
      00FF6CB9H   LINE      CODE     ---       #209
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 90


      00FF6CBAH   LINE      CODE     ---       #211
      00FF6CBAH   LINE      CODE     ---       #213
      00FF6CBDH   LINE      CODE     ---       #214
      00FF6CC0H   LINE      CODE     ---       #215
      00FF6CC3H   LINE      CODE     ---       #216
      00FF6CC6H   LINE      CODE     ---       #217
      00FF6CC6H   LINE      CODE     ---       #219
      00FF6CC6H   LINE      CODE     ---       #220
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       PWM
      00FF30BEH   PUBLIC    CODE     ---       PWM_init
      00FF399FH   PUBLIC    CODE     ---       PWM_change
      00FF84E1H   PUBLIC    HCONST   ---       PWM_CCR_ADDR
      00FF8501H   PUBLIC    HCONST   ---       PWM_ARR_ADDR
      00FF8509H   PUBLIC    HCONST   ---       PWM_CCER_ADDR
      00FF8519H   PUBLIC    HCONST   ---       PWM_CCMR_ADDR
      000000B4H   PUBLIC    EDATA    BYTE      ?PWM_init?BYTE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF30BEH   BLOCK     CODE     ---       LVL=0
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 91


      WR30        REGSYM    ---      INT       pwmch
      DR24        REGSYM    ---      DWORD     freq
      000000BAH   SYMBOL    EDATA    DWORD     duty
      00FF30C0H   BLOCK     CODE     NEAR LAB  LVL=1
      DR20        REGSYM    ---      DWORD     match_temp
      DR24        REGSYM    ---      DWORD     period_temp
      WR28        REGSYM    ---      WORD      freq_div
      ---         BLOCKEND  ---      ---       LVL=1
      00FF30BEH   LINE      CODE     ---       #14
      00FF30C0H   LINE      CODE     ---       #15
      00FF30C0H   LINE      CODE     ---       #19
      00FF30C0H   LINE      CODE     ---       #29
      00FF30CDH   LINE      CODE     ---       #30
      00FF30CDH   LINE      CODE     ---       #31
      00FF30D6H   LINE      CODE     ---       #33
      00FF30E0H   LINE      CODE     ---       #35
      00FF30FFH   LINE      CODE     ---       #36
      00FF3101H   LINE      CODE     ---       #39
      00FF3105H   LINE      CODE     ---       #40
      00FF3105H   LINE      CODE     ---       #43
      00FF310EH   LINE      CODE     ---       #46
      00FF3139H   LINE      CODE     ---       #47
      00FF315AH   LINE      CODE     ---       #50
      00FF31A6H   LINE      CODE     ---       #53
      00FF31B5H   LINE      CODE     ---       #54
      00FF31C0H   LINE      CODE     ---       #56
      00FF31C9H   LINE      CODE     ---       #57
      00FF31CFH   LINE      CODE     ---       #58
      00FF31D2H   LINE      CODE     ---       #61
      00FF3200H   LINE      CODE     ---       #62
      00FF3222H   LINE      CODE     ---       #63
      00FF3245H   LINE      CODE     ---       #66
      00FF3291H   LINE      CODE     ---       #67
      00FF32E7H   LINE      CODE     ---       #71
      00FF32F6H   LINE      CODE     ---       #72
      00FF3301H   LINE      CODE     ---       #74
      00FF330AH   LINE      CODE     ---       #75
      00FF3317H   LINE      CODE     ---       #76
      00FF3317H   LINE      CODE     ---       #79
      00FF3343H   LINE      CODE     ---       #80
      00FF3371H   LINE      CODE     ---       #83
      00FF3399H   LINE      CODE     ---       #84
      00FF33C7H   LINE      CODE     ---       #87
      00FF33DEH   LINE      CODE     ---       #88
      00FF3409H   LINE      CODE     ---       #91
      ---         BLOCKEND  ---      ---       LVL=0

      00FF399FH   BLOCK     CODE     ---       LVL=0
      WR30        REGSYM    ---      INT       pwmch
      DR24        REGSYM    ---      DWORD     duty
      00FF39A3H   BLOCK     CODE     NEAR LAB  LVL=1
      DR20        REGSYM    ---      DWORD     match_temp
      DR16        REGSYM    ---      DWORD     arr
      ---         BLOCKEND  ---      ---       LVL=1
      00FF399FH   LINE      CODE     ---       #92
      00FF39A3H   LINE      CODE     ---       #93
      00FF39A3H   LINE      CODE     ---       #95
      00FF39E7H   LINE      CODE     ---       #98
      00FF39EDH   LINE      CODE     ---       #101
      00FF3A18H   LINE      CODE     ---       #102
      00FF3A31H   LINE      CODE     ---       #104
      00FF3A34H   LINE      CODE     ---       #107
      00FF3A62H   LINE      CODE     ---       #108
      00FF3A84H   LINE      CODE     ---       #109
      00FF3AA7H   LINE      CODE     ---       #112
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 92


      00FF3AF3H   LINE      CODE     ---       #113
      00FF3B49H   LINE      CODE     ---       #115
      00FF3B49H   LINE      CODE     ---       #117
      00FF3B4FH   LINE      CODE     ---       #119
      00FF3B70H   LINE      CODE     ---       #120
      00FF3B72H   LINE      CODE     ---       #123
      00FF3B76H   LINE      CODE     ---       #124
      00FF3B76H   LINE      CODE     ---       #126
      00FF3B9EH   LINE      CODE     ---       #127
      00FF3BC8H   LINE      CODE     ---       #131
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       UART
      00FF1793H   PUBLIC    CODE     ---       UART_Send_float
      00FF7BCAH   PUBLIC    CODE     ---       UART_Send_string
      00FF6D19H   PUBLIC    CODE     ---       UART_Send_byte
      00FF44CAH   PUBLIC    CODE     ---       UART_init
      00FF1FB0H   PUBLIC    CODE     ---       UART_Send_int
      000002D3H   PUBLIC    EDATA    BYTE      UART1_OK
      000002D4H   PUBLIC    EDATA    BYTE      UART2_OK
      000002D5H   PUBLIC    EDATA    BYTE      UART3_OK
      000002D6H   PUBLIC    EDATA    BYTE      UART4_OK
      00000242H   PUBLIC    EDATA    BYTE      ?UART_Send_int?BYTE
      00000232H   PUBLIC    EDATA    BYTE      ?UART_Send_float?BYTE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000FEH   SFRSYM    DATA     BYTE      S4BUF
      000000ADH   SFRSYM    DATA     BYTE      S3BUF
      000000DDH.5 SFRSYM    DATA     BIT       T4x12
      0000009BH   SFRSYM    DATA     BYTE      S2BUF
      000000DDH.1 SFRSYM    DATA     BIT       T3x12
      0000008EH.2 SFRSYM    DATA     BIT       T2x12
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000DDH.7 SFRSYM    DATA     BIT       T4R
      000000DDH.3 SFRSYM    DATA     BIT       T3R
      0000008EH.4 SFRSYM    DATA     BIT       T2R
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000D3H   SFRSYM    DATA     BYTE      T4L
      000000D5H   SFRSYM    DATA     BYTE      T3L
      000000D7H   SFRSYM    DATA     BYTE      T2L
      0000008BH   SFRSYM    DATA     BYTE      TL1
      000000D2H   SFRSYM    DATA     BYTE      T4H
      000000D4H   SFRSYM    DATA     BYTE      T3H
      000000D6H   SFRSYM    DATA     BYTE      T2H
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000AFH.4 SFRSYM    DATA     BIT       ES4
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000AFH.3 SFRSYM    DATA     BIT       ES3
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000AFH.0 SFRSYM    DATA     BIT       ES2
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 93


      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      00000099H   SFRSYM    DATA     BYTE      SBUF
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000A8H.4 SFRSYM    DATA     BIT       ES
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF44CAH   BLOCK     CODE     ---       LVL=0
      R13         REGSYM    ---      BYTE      pin
      DR28        REGSYM    ---      DWORD     btl
      R14         REGSYM    ---      BYTE      n
      R15         REGSYM    ---      BYTE      isr
      00FF44CAH   LINE      CODE     ---       #8
      00FF44D6H   LINE      CODE     ---       #10
      00FF44DBH   LINE      CODE     ---       #12
      00FF44DEH   LINE      CODE     ---       #13
      00FF44EEH   LINE      CODE     ---       #15
      00FF44F4H   LINE      CODE     ---       #16
      00FF44F9H   LINE      CODE     ---       #17
      00FF44FEH   LINE      CODE     ---       #18
      00FF4501H   LINE      CODE     ---       #19
      00FF4501H   LINE      CODE     ---       #20
      00FF4504H   LINE      CODE     ---       #21
      00FF4507H   LINE      CODE     ---       #22
      00FF450BH   LINE      CODE     ---       #23
      00FF4530H   LINE      CODE     ---       #24
      00FF4534H   LINE      CODE     ---       #25
      00FF4536H   LINE      CODE     ---       #26
      00FF4539H   LINE      CODE     ---       #27
      00FF453EH   LINE      CODE     ---       #28
      00FF4540H   LINE      CODE     ---       #30
      00FF4545H   LINE      CODE     ---       #33
      00FF454FH   LINE      CODE     ---       #34
      00FF4552H   LINE      CODE     ---       #35
      00FF4555H   LINE      CODE     ---       #36
      00FF4562H   LINE      CODE     ---       #37
      00FF4587H   LINE      CODE     ---       #38
      00FF458BH   LINE      CODE     ---       #39
      00FF458EH   LINE      CODE     ---       #40
      00FF4591H   LINE      CODE     ---       #41
      00FF4596H   LINE      CODE     ---       #42
      00FF4599H   LINE      CODE     ---       #44
      00FF459EH   LINE      CODE     ---       #46
      00FF45A8H   LINE      CODE     ---       #47
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 94


      00FF45ABH   LINE      CODE     ---       #48
      00FF45AEH   LINE      CODE     ---       #49
      00FF45D3H   LINE      CODE     ---       #50
      00FF45D7H   LINE      CODE     ---       #51
      00FF45DAH   LINE      CODE     ---       #52
      00FF45DDH   LINE      CODE     ---       #53
      00FF45E2H   LINE      CODE     ---       #54
      00FF45E5H   LINE      CODE     ---       #56
      00FF45EAH   LINE      CODE     ---       #58
      00FF45F4H   LINE      CODE     ---       #59
      00FF45F7H   LINE      CODE     ---       #60
      00FF45FAH   LINE      CODE     ---       #61
      00FF461FH   LINE      CODE     ---       #62
      00FF4623H   LINE      CODE     ---       #63
      00FF4626H   LINE      CODE     ---       #64
      00FF4629H   LINE      CODE     ---       #65
      00FF462EH   LINE      CODE     ---       #66
      00FF4631H   LINE      CODE     ---       #69
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6D19H   BLOCK     CODE     ---       LVL=0
      R7          REGSYM    ---      BYTE      pin
      R10         REGSYM    ---      BYTE      c
      00FF6D19H   LINE      CODE     ---       #70
      00FF6D1DH   LINE      CODE     ---       #72
      00FF6D21H   LINE      CODE     ---       #74
      00FF6D27H   LINE      CODE     ---       #75
      00FF6D2DH   LINE      CODE     ---       #76
      00FF6D30H   LINE      CODE     ---       #77
      00FF6D30H   LINE      CODE     ---       #78
      00FF6D34H   LINE      CODE     ---       #80
      00FF6D3AH   LINE      CODE     ---       #81
      00FF6D40H   LINE      CODE     ---       #82
      00FF6D43H   LINE      CODE     ---       #83
      00FF6D43H   LINE      CODE     ---       #84
      00FF6D47H   LINE      CODE     ---       #86
      00FF6D4DH   LINE      CODE     ---       #87
      00FF6D53H   LINE      CODE     ---       #88
      00FF6D56H   LINE      CODE     ---       #89
      00FF6D56H   LINE      CODE     ---       #90
      00FF6D5AH   LINE      CODE     ---       #92
      00FF6D60H   LINE      CODE     ---       #93
      00FF6D66H   LINE      CODE     ---       #94
      00FF6D69H   LINE      CODE     ---       #95
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7BCAH   BLOCK     CODE     ---       LVL=0
      000002E0H   SYMBOL    EDATA    BYTE      pin
      REG=3       REGSYM    ---      ---       pt
      00FF7BCAH   LINE      CODE     ---       #97
      00FF7BD2H   LINE      CODE     ---       #99
      00FF7BD4H   LINE      CODE     ---       #101
      00FF7BE0H   LINE      CODE     ---       #102
      00FF7BE5H   LINE      CODE     ---       #103
      ---         BLOCKEND  ---      ---       LVL=0

      00FF1FB0H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      pin
      00000243H   SYMBOL    EDATA    ---       pa
      00000247H   SYMBOL    EDATA    LONG      num
      0000024BH   SYMBOL    EDATA    ---       pb
      00FF1FBCH   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      k
      ---         BLOCKEND  ---      ---       LVL=1
      00FF1FB0H   LINE      CODE     ---       #104
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 95


      00FF1FBCH   LINE      CODE     ---       #105
      00FF1FBCH   LINE      CODE     ---       #108
      00FF1FBEH   LINE      CODE     ---       #110
      00FF1FD0H   LINE      CODE     ---       #111
      00FF1FD9H   LINE      CODE     ---       #112
      00FF1FE9H   LINE      CODE     ---       #115
      00FF1FFBH   LINE      CODE     ---       #116
      00FF2014H   LINE      CODE     ---       #117
      00FF2043H   LINE      CODE     ---       #119
      00FF2043H   LINE      CODE     ---       #120
      00FF2059H   LINE      CODE     ---       #121
      00FF205CH   LINE      CODE     ---       #122
      00FF205CH   LINE      CODE     ---       #123
      00FF207CH   LINE      CODE     ---       #124
      00FF2092H   LINE      CODE     ---       #125
      00FF2095H   LINE      CODE     ---       #126
      00FF2095H   LINE      CODE     ---       #127
      00FF20B5H   LINE      CODE     ---       #128
      00FF20D5H   LINE      CODE     ---       #129
      00FF20EBH   LINE      CODE     ---       #130
      00FF20EEH   LINE      CODE     ---       #131
      00FF20EEH   LINE      CODE     ---       #132
      00FF210EH   LINE      CODE     ---       #133
      00FF212EH   LINE      CODE     ---       #134
      00FF214EH   LINE      CODE     ---       #135
      00FF2164H   LINE      CODE     ---       #136
      00FF2167H   LINE      CODE     ---       #137
      00FF2167H   LINE      CODE     ---       #138
      00FF2187H   LINE      CODE     ---       #139
      00FF21A7H   LINE      CODE     ---       #140
      00FF21C7H   LINE      CODE     ---       #141
      00FF21E7H   LINE      CODE     ---       #142
      00FF21FDH   LINE      CODE     ---       #143
      00FF2200H   LINE      CODE     ---       #144
      00FF2200H   LINE      CODE     ---       #145
      00FF2224H   LINE      CODE     ---       #146
      00FF2244H   LINE      CODE     ---       #147
      00FF2264H   LINE      CODE     ---       #148
      00FF2284H   LINE      CODE     ---       #149
      00FF22A4H   LINE      CODE     ---       #150
      00FF22BAH   LINE      CODE     ---       #151
      00FF22BDH   LINE      CODE     ---       #152
      00FF22BDH   LINE      CODE     ---       #153
      00FF22E1H   LINE      CODE     ---       #154
      00FF2305H   LINE      CODE     ---       #155
      00FF2325H   LINE      CODE     ---       #156
      00FF2345H   LINE      CODE     ---       #157
      00FF2365H   LINE      CODE     ---       #158
      00FF2385H   LINE      CODE     ---       #159
      00FF239BH   LINE      CODE     ---       #160
      00FF239EH   LINE      CODE     ---       #161
      00FF239EH   LINE      CODE     ---       #162
      00FF23C2H   LINE      CODE     ---       #163
      00FF23E6H   LINE      CODE     ---       #164
      00FF240AH   LINE      CODE     ---       #165
      00FF242AH   LINE      CODE     ---       #166
      00FF244AH   LINE      CODE     ---       #167
      00FF246AH   LINE      CODE     ---       #168
      00FF248AH   LINE      CODE     ---       #169
      00FF24A0H   LINE      CODE     ---       #170
      00FF24A3H   LINE      CODE     ---       #171
      00FF24A3H   LINE      CODE     ---       #172
      00FF24C7H   LINE      CODE     ---       #173
      00FF24EBH   LINE      CODE     ---       #174
      00FF250FH   LINE      CODE     ---       #175
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 96


      00FF2533H   LINE      CODE     ---       #176
      00FF2553H   LINE      CODE     ---       #177
      00FF2573H   LINE      CODE     ---       #178
      00FF2593H   LINE      CODE     ---       #179
      00FF25B3H   LINE      CODE     ---       #180
      00FF25C9H   LINE      CODE     ---       #181
      00FF25CCH   LINE      CODE     ---       #182
      00FF25CCH   LINE      CODE     ---       #183
      00FF25F0H   LINE      CODE     ---       #184
      00FF2614H   LINE      CODE     ---       #185
      00FF2638H   LINE      CODE     ---       #186
      00FF265CH   LINE      CODE     ---       #187
      00FF2680H   LINE      CODE     ---       #188
      00FF26A0H   LINE      CODE     ---       #189
      00FF26C0H   LINE      CODE     ---       #190
      00FF26E0H   LINE      CODE     ---       #191
      00FF2700H   LINE      CODE     ---       #192
      00FF2719H   LINE      CODE     ---       #193
      00FF2719H   LINE      CODE     ---       #194
      00FF2719H   LINE      CODE     ---       #195
      00FF2719H   LINE      CODE     ---       #197
      00FF271BH   LINE      CODE     ---       #199
      00FF272DH   LINE      CODE     ---       #200
      00FF2736H   LINE      CODE     ---       #201
      ---         BLOCKEND  ---      ---       LVL=0

      00FF1793H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      pin
      00000233H   SYMBOL    EDATA    ---       ps
      00000237H   SYMBOL    EDATA    FLOAT     num
      0000023BH   SYMBOL    EDATA    ---       pe
      00FF179FH   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      k
      0000023FH   SYMBOL    EDATA    DWORD     l
      ---         BLOCKEND  ---      ---       LVL=1
      00FF1793H   LINE      CODE     ---       #202
      00FF179FH   LINE      CODE     ---       #203
      00FF179FH   LINE      CODE     ---       #207
      00FF17B0H   LINE      CODE     ---       #209
      00FF17B2H   LINE      CODE     ---       #211
      00FF17C4H   LINE      CODE     ---       #212
      00FF17CDH   LINE      CODE     ---       #213
      00FF17E0H   LINE      CODE     ---       #215
      00FF17F0H   LINE      CODE     ---       #217
      00FF180BH   LINE      CODE     ---       #218
      00FF183AH   LINE      CODE     ---       #220
      00FF183AH   LINE      CODE     ---       #221
      00FF183AH   LINE      CODE     ---       #222
      00FF183DH   LINE      CODE     ---       #223
      00FF183DH   LINE      CODE     ---       #224
      00FF1862H   LINE      CODE     ---       #225
      00FF1862H   LINE      CODE     ---       #226
      00FF1865H   LINE      CODE     ---       #227
      00FF1865H   LINE      CODE     ---       #228
      00FF188AH   LINE      CODE     ---       #229
      00FF18AFH   LINE      CODE     ---       #230
      00FF18AFH   LINE      CODE     ---       #231
      00FF18B2H   LINE      CODE     ---       #232
      00FF18B2H   LINE      CODE     ---       #233
      00FF18D7H   LINE      CODE     ---       #234
      00FF18FCH   LINE      CODE     ---       #235
      00FF1921H   LINE      CODE     ---       #236
      00FF1921H   LINE      CODE     ---       #237
      00FF1924H   LINE      CODE     ---       #238
      00FF1924H   LINE      CODE     ---       #239
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 97


      00FF194BH   LINE      CODE     ---       #240
      00FF1970H   LINE      CODE     ---       #241
      00FF1995H   LINE      CODE     ---       #242
      00FF19BAH   LINE      CODE     ---       #243
      00FF19BAH   LINE      CODE     ---       #244
      00FF19BDH   LINE      CODE     ---       #245
      00FF19BDH   LINE      CODE     ---       #246
      00FF19E4H   LINE      CODE     ---       #247
      00FF1A0BH   LINE      CODE     ---       #248
      00FF1A30H   LINE      CODE     ---       #249
      00FF1A55H   LINE      CODE     ---       #250
      00FF1A7AH   LINE      CODE     ---       #251
      00FF1A7AH   LINE      CODE     ---       #252
      00FF1A7DH   LINE      CODE     ---       #253
      00FF1A7DH   LINE      CODE     ---       #254
      00FF1AA4H   LINE      CODE     ---       #255
      00FF1ACBH   LINE      CODE     ---       #256
      00FF1AF2H   LINE      CODE     ---       #257
      00FF1B17H   LINE      CODE     ---       #258
      00FF1B3CH   LINE      CODE     ---       #259
      00FF1B61H   LINE      CODE     ---       #260
      00FF1B61H   LINE      CODE     ---       #261
      00FF1B64H   LINE      CODE     ---       #262
      00FF1B64H   LINE      CODE     ---       #263
      00FF1B8BH   LINE      CODE     ---       #264
      00FF1BB2H   LINE      CODE     ---       #265
      00FF1BD9H   LINE      CODE     ---       #266
      00FF1C00H   LINE      CODE     ---       #267
      00FF1C25H   LINE      CODE     ---       #268
      00FF1C4AH   LINE      CODE     ---       #269
      00FF1C6FH   LINE      CODE     ---       #270
      00FF1C6FH   LINE      CODE     ---       #271
      00FF1C72H   LINE      CODE     ---       #272
      00FF1C72H   LINE      CODE     ---       #273
      00FF1C99H   LINE      CODE     ---       #274
      00FF1CC0H   LINE      CODE     ---       #275
      00FF1CE7H   LINE      CODE     ---       #276
      00FF1D0EH   LINE      CODE     ---       #277
      00FF1D35H   LINE      CODE     ---       #278
      00FF1D5AH   LINE      CODE     ---       #279
      00FF1D7FH   LINE      CODE     ---       #280
      00FF1DA4H   LINE      CODE     ---       #281
      00FF1DA4H   LINE      CODE     ---       #282
      00FF1DA7H   LINE      CODE     ---       #283
      00FF1DA7H   LINE      CODE     ---       #284
      00FF1DCEH   LINE      CODE     ---       #285
      00FF1DF5H   LINE      CODE     ---       #286
      00FF1E1CH   LINE      CODE     ---       #287
      00FF1E43H   LINE      CODE     ---       #288
      00FF1E6AH   LINE      CODE     ---       #289
      00FF1E91H   LINE      CODE     ---       #290
      00FF1EB6H   LINE      CODE     ---       #291
      00FF1EDBH   LINE      CODE     ---       #292
      00FF1F00H   LINE      CODE     ---       #293
      00FF1F1CH   LINE      CODE     ---       #294
      00FF1F1CH   LINE      CODE     ---       #295
      00FF1F1CH   LINE      CODE     ---       #296
      00FF1F24H   LINE      CODE     ---       #298
      00FF1F49H   LINE      CODE     ---       #299
      00FF1F6EH   LINE      CODE     ---       #300
      00FF1F90H   LINE      CODE     ---       #302
      00FF1F92H   LINE      CODE     ---       #304
      00FF1FA4H   LINE      CODE     ---       #305
      00FF1FADH   LINE      CODE     ---       #306
      ---         BLOCKEND  ---      ---       LVL=0
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 98



      ---         MODULE    ---      ---       EEPROM
      00FF79B6H   PUBLIC    CODE     ---       EEPROM_Delete
      00FF8194H   PUBLIC    CODE     ---       EEPROM_OFF
      00FF7787H   PUBLIC    CODE     ---       EEPROM_Read
      00FF77B3H   PUBLIC    CODE     ---       EEPROM_Change
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000C6H   SFRSYM    DATA     BYTE      IAP_TRIG
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000C2H   SFRSYM    DATA     BYTE      IAP_DATA
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F5H   SFRSYM    DATA     BYTE      IAP_TPS
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000C4H   SFRSYM    DATA     BYTE      IAP_ADDRL
      000000C3H   SFRSYM    DATA     BYTE      IAP_ADDRH
      000000F6H   SFRSYM    DATA     BYTE      IAP_ADDRE
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000C5H   SFRSYM    DATA     BYTE      IAP_CMD
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF8194H   BLOCK     CODE     ---       LVL=0
      00FF8194H   LINE      CODE     ---       #11
      00FF8194H   LINE      CODE     ---       #13
      00FF8197H   LINE      CODE     ---       #14
      00FF819AH   LINE      CODE     ---       #15
      00FF819DH   LINE      CODE     ---       #16
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 99


      00FF81A0H   LINE      CODE     ---       #17
      00FF81A3H   LINE      CODE     ---       #18
      00FF81A6H   LINE      CODE     ---       #19
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7787H   BLOCK     CODE     ---       LVL=0
      DR4         REGSYM    ---      DWORD     addr
      00FF7789H   BLOCK     CODE     NEAR LAB  LVL=1
      R15         REGSYM    ---      BYTE      dat
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7787H   LINE      CODE     ---       #21
      00FF7789H   LINE      CODE     ---       #22
      00FF7789H   LINE      CODE     ---       #25
      00FF778CH   LINE      CODE     ---       #26
      00FF778FH   LINE      CODE     ---       #27
      00FF7792H   LINE      CODE     ---       #28
      00FF7796H   LINE      CODE     ---       #29
      00FF779AH   LINE      CODE     ---       #30
      00FF779EH   LINE      CODE     ---       #31
      00FF77A1H   LINE      CODE     ---       #32
      00FF77A4H   LINE      CODE     ---       #33
      00FF77A5H   LINE      CODE     ---       #34
      00FF77A6H   LINE      CODE     ---       #35
      00FF77A7H   LINE      CODE     ---       #36
      00FF77A8H   LINE      CODE     ---       #37
      00FF77ABH   LINE      CODE     ---       #38
      00FF77AEH   LINE      CODE     ---       #40
      00FF77B0H   LINE      CODE     ---       #41
      ---         BLOCKEND  ---      ---       LVL=0

      00FF77B3H   BLOCK     CODE     ---       LVL=0
      DR4         REGSYM    ---      DWORD     addr
      R15         REGSYM    ---      BYTE      dat
      00FF77B3H   LINE      CODE     ---       #43
      00FF77B7H   LINE      CODE     ---       #45
      00FF77BAH   LINE      CODE     ---       #46
      00FF77BDH   LINE      CODE     ---       #47
      00FF77C0H   LINE      CODE     ---       #48
      00FF77C4H   LINE      CODE     ---       #49
      00FF77C8H   LINE      CODE     ---       #50
      00FF77CCH   LINE      CODE     ---       #51
      00FF77CFH   LINE      CODE     ---       #52
      00FF77D2H   LINE      CODE     ---       #53
      00FF77D5H   LINE      CODE     ---       #54
      00FF77D6H   LINE      CODE     ---       #55
      00FF77D7H   LINE      CODE     ---       #56
      00FF77D8H   LINE      CODE     ---       #57
      00FF77D9H   LINE      CODE     ---       #58
      00FF77DCH   LINE      CODE     ---       #59
      ---         BLOCKEND  ---      ---       LVL=0

      00FF79B6H   BLOCK     CODE     ---       LVL=0
      DR4         REGSYM    ---      DWORD     addr
      00FF79B6H   LINE      CODE     ---       #61
      00FF79B6H   LINE      CODE     ---       #63
      00FF79B9H   LINE      CODE     ---       #64
      00FF79BCH   LINE      CODE     ---       #65
      00FF79BFH   LINE      CODE     ---       #66
      00FF79C3H   LINE      CODE     ---       #67
      00FF79C7H   LINE      CODE     ---       #68
      00FF79CBH   LINE      CODE     ---       #69
      00FF79CEH   LINE      CODE     ---       #70
      00FF79D1H   LINE      CODE     ---       #71
      00FF79D2H   LINE      CODE     ---       #72
      00FF79D3H   LINE      CODE     ---       #73
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 100


      00FF79D4H   LINE      CODE     ---       #74
      00FF79D5H   LINE      CODE     ---       #75
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       CAN
      00FF60A0H   PUBLIC    CODE     ---       CanReadFifo
      00FF81A7H   PUBLIC    CODE     ---       CanReadReg
      00FF593EH   PUBLIC    CODE     ---       CanReadMsg
      00FF8144H   PUBLIC    CODE     ---       CanWriteReg
      00FF4636H   PUBLIC    CODE     ---       CanSendMsg
      00FF3DEAH   PUBLIC    CODE     ---       CANInit
      000002B6H   PUBLIC    EDATA    BYTE      TSG1
      000002B7H   PUBLIC    EDATA    BYTE      TSG2
      000002B8H   PUBLIC    EDATA    BYTE      BRP
      000002B9H   PUBLIC    EDATA    WORD      CAN_time
      00000022H.0 PUBLIC    BIT      BIT       CAN_TX_OK
      00000022H.1 PUBLIC    BIT      BIT       ?CANInit?BIT
      00000022H.2 PUBLIC    BIT      BIT       ?CanReadMsg?BIT
      00000254H   PUBLIC    EDATA    BYTE      ?CanSendMsg?BYTE
      00000022H.3 PUBLIC    BIT      BIT       ?CanSendMsg?BIT
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000F1H.1 SFRSYM    DATA     BIT       CANIE
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000097H.3 SFRSYM    DATA     BIT       CANSEL
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000F1H.5 SFRSYM    DATA     BIT       CAN2IE
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 101


      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF81A7H   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      addr
      R11         REGSYM    ---      BYTE      dat
      00FF81A7H   LINE      CODE     ---       #14
      00FF81A7H   LINE      CODE     ---       #15
      00FF81A7H   LINE      CODE     ---       #17
      00FF81B2H   LINE      CODE     ---       #18
      00FF81B9H   LINE      CODE     ---       #19
      00FF81B9H   LINE      CODE     ---       #20
      ---         BLOCKEND  ---      ---       LVL=0

      00FF8144H   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      addr
      R2          REGSYM    ---      BYTE      dat
      00FF8144H   LINE      CODE     ---       #22
      00FF8146H   LINE      CODE     ---       #24
      00FF8151H   LINE      CODE     ---       #25
      00FF8158H   LINE      CODE     ---       #26
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3DEAH   BLOCK     CODE     ---       LVL=0
      R5          REGSYM    ---      BYTE      CAN
      WR6         REGSYM    ---      INT       Bbaud
      R4          REGSYM    ---      BYTE      CAN_N
      00000022H.1 SYMBOL    BIT      BIT       en
      00FF3DEAH   LINE      CODE     ---       #83
      00FF3DEEH   LINE      CODE     ---       #85
      00FF3E28H   LINE      CODE     ---       #87
      00FF3E39H   LINE      CODE     ---       #88
      00FF3E4AH   LINE      CODE     ---       #89
      00FF3E5BH   LINE      CODE     ---       #90
      00FF3E6CH   LINE      CODE     ---       #91
      00FF3E7CH   LINE      CODE     ---       #92
      00FF3E8CH   LINE      CODE     ---       #93
      00FF3E9CH   LINE      CODE     ---       #94
      00FF3EAAH   LINE      CODE     ---       #95
      00FF3EBAH   LINE      CODE     ---       #96
      00FF3EC8H   LINE      CODE     ---       #97
      00FF3ED6H   LINE      CODE     ---       #98
      00FF3EE4H   LINE      CODE     ---       #99
      00FF3EF4H   LINE      CODE     ---       #100
      00FF3EF4H   LINE      CODE     ---       #101
      00FF3EF4H   LINE      CODE     ---       #103
      00FF3EFFH   LINE      CODE     ---       #105
      00FF3F03H   LINE      CODE     ---       #107
      00FF3F12H   LINE      CODE     ---       #109
      00FF3F1BH   LINE      CODE     ---       #110
      00FF3F23H   LINE      CODE     ---       #111
      00FF3F2BH   LINE      CODE     ---       #112
      00FF3F31H   LINE      CODE     ---       #113
      00FF3F31H   LINE      CODE     ---       #114
      00FF3F34H   LINE      CODE     ---       #115
      00FF3F39H   LINE      CODE     ---       #116
      00FF3F39H   LINE      CODE     ---       #117
      00FF3F3DH   LINE      CODE     ---       #119
      00FF3F4EH   LINE      CODE     ---       #121
      00FF3F57H   LINE      CODE     ---       #122
      00FF3F5FH   LINE      CODE     ---       #123
      00FF3F67H   LINE      CODE     ---       #124
      00FF3F6DH   LINE      CODE     ---       #125
      00FF3F6DH   LINE      CODE     ---       #126
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 102


      00FF3F70H   LINE      CODE     ---       #127
      00FF3F75H   LINE      CODE     ---       #128
      00FF3F75H   LINE      CODE     ---       #130
      00FF3F7CH   LINE      CODE     ---       #132
      00FF3F8BH   LINE      CODE     ---       #133
      00FF3FA6H   LINE      CODE     ---       #135
      00FF3FADH   LINE      CODE     ---       #136
      00FF3FB4H   LINE      CODE     ---       #137
      00FF3FBBH   LINE      CODE     ---       #138
      00FF3FC2H   LINE      CODE     ---       #139
      00FF3FCAH   LINE      CODE     ---       #140
      00FF3FD2H   LINE      CODE     ---       #141
      00FF3FDAH   LINE      CODE     ---       #142
      00FF3FE2H   LINE      CODE     ---       #144
      00FF3FEAH   LINE      CODE     ---       #145
      00FF3FF2H   LINE      CODE     ---       #146
      ---         BLOCKEND  ---      ---       LVL=0

      00FF60A0H   BLOCK     CODE     ---       LVL=0
      REG=3       REGSYM    ---      ---       pdat
      00FF60A0H   LINE      CODE     ---       #149
      00FF60A4H   LINE      CODE     ---       #151
      00FF60ACH   LINE      CODE     ---       #152
      00FF60B5H   LINE      CODE     ---       #153
      00FF60BEH   LINE      CODE     ---       #154
      00FF60C7H   LINE      CODE     ---       #155
      00FF60D0H   LINE      CODE     ---       #156
      00FF60D9H   LINE      CODE     ---       #157
      00FF60E2H   LINE      CODE     ---       #158
      00FF60EBH   LINE      CODE     ---       #159
      00FF60F4H   LINE      CODE     ---       #160
      00FF60FDH   LINE      CODE     ---       #161
      00FF6106H   LINE      CODE     ---       #162
      00FF610FH   LINE      CODE     ---       #163
      00FF6118H   LINE      CODE     ---       #164
      00FF6121H   LINE      CODE     ---       #165
      00FF612AH   LINE      CODE     ---       #166
      00FF6133H   LINE      CODE     ---       #167
      ---         BLOCKEND  ---      ---       LVL=0

      00FF593EH   BLOCK     CODE     ---       LVL=0
      REG=3       REGSYM    ---      ---       pdat
      00000022H.2 SYMBOL    BIT      BIT       mode
      00FF5942H   BLOCK     CODE     NEAR LAB  LVL=1
      0000020AH   SYMBOL    EDATA    BYTE      i
      0000020BH   SYMBOL    EDATA    DWORD     CanID
      0000020FH   SYMBOL    EDATA    ---       buffer
      ---         BLOCKEND  ---      ---       LVL=1
      00FF593EH   LINE      CODE     ---       #171
      00FF5942H   LINE      CODE     ---       #172
      00FF5942H   LINE      CODE     ---       #177
      00FF5949H   LINE      CODE     ---       #178
      00FF594CH   LINE      CODE     ---       #180
      00FF598AH   LINE      CODE     ---       #181
      00FF598FH   LINE      CODE     ---       #183
      00FF59A0H   LINE      CODE     ---       #184
      00FF59ACH   LINE      CODE     ---       #185
      00FF59AEH   LINE      CODE     ---       #188
      00FF59CEH   LINE      CODE     ---       #189
      00FF59D3H   LINE      CODE     ---       #191
      00FF59E4H   LINE      CODE     ---       #192
      00FF59F0H   LINE      CODE     ---       #193
      00FF59F0H   LINE      CODE     ---       #194
      00FF59F4H   LINE      CODE     ---       #195
      ---         BLOCKEND  ---      ---       LVL=0
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 103



      00FF4636H   BLOCK     CODE     ---       LVL=0
      R7          REGSYM    ---      BYTE      CAN
      00000259H   SYMBOL    EDATA    DWORD     canid
      REG=3       REGSYM    ---      ---       pdat
      0000025DH   SYMBOL    EDATA    BYTE      len
      00000022H.3 SYMBOL    BIT      BIT       mode
      00FF463EH   BLOCK     CODE     NEAR LAB  LVL=1
      0000025EH   SYMBOL    EDATA    DWORD     CanID
      ---         BLOCKEND  ---      ---       LVL=1
      00FF4636H   LINE      CODE     ---       #196
      00FF463EH   LINE      CODE     ---       #197
      00FF463EH   LINE      CODE     ---       #201
      00FF4649H   LINE      CODE     ---       #203
      00FF464FH   LINE      CODE     ---       #205
      00FF465DH   LINE      CODE     ---       #206
      00FF466CH   LINE      CODE     ---       #207
      00FF4677H   LINE      CODE     ---       #208
      00FF4682H   LINE      CODE     ---       #209
      00FF468DH   LINE      CODE     ---       #211
      00FF4696H   LINE      CODE     ---       #212
      00FF469EH   LINE      CODE     ---       #213
      00FF46A7H   LINE      CODE     ---       #214
      00FF46B0H   LINE      CODE     ---       #216
      00FF46B9H   LINE      CODE     ---       #217
      00FF46C2H   LINE      CODE     ---       #218
      00FF46CBH   LINE      CODE     ---       #219
      00FF46D4H   LINE      CODE     ---       #221
      00FF46DDH   LINE      CODE     ---       #222
      00FF46E4H   LINE      CODE     ---       #223
      00FF46E8H   LINE      CODE     ---       #224
      00FF46E8H   LINE      CODE     ---       #225
      00FF46EAH   LINE      CODE     ---       #228
      00FF46F9H   LINE      CODE     ---       #229
      00FF4702H   LINE      CODE     ---       #230
      00FF470DH   LINE      CODE     ---       #231
      00FF4716H   LINE      CODE     ---       #232
      00FF471EH   LINE      CODE     ---       #234
      00FF4727H   LINE      CODE     ---       #235
      00FF4730H   LINE      CODE     ---       #236
      00FF4739H   LINE      CODE     ---       #237
      00FF4742H   LINE      CODE     ---       #239
      00FF474BH   LINE      CODE     ---       #240
      00FF4754H   LINE      CODE     ---       #241
      00FF475DH   LINE      CODE     ---       #243
      00FF4764H   LINE      CODE     ---       #244
      00FF4764H   LINE      CODE     ---       #245
      00FF4766H   LINE      CODE     ---       #246
      00FF476EH   LINE      CODE     ---       #247
      00FF4776H   LINE      CODE     ---       #249
      00FF4778H   LINE      CODE     ---       #251
      00FF4782H   LINE      CODE     ---       #252
      00FF4789H   LINE      CODE     ---       #253
      00FF4794H   LINE      CODE     ---       #254
      00FF479BH   LINE      CODE     ---       #255
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       DMA
      00FF479EH   PUBLIC    CODE     ---       DMA_RXD_init
      00FF4A26H   PUBLIC    CODE     ---       DMA_TXD_init
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 104


      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF4A26H   BLOCK     CODE     ---       LVL=0
      R7          REGSYM    ---      BYTE      pin
      WR0         REGSYM    ---      WORD      length
      REG=7       REGSYM    ---      ---       DAT
      00FF4A26H   LINE      CODE     ---       #7
      00FF4A2CH   LINE      CODE     ---       #9
      00FF4A46H   LINE      CODE     ---       #11
      00FF4A46H   LINE      CODE     ---       #13
      00FF4A52H   LINE      CODE     ---       #14
      00FF4A59H   LINE      CODE     ---       #15
      00FF4A64H   LINE      CODE     ---       #16
      00FF4A6DH   LINE      CODE     ---       #17
      00FF4A78H   LINE      CODE     ---       #18
      00FF4A83H   LINE      CODE     ---       #20
      00FF4A89H   LINE      CODE     ---       #21
      00FF4A8CH   LINE      CODE     ---       #22
      00FF4A8CH   LINE      CODE     ---       #24
      00FF4A98H   LINE      CODE     ---       #25
      00FF4A9FH   LINE      CODE     ---       #26
      00FF4AAAH   LINE      CODE     ---       #27
      00FF4AB3H   LINE      CODE     ---       #28
      00FF4ABEH   LINE      CODE     ---       #29
      00FF4AC9H   LINE      CODE     ---       #30
      00FF4ACFH   LINE      CODE     ---       #31
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 105


      00FF4AD2H   LINE      CODE     ---       #32
      00FF4AD2H   LINE      CODE     ---       #34
      00FF4ADEH   LINE      CODE     ---       #35
      00FF4AE5H   LINE      CODE     ---       #36
      00FF4AF0H   LINE      CODE     ---       #37
      00FF4AF9H   LINE      CODE     ---       #38
      00FF4B04H   LINE      CODE     ---       #39
      00FF4B0FH   LINE      CODE     ---       #40
      00FF4B15H   LINE      CODE     ---       #41
      00FF4B17H   LINE      CODE     ---       #42
      00FF4B17H   LINE      CODE     ---       #44
      00FF4B23H   LINE      CODE     ---       #45
      00FF4B2AH   LINE      CODE     ---       #46
      00FF4B39H   LINE      CODE     ---       #47
      00FF4B42H   LINE      CODE     ---       #48
      00FF4B4DH   LINE      CODE     ---       #49
      00FF4B58H   LINE      CODE     ---       #50
      00FF4B65H   LINE      CODE     ---       #51
      00FF4B65H   LINE      CODE     ---       #52
      00FF4B65H   LINE      CODE     ---       #53
      ---         BLOCKEND  ---      ---       LVL=0

      00FF479EH   BLOCK     CODE     ---       LVL=0
      R7          REGSYM    ---      BYTE      pin
      WR0         REGSYM    ---      WORD      length
      REG=7       REGSYM    ---      ---       DAT
      00FF479EH   LINE      CODE     ---       #55
      00FF47A4H   LINE      CODE     ---       #57
      00FF47BEH   LINE      CODE     ---       #59
      00FF47BEH   LINE      CODE     ---       #61
      00FF47CBH   LINE      CODE     ---       #62
      00FF47D3H   LINE      CODE     ---       #63
      00FF47DEH   LINE      CODE     ---       #64
      00FF47E7H   LINE      CODE     ---       #65
      00FF47F2H   LINE      CODE     ---       #66
      00FF47FDH   LINE      CODE     ---       #67
      00FF4803H   LINE      CODE     ---       #68
      00FF4806H   LINE      CODE     ---       #69
      00FF4806H   LINE      CODE     ---       #71
      00FF4813H   LINE      CODE     ---       #72
      00FF481BH   LINE      CODE     ---       #73
      00FF4826H   LINE      CODE     ---       #74
      00FF482FH   LINE      CODE     ---       #75
      00FF483AH   LINE      CODE     ---       #76
      00FF4845H   LINE      CODE     ---       #77
      00FF484BH   LINE      CODE     ---       #78
      00FF484EH   LINE      CODE     ---       #79
      00FF484EH   LINE      CODE     ---       #81
      00FF485BH   LINE      CODE     ---       #82
      00FF4863H   LINE      CODE     ---       #83
      00FF486EH   LINE      CODE     ---       #84
      00FF4877H   LINE      CODE     ---       #85
      00FF4882H   LINE      CODE     ---       #86
      00FF488DH   LINE      CODE     ---       #87
      00FF4893H   LINE      CODE     ---       #88
      00FF4895H   LINE      CODE     ---       #89
      00FF4895H   LINE      CODE     ---       #91
      00FF48A2H   LINE      CODE     ---       #92
      00FF48AAH   LINE      CODE     ---       #93
      00FF48B9H   LINE      CODE     ---       #94
      00FF48C2H   LINE      CODE     ---       #95
      00FF48CDH   LINE      CODE     ---       #96
      00FF48D8H   LINE      CODE     ---       #97
      00FF48E5H   LINE      CODE     ---       #98
      00FF48E5H   LINE      CODE     ---       #99
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 106


      00FF48E5H   LINE      CODE     ---       #100
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       SPI
      00FF8266H   PUBLIC    CODE     ---       ESD_SPI_ReadByte
      00FF77DFH   PUBLIC    CODE     ---       ESD_SPI_Init
      00FF7971H   PUBLIC    CODE     ---       ESD_SPI_ReadMultiBytes
      00FF829CH   PUBLIC    CODE     ---       ESD_SPI_WriteByte
      00FF7A9CH   PUBLIC    CODE     ---       ESD_SPI_WriteMultiBytes
      00FF8274H   PUBLIC    CODE     ---       ESD_SPI_RW
      00FF0016H   PUBLIC    CODE     ---       ESD_SPI_Deinit
      000000CFH   SFRSYM    DATA     BYTE      SPDAT
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF77DFH   BLOCK     CODE     ---       LVL=0
      WR6         REGSYM    ---      INT       spi_port
      00FF77DFH   LINE      CODE     ---       #6
      00FF77DFH   LINE      CODE     ---       #9
      00FF77E2H   LINE      CODE     ---       #14
      00FF77F4H   LINE      CODE     ---       #16
      00FF77F4H   LINE      CODE     ---       #17
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 107


      00FF77F8H   LINE      CODE     ---       #18
      00FF77FAH   LINE      CODE     ---       #19
      00FF77FAH   LINE      CODE     ---       #20
      00FF77FDH   LINE      CODE     ---       #21
      00FF77FFH   LINE      CODE     ---       #22
      00FF77FFH   LINE      CODE     ---       #23
      00FF7802H   LINE      CODE     ---       #24
      00FF7804H   LINE      CODE     ---       #25
      00FF7804H   LINE      CODE     ---       #26
      00FF7807H   LINE      CODE     ---       #27
      00FF7807H   LINE      CODE     ---       #28
      00FF7807H   LINE      CODE     ---       #29
      00FF7807H   LINE      CODE     ---       #30
      00FF7807H   LINE      CODE     ---       #31
      00FF780AH   LINE      CODE     ---       #32
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0016H   BLOCK     CODE     ---       LVL=0
      00FF0016H   LINE      CODE     ---       #37
      00FF0016H   LINE      CODE     ---       #39
      00FF0019H   LINE      CODE     ---       #40
      ---         BLOCKEND  ---      ---       LVL=0

      00FF829CH   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      out
      00FF829CH   LINE      CODE     ---       #46
      00FF829CH   LINE      CODE     ---       #48
      00FF829FH   LINE      CODE     ---       #49
      00FF82A4H   LINE      CODE     ---       #50
      00FF82A7H   LINE      CODE     ---       #51
      ---         BLOCKEND  ---      ---       LVL=0

      00FF8274H   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      dat
      00FF8274H   LINE      CODE     ---       #58
      00FF8274H   LINE      CODE     ---       #60
      00FF8277H   LINE      CODE     ---       #61
      00FF827CH   LINE      CODE     ---       #62
      00FF827FH   LINE      CODE     ---       #63
      00FF8281H   LINE      CODE     ---       #64
      ---         BLOCKEND  ---      ---       LVL=0

      00FF8266H   BLOCK     CODE     ---       LVL=0
      00FF8266H   LINE      CODE     ---       #70
      00FF8266H   LINE      CODE     ---       #72
      00FF8269H   LINE      CODE     ---       #73
      00FF826EH   LINE      CODE     ---       #74
      00FF8271H   LINE      CODE     ---       #75
      00FF8273H   LINE      CODE     ---       #76
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7A9CH   BLOCK     CODE     ---       LVL=0
      REG=7       REGSYM    ---      ---       tx_ptr
      WR8         REGSYM    ---      WORD      n
      00FF7AA0H   BLOCK     CODE     NEAR LAB  LVL=1
      WR6         REGSYM    ---      WORD      i
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7A9CH   LINE      CODE     ---       #83
      00FF7AA0H   LINE      CODE     ---       #84
      00FF7AA0H   LINE      CODE     ---       #86
      00FF7AA4H   LINE      CODE     ---       #88
      00FF7AADH   LINE      CODE     ---       #89
      00FF7AB2H   LINE      CODE     ---       #90
      00FF7AB5H   LINE      CODE     ---       #91
      00FF7ABBH   LINE      CODE     ---       #92
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 108


      ---         BLOCKEND  ---      ---       LVL=0

      00FF7971H   BLOCK     CODE     ---       LVL=0
      REG=7       REGSYM    ---      ---       rx_ptr
      WR8         REGSYM    ---      WORD      n
      00FF7975H   BLOCK     CODE     NEAR LAB  LVL=1
      WR6         REGSYM    ---      WORD      i
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7971H   LINE      CODE     ---       #99
      00FF7975H   LINE      CODE     ---       #100
      00FF7975H   LINE      CODE     ---       #102
      00FF7979H   LINE      CODE     ---       #104
      00FF797CH   LINE      CODE     ---       #105
      00FF7981H   LINE      CODE     ---       #106
      00FF7984H   LINE      CODE     ---       #107
      00FF798DH   LINE      CODE     ---       #108
      00FF7993H   LINE      CODE     ---       #109
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ?C?FPADD
      00FF01D4H   PUBLIC    CODE     ---       ?C?FPADD
      00FF01D1H   PUBLIC    CODE     ---       ?C?FPSUB

      ---         MODULE    ---      ---       ?C?FPMUL
      00FF028CH   PUBLIC    CODE     ---       ?C?FPMUL

      ---         MODULE    ---      ---       ?C?FPDIV
      00FF032CH   PUBLIC    CODE     ---       ?C?FPDIV

      ---         MODULE    ---      ---       ?C?FPCMP
      00FF03AFH   PUBLIC    CODE     ---       ?C?FPCMP
      00FF03ADH   PUBLIC    CODE     ---       ?C?FPCMP3

      ---         MODULE    ---      ---       ?C?FPNEG
      00FF03F3H   PUBLIC    CODE     ---       ?C?FPNEG

      ---         MODULE    ---      ---       ?C?FCAST
      00FF040BH   PUBLIC    CODE     NEAR LAB  ?C?FCASTC
      00FF0406H   PUBLIC    CODE     NEAR LAB  ?C?FCASTI
      00FF0401H   PUBLIC    CODE     NEAR LAB  ?C?FCASTL

      ---         MODULE    ---      ---       ?C?CASTF
      00FF043EH   PUBLIC    CODE     NEAR LAB  ?C?CASTF

      ---         MODULE    ---      ---       SPRINTF
      0000010CH   PUBLIC    EDATA    ---       ?SPRINTF?BYTE
      00FF047BH   PUBLIC    CODE     NEAR LAB  SPRINTF

      ---         MODULE    ---      ---       VSPRINTF
      000002CFH   PUBLIC    EDATA    ---       ?VSPRINTF?BYTE
      00FF049BH   PUBLIC    CODE     NEAR LAB  VSPRINTF

      ---         MODULE    ---      ---       FABS
      00FF04B7H   PUBLIC    CODE     NEAR LAB  FABS?_

      ---         MODULE    ---      ---       LOG10?_
      00FF04C6H   PUBLIC    CODE     NEAR LAB  LOG10?_

      ---         MODULE    ---      ---       SQRT?_
      00FF04D4H   PUBLIC    CODE     ---       SQRT?_

      ---         MODULE    ---      ---       ASIN?_
      00FF0564H   PUBLIC    CODE     ---       ASIN?_

      ---         MODULE    ---      ---       floor
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 109


      00FF68CCH   PUBLIC    CODE     ---       floor?_

      ---         MODULE    ---      ---       ?C?FPGETOPN
      00FF0584H   PUBLIC    CODE     ---       ?C?FPGETOPN2
      00FF05C1H   PUBLIC    CODE     ---       ?C?FPNANRESULT
      00FF05C9H   PUBLIC    CODE     ---       ?C?FPOVERFLOW
      00FF059DH   PUBLIC    CODE     ---       ?C?FPRESULT
      00FF05B3H   PUBLIC    CODE     ---       ?C?FPRESULT2
      00FF05C6H   PUBLIC    CODE     ---       ?C?FPUNDERFLOW

      ---         MODULE    ---      ---       ?C?PRNFMT
      00FF0602H   PUBLIC    CODE     ---       ?C?PRNFMT

      ---         MODULE    ---      ---       LOG?_
      00FF0A3DH   PUBLIC    CODE     NEAR LAB  LOG?_

      ---         MODULE    ---      ---       ATAN?_
      00FF0B22H   PUBLIC    CODE     ---       ATAN?_

      ---         MODULE    ---      ---       ?C?FPCONVERT
      00FF0C02H   PUBLIC    CODE     ---       ?C?FPCONVERT
      00FF0CBAH   PUBLIC    CODE     ---       ?C?FPROUND

      ---         MODULE    ---      ---       ?C?FPSERIES
      00FF0CFDH   PUBLIC    CODE     ---       ?C?FP2SERIES
      00FF0D06H   PUBLIC    CODE     ---       ?C?FPSERIES

      ---         MODULE    ---      ---       ?C?FTNPWR
      00FF0D5AH   PUBLIC    CODE     ---       ?C?FTNPWR

      ---         MODULE    ---      ---       ?C_START
      00FF0000H   PUBLIC    CODE     ---       ?C?STARTUP
      00FF0000H   PUBLIC    CODE     ---       ?C_STARTUP

      ---         MODULE    ---      ---       ?C?INITEDATA
      00FF6D8FH   PUBLIC    CODE     ---       ?C?INITEDATA

      ---         MODULE    ---      ---       ?C?SIDIV
      00FF0D91H   PUBLIC    CODE     ---       ?C?SIDIV

      ---         MODULE    ---      ---       ?C?LMUL
      00FF0DC3H   PUBLIC    CODE     ---       ?C?LMUL

      ---         MODULE    ---      ---       ?C?ULDIV
      00FF0DD6H   PUBLIC    CODE     NEAR LAB  ?C?ULDIV
      00FF0DD4H   PUBLIC    CODE     NEAR LAB  ?C?ULIDIV

      ---         MODULE    ---      ---       ?C?SLDIV
      00FF0E27H   PUBLIC    CODE     NEAR LAB  ?C?SLDIV

      ---         MODULE    ---      ---       ABS
      00FF820CH   PUBLIC    CODE     ---       abs?_

      ---         MODULE    ---      ---       LABS
      00FF821CH   PUBLIC    CODE     ---       labs?_

      ---         MODULE    ---      ---       STRLEN
      00FF8282H   PUBLIC    CODE     ---       strlen?_

      ---         MODULE    ---      ---       memcpy
      00FF794CH   PUBLIC    CODE     ---       memcpy?_

      ---         MODULE    ---      ---       ?C?INITEDATA_END
      00FF84DFH   PUBLIC    HCONST   WORD      ?C?INITEDATA_END

L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  15:35:33  PAGE 110


Program Size: data=10.4 edata+hdata=982 xdata=195 const=695 code=33332
L251 RUN COMPLETE.  0 WARNING(S),  0 ERROR(S)
