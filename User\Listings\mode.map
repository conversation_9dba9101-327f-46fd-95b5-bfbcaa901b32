L251 LINKER/LOCATER V4.66.93.0                                                          07/26/2025  14:45:41  PAGE 1


L251 LINKER/LOCATER V4.66.93.0, INVOKED BY:
D:\KEIL_V5\C251\BIN\L251.EXE .\Objects\main.obj, .\Objects\isr.obj, .\Objects\config.obj, ..\Driver\stc_usb_cdc_32g.lib,
>>  .\Objects\MPU6050.obj, .\Objects\OLED_SPI.obj, .\Objects\MKS.obj, .\Objects\D2Car.obj, .\Objects\ADC.obj, .\Objects\
>> Delay.obj, .\Objects\GPIO.obj, .\Objects\IIC.obj, .\Objects\INT.obj, .\Objects\PIT.obj, .\Objects\PWM.obj, .\Objects\
>> UART.obj, .\Objects\EEPROM.obj, .\Objects\CAN.obj, .\Objects\DMA.obj, .\Objects\SPI.obj TO .\Objects\mode PRINT (.\Li
>> stings\mode.map) CASE DISABLEWARNING (57) CLASSES (EDATA (0X0-0XFFF), HDATA (0X0-0XFFF))


CPU MODE:     251 SOURCE MODE
INTR FRAME:   2 BYTES SAVED ON INTERRUPT
MEMORY MODEL: XSMALL WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\main.obj (main)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\isr.obj (isr)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\config.obj (config)
         COMMENT TYPE 0: C251 V5.60.0
  ..\Driver\stc_usb_cdc_32g.lib (usb)
         COMMENT TYPE 0: C251 V5.60.0
  ..\Driver\stc_usb_cdc_32g.lib (util)
         COMMENT TYPE 0: C251 V5.60.0
  ..\Driver\stc_usb_cdc_32g.lib (usb_req_class)
         COMMENT TYPE 0: C251 V5.60.0
  ..\Driver\stc_usb_cdc_32g.lib (usb_req_std)
         COMMENT TYPE 0: C251 V5.60.0
  ..\Driver\stc_usb_cdc_32g.lib (usb_req_vendor)
         COMMENT TYPE 0: C251 V5.60.0
  ..\Driver\stc_usb_cdc_32g.lib (usb_desc)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\MPU6050.obj (MPU6050)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\OLED_SPI.obj (OLED_SPI)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\MKS.obj (MKS)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\D2Car.obj (D2Car)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\ADC.obj (ADC)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\Delay.obj (Delay)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\GPIO.obj (GPIO)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\IIC.obj (IIC)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\INT.obj (INT)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\PIT.obj (PIT)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\PWM.obj (PWM)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\UART.obj (UART)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\EEPROM.obj (EEPROM)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\CAN.obj (CAN)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\DMA.obj (DMA)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\SPI.obj (SPI)
         COMMENT TYPE 0: C251 V5.60.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FPADD)
         COMMENT TYPE 0: A251 V4.69.6.0
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 2


  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FPMUL)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FPDIV)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FPCMP)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FPNEG)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FCAST)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?CASTF)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (SPRINTF)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (VSPRINTF)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (FABS)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (LOG10?_)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (SQRT?_)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (ASIN?_)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (floor)
         COMMENT TYPE 0: C251 V5.58.7
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FPGETOPN)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?PRNFMT)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (LOG?_)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (ATAN?_)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FPCONVERT)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FPSERIES)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FTNPWR)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (?C_START)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (?C?INITEDATA)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (?C?SIDIV)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (?C?LMUL)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (?C?ULDIV)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (?C?SLDIV)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (ABS)
         COMMENT TYPE 0: C251 V5.58.7
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (LABS)
         COMMENT TYPE 0: C251 V5.58.7
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (STRLEN)
         COMMENT TYPE 0: C251 V5.58.7
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (memcpy)
         COMMENT TYPE 0: C251 V5.58.7
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (?C?INITEDATA_END)
         COMMENT TYPE 0: A251 V4.69.6.0


ACTIVE MEMORY CLASSES OF MODULE:  .\Objects\mode (main)
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 3



BASE        START       END         USED      MEMORY CLASS
==========================================================
000000H     000000H     000FFFH     0003FCH   EDATA
000000H     000000H     000FFFH               HDATA
000000H     FF0000H     FFFFFFH     0002A5H   HCONST
FF0000H     FF0000H     FFFFFFH     007DACH   CODE
000020H.0   000020H.0   00002FH.7   000002H.0 BIT
010000H     010000H     01FFFFH     0000C0H   XDATA
FF0000H     FF0000H     FFFFFFH     000004H   CONST
000000H     000000H     00007FH     000008H   DATA


MEMORY MAP OF MODULE:  .\Objects\mode (main)


START     STOP      LENGTH    ALIGN  RELOC    MEMORY CLASS   SEGMENT NAME
=========================================================================
000000H   000007H   000008H   ---    AT..     DATA           "REG BANK 0"
000008H   00001FH   000018H   BYTE   UNIT     EDATA          ?ED?IMUUPDATE?MPU6050
000020H.0 000020H.2 000000H.3 BIT    UNIT     BIT            ?BI?MAIN
000020H.3 000020H.5 000000H.3 BIT    UNIT     BIT            ?BI?USB
000020H.6 000020H.7 000000H.2 BIT    UNIT     BIT            ?BI?CONFIG
000021H.0 000021H.0 000000H.1 BIT    UNIT     BIT            ?BI?CAN_MKS_CONTROL?MKS
000021H.1 000021H.1 000000H.1 BIT    UNIT     BIT            ?BI?ESD_M1_POS_CONTROL?D2CAR
000021H.2 000021H.2 000000H.1 BIT    UNIT     BIT            ?BI?GET_IO?GPIO
000021H.3 000021H.3 000000H.1 BIT    UNIT     BIT            ?BI?OUT_IO?GPIO
000021H.4 000021H.4 000000H.1 BIT    UNIT     BIT            ?BI?CAN
000021H.5 000021H.5 000000H.1 BIT    UNIT     BIT            ?BI?CANINIT?CAN
000021H.6 000021H.6 000000H.1 BIT    UNIT     BIT            ?BI?CANREADMSG?CAN
000021H.7 000021H.7 000000H.1 BIT    UNIT     BIT            ?BI?CANSENDMSG?CAN
000022H   0000B4H   000093H   BYTE   UNIT     EDATA          ?ED?MPU6050
0000B5H   0000FBH   000047H   BYTE   UNIT     EDATA          ?ED?D2CAR
0000FCH   00012FH   000034H   BYTE   UNIT     EDATA          _EDATA_GROUP_
000130H   00015BH   00002CH   BYTE   UNIT     EDATA          ?ED?SEG7_SHOWSTRING?UTIL
00015CH   000185H   00002AH   BYTE   UNIT     EDATA          ?ED?OLED_SHOWFLOAT?OLED_SPI
000186H   0001ADH   000028H   BYTE   UNIT     EDATA          ?ED?SPRINTF
0001AEH   0001D4H   000027H   BYTE   UNIT     EDATA          ?ED?OLED_SHOWINT?OLED_SPI
0001D5H   0001F6H   000022H   BYTE   UNIT     EDATA          ?ED?MAIN
0001F7H   000213H   00001DH   BYTE   UNIT     EDATA          ?ED?CONFIG
000214H   000229H   000016H   BYTE   UNIT     EDATA          ?ED?MKS
00022AH   00023EH   000015H   BYTE   UNIT     EDATA          ?ED?CANREADMSG?CAN
00023FH   000252H   000014H   BYTE   UNIT     EDATA          ?ED?USB
000253H   000262H   000010H   BYTE   UNIT     EDATA          ?ED?UART_SEND_FLOAT?UART
000263H   00026EH   00000CH   BYTE   UNIT     EDATA          ?ED?UART_SEND_INT?UART
00026FH   000278H   00000AH   BYTE   UNIT     EDATA          ?ED?PIT
000279H   000281H   000009H   BYTE   UNIT     EDATA          ?ED?CANSENDMSG?CAN
000282H   000289H   000008H   BYTE   UNIT     EDATA          ?ED?REVERSE4?UTIL
00028AH   000291H   000008H   BYTE   UNIT     EDATA          ?ED?MPU6050_GET_GYRO?MPU6050
000292H   000299H   000008H   BYTE   UNIT     EDATA          ?ED?OLED_SHOWNUM?OLED_SPI
00029AH   0002A1H   000008H   BYTE   UNIT     EDATA          ?ED?OLED_DRAWBMP?OLED_SPI
0002A2H   0002A9H   000008H   BYTE   UNIT     EDATA          ?ED?GPIO_INIT_PIN?GPIO
0002AAH   0002B1H   000008H   BYTE   UNIT     EDATA          ?ED?GPIO_PULL_PIN?GPIO
0002B2H   0002B9H   000008H   BYTE   UNIT     EDATA          ?ED?GPIO_ISR_INIT?GPIO
0002BAH   0002C0H   000007H   BYTE   UNIT     EDATA          ?ED?USB_REQ_CLASS
0002C1H   0002C6H   000006H   BYTE   UNIT     EDATA          ?ED?MPU6050_GET_ACC?MPU6050
0002C7H   0002CBH   000005H   BYTE   UNIT     EDATA          ?ED?USB_SENDDATA?USB
0002CCH   0002D0H   000005H   BYTE   UNIT     EDATA          ?ED?LCD12864_SETBUFFER?UTIL
0002D1H   0002D5H   000005H   BYTE   UNIT     EDATA          ?ED?OLED12864_SETBUFFER?UTIL
0002D6H   0002DAH   000005H   BYTE   UNIT     EDATA          ?ED?OLED_SHOWCHAR?OLED_SPI
0002DBH   0002DFH   000005H   BYTE   UNIT     EDATA          ?ED?CAN
0002E0H   0002E3H   000004H   BYTE   UNIT     EDATA          ?ED?LIMIT_INT?CONFIG
0002E4H   0002E7H   000004H   BYTE   UNIT     EDATA          ?ED?LIMIT_FLOAT?CONFIG
0002E8H   0002EBH   000004H   BYTE   UNIT     EDATA          ?ED?SEG7_SHOWLONG?UTIL
0002ECH   0002EFH   000004H   BYTE   UNIT     EDATA          ?ED?OLED_SHOWSTRING?OLED_SPI
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 4


0002F0H   0002F3H   000004H   BYTE   UNIT     EDATA          ?ED?OLED_SHOWCHINESE?OLED_SPI
0002F4H   0002F7H   000004H   BYTE   UNIT     EDATA          ?ED?ESD_WRITE_IIC?IIC
0002F8H   0002FBH   000004H   BYTE   UNIT     EDATA          ?ED?ESD_READ_IIC?IIC
0002FCH   0002FFH   000004H   BYTE   UNIT     EDATA          ?ED?UART
000300H   000302H   000003H   BYTE   UNIT     EDATA          ?ED?MPU6050_SIMIIC_READ_REGS?MPU6050
000303H   000304H   000002H   BYTE   UNIT     EDATA          ?ED?LCD12864_SHOWSTRING?UTIL
000305H   000305H   000001H   BYTE   UNIT     EDATA          ?ED?UART_SEND_STRING?UART
000306H   000405H   000100H   BYTE   UNIT     EDATA          ?STACK
000406H   00FFFFH   00FBFAH   ---    ---      **GAP**
010000H   0100BFH   0000C0H   BYTE   INSEG    XDATA          ?XD?USB
0100C0H   FEFFFFH   FDFF40H   ---    ---      **GAP**
FF0000H   FF0002H   000003H   ---    OFFS..   CODE           ?CO?start251?4
FF0003H   FF0005H   000003H   ---    OFFS..   CODE           ?PR?IV?0
FF0006H   FF000AH   000005H   BYTE   INSEG    CODE           ?PR?SQ?CONFIG
FF000BH   FF000DH   000003H   ---    OFFS..   CODE           ?PR?IV?1
FF000EH   FF0011H   000004H   BYTE   INSEG    CODE           ?PR?ESD_SPI_DEINIT?SPI
FF0012H   FF0012H   000001H   BYTE   INSEG    CODE           ?PR?IIC_I?ISR
FF0013H   FF0015H   000003H   ---    OFFS..   CODE           ?PR?IV?2
FF0016H   FF0018H   000003H   BYTE   INSEG    CODE           ?PR?HID_ISR?CONFIG
FF0019H   FF0019H   000001H   BYTE   INSEG    CODE           ?PR?CMP_I?ISR
FF001AH   FF001AH   000001H   BYTE   INSEG    CODE           ?PR?LVD_I?ISR
FF001BH   FF001DH   000003H   ---    OFFS..   CODE           ?PR?IV?3
FF001EH   FF0020H   000003H   BYTE   INSEG    CODE           ?PR?USB_SET_DESCRIPTOR?USB_REQ_STD
FF0021H   FF0021H   000001H   BYTE   INSEG    CODE           ?PR?SPI_I?ISR
FF0022H   FF0022H   000001H   BYTE   INSEG    CODE           ?PR?DMA_ADC_ISR?ISR
FF0023H   FF0025H   000003H   ---    OFFS..   CODE           ?PR?IV?4
FF0026H   FF0028H   000003H   BYTE   INSEG    CODE           ?PR?USB_SYNCH_FRAME?USB_REQ_STD
FF0029H   FF0029H   000001H   BYTE   INSEG    CODE           ?PR?DMA_M2M_ISR?ISR
FF002AH   FF002AH   000001H   BYTE   INSEG    CODE           ?PR?INT0_I?ISR
FF002BH   FF002DH   000003H   ---    OFFS..   CODE           ?PR?IV?5
FF002EH   FF0030H   000003H   BYTE   INSEG    CODE           ?PR?USB_REQ_VENDOR?USB_REQ_VENDOR
FF0031H   FF0031H   000001H   BYTE   INSEG    CODE           ?PR?INT1_I?ISR
FF0032H   FF0032H   000001H   BYTE   INSEG    CODE           ?PR?INT3_I?ISR
FF0033H   FF0035H   000003H   ---    OFFS..   CODE           ?PR?IV?6
FF0036H   FF0042H   00000DH   BYTE   INSEG    CODE           ?PR?USB_SETUP_STATUS?USB
FF0043H   FF0045H   000003H   ---    OFFS..   CODE           ?PR?IV?8
FF0046H   FF0046H   000001H   BYTE   INSEG    CODE           ?PR?INT4_I?ISR
FF0047H   FF0047H   000001H   BYTE   INSEG    CODE           ?PR?DMA_SPI_ISR?ISR
FF0048H   FF0048H   000001H   BYTE   INSEG    CODE           ?PR?TIMER1_I?ISR
FF0049H   FF0049H   000001H   BYTE   INSEG    CODE           ?PR?TIMER2_I?ISR
FF004AH   FF004AH   000001H   BYTE   INSEG    CODE           ?PR?PWMA_I?ISR
FF004BH   FF004DH   000003H   ---    OFFS..   CODE           ?PR?IV?9
FF004EH   FF004EH   000001H   BYTE   INSEG    CODE           ?PR?TIMER3_I?ISR
FF004FH   FF004FH   000001H   BYTE   INSEG    CODE           ?PR?PWMB_I?ISR
FF0050H   FF0050H   000001H   BYTE   INSEG    CODE           ?PR?TIMER4_I?ISR
FF0051H   FF0051H   000001H   BYTE   INSEG    CODE           ?PR?ADC_I?ISR
FF0052H   FF0052H   000001H   BYTE   INSEG    CODE           ?PR?USB_SUSPEND?USB
FF0053H   FF0055H   000003H   ---    OFFS..   CODE           ?PR?IV?10
FF0056H   FF0056H   000001H   BYTE   INSEG    CODE           ?PR?USB_RESUME?USB
FF0057H   FF0057H   000001H   BYTE   INSEG    CODE           ?PR?MKS_ALL_RUN?MKS
FF0058H   FF005AH   000003H   ---    ---      **GAP**
FF005BH   FF005DH   000003H   ---    OFFS..   CODE           ?PR?IV?11
FF005EH   FF0061H   000004H   BYTE   UNIT     CONST          ?CO?PRINTF
FF0062H   FF0062H   000001H   ---    ---      **GAP**
FF0063H   FF0065H   000003H   ---    OFFS..   CODE           ?PR?IV?12
FF0066H   FF0082H   00001DH   BYTE   INSEG    CODE           ?PR?DMA_UART1_TXD_ISR?ISR
FF0083H   FF0085H   000003H   ---    OFFS..   CODE           ?PR?IV?16
FF0086H   FF008AH   000005H   ---    ---      **GAP**
FF008BH   FF008DH   000003H   ---    OFFS..   CODE           ?PR?IV?17
FF008EH   FF0092H   000005H   ---    ---      **GAP**
FF0093H   FF0095H   000003H   ---    OFFS..   CODE           ?PR?IV?18
FF0096H   FF009AH   000005H   ---    ---      **GAP**
FF009BH   FF009DH   000003H   ---    OFFS..   CODE           ?PR?IV?19
FF009EH   FF00A2H   000005H   ---    ---      **GAP**
FF00A3H   FF00A5H   000003H   ---    OFFS..   CODE           ?PR?IV?20
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 5


FF00A6H   FF00AAH   000005H   ---    ---      **GAP**
FF00ABH   FF00ADH   000003H   ---    OFFS..   CODE           ?PR?IV?21
FF00AEH   FF00C2H   000015H   BYTE   INSEG    CODE           ?PR?USB_BULK_INTR_IN?USB
FF00C3H   FF00C5H   000003H   ---    OFFS..   CODE           ?PR?IV?24
FF00C6H   FF00CAH   000005H   ---    ---      **GAP**
FF00CBH   FF00CDH   000003H   ---    OFFS..   CODE           ?PR?IV?25
FF00CEH   FF00D2H   000005H   ---    ---      **GAP**
FF00D3H   FF00D5H   000003H   ---    OFFS..   CODE           ?PR?IV?26
FF00D6H   FF00DAH   000005H   ---    ---      **GAP**
FF00DBH   FF00DDH   000003H   ---    OFFS..   CODE           ?PR?IV?27
FF00DEH   FF00E2H   000005H   ---    ---      **GAP**
FF00E3H   FF00E5H   000003H   ---    OFFS..   CODE           ?PR?IV?28
FF00E6H   FF00EAH   000005H   ---    ---      **GAP**
FF00EBH   FF00EDH   000003H   ---    OFFS..   CODE           ?PR?IV?29
FF00EEH   FF012AH   00003DH   BYTE   INSEG    CODE           ?PR?SEG7_SHOWLONG?UTIL
FF012BH   FF012DH   000003H   ---    OFFS..   CODE           ?PR?IV?37
FF012EH   FF0132H   000005H   ---    ---      **GAP**
FF0133H   FF0135H   000003H   ---    OFFS..   CODE           ?PR?IV?38
FF0136H   FF013AH   000005H   ---    ---      **GAP**
FF013BH   FF013DH   000003H   ---    OFFS..   CODE           ?PR?IV?39
FF013EH   FF0142H   000005H   ---    ---      **GAP**
FF0143H   FF0145H   000003H   ---    OFFS..   CODE           ?PR?IV?40
FF0146H   FF014AH   000005H   ---    ---      **GAP**
FF014BH   FF014DH   000003H   ---    OFFS..   CODE           ?PR?IV?41
FF014EH   FF0152H   000005H   ---    ---      **GAP**
FF0153H   FF0155H   000003H   ---    OFFS..   CODE           ?PR?IV?42
FF0156H   FF015AH   000005H   ---    ---      **GAP**
FF015BH   FF015DH   000003H   ---    OFFS..   CODE           ?PR?IV?43
FF015EH   FF0162H   000005H   ---    ---      **GAP**
FF0163H   FF0165H   000003H   ---    OFFS..   CODE           ?PR?IV?44
FF0166H   FF017AH   000015H   BYTE   INSEG    CODE           ?PR?USB_READ_REG?USB
FF017BH   FF017DH   000003H   ---    OFFS..   CODE           ?PR?IV?47
FF017EH   FF0182H   000005H   ---    ---      **GAP**
FF0183H   FF0185H   000003H   ---    OFFS..   CODE           ?PR?IV?48
FF0186H   FF018AH   000005H   ---    ---      **GAP**
FF018BH   FF018DH   000003H   ---    OFFS..   CODE           ?PR?IV?49
FF018EH   FF0192H   000005H   ---    ---      **GAP**
FF0193H   FF0195H   000003H   ---    OFFS..   CODE           ?PR?IV?50
FF0196H   FF019AH   000005H   ---    ---      **GAP**
FF019BH   FF019DH   000003H   ---    OFFS..   CODE           ?PR?IV?51
FF019EH   FF01A2H   000005H   ---    ---      **GAP**
FF01A3H   FF01A5H   000003H   ---    OFFS..   CODE           ?PR?IV?52
FF01A6H   FF01AAH   000005H   ---    ---      **GAP**
FF01ABH   FF01ADH   000003H   ---    OFFS..   CODE           ?PR?IV?53
FF01AEH   FF01B2H   000005H   ---    ---      **GAP**
FF01B3H   FF01B5H   000003H   ---    OFFS..   CODE           ?PR?IV?54
FF01B6H   FF01BAH   000005H   ---    ---      **GAP**
FF01BBH   FF01BDH   000003H   ---    OFFS..   CODE           ?PR?IV?55
FF01BEH   FF01C2H   000005H   ---    ---      **GAP**
FF01C3H   FF01C5H   000003H   ---    OFFS..   CODE           ?PR?IV?56
FF01C6H   FF01CAH   000005H   ---    ---      **GAP**
FF01CBH   FF01CDH   000003H   ---    OFFS..   CODE           ?PR?IV?57
FF01CEH   FF0E5AH   000C8DH   BYTE   UNIT     CODE           ?C?LIB_CODE
FF0E5BH   FF1792H   000938H   BYTE   UNIT     CODE           ?CO?OLED_SPI
FF1793H   FF1FAFH   00081DH   BYTE   INSEG    CODE           ?PR?UART_SEND_FLOAT?UART
FF1FB0H   FF2738H   000789H   BYTE   INSEG    CODE           ?PR?UART_SEND_INT?UART
FF2739H   FF2D0BH   0005D3H   BYTE   INSEG    CODE           ?PR?GPIO_ISR_INIT?GPIO
FF2D0CH   FF30BDH   0003B2H   BYTE   INSEG    CODE           ?PR?IMUUPDATE?MPU6050
FF30BEH   FF3409H   00034CH   BYTE   INSEG    CODE           ?PR?PWM_INIT?PWM
FF340AH   FF3717H   00030EH   BYTE   INSEG    CODE           ?PR?GPIO_INIT_PIN?GPIO
FF3718H   FF399EH   000287H   BYTE   INSEG    CODE           ?PR?GET_IO?GPIO
FF399FH   FF3BC8H   00022AH   BYTE   INSEG    CODE           ?PR?PWM_CHANGE?PWM
FF3BC9H   FF3DE9H   000221H   BYTE   INSEG    CODE           ?PR?OUT_IO?GPIO
FF3DEAH   FF3FF7H   00020EH   BYTE   INSEG    CODE           ?PR?CANINIT?CAN
FF3FF8H   FF41A4H   0001ADH   BYTE   INSEG    CODE           ?PR?KALMAN_FILTER?MPU6050
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 6


FF41A5H   FF434BH   0001A7H   BYTE   INSEG    CODE           ?PR?GPIO_PULL_PIN?GPIO
FF434CH   FF44C9H   00017EH   BYTE   INSEG    CODE           ?PR?GPIO_INIT_8PIN?GPIO
FF44CAH   FF4635H   00016CH   BYTE   INSEG    CODE           ?PR?UART_INIT?UART
FF4636H   FF479DH   000168H   BYTE   INSEG    CODE           ?PR?CANSENDMSG?CAN
FF479EH   FF48E5H   000148H   BYTE   INSEG    CODE           ?PR?DMA_RXD_INIT?DMA
FF48E6H   FF4A25H   000140H   BYTE   INSEG    CODE           ?PR?DMA_TXD_INIT?DMA
FF4A26H   FF4B42H   00011DH   BYTE   INSEG    CODE           ?PR?SET_CLK?CONFIG
FF4B43H   FF4C55H   000113H   BYTE   INSEG    CODE           ?PR?CAR_ADD_ANGLE?D2CAR
FF4C56H   FF4D62H   00010DH   BYTE   INSEG    CODE           ?PR?GPIO_ISR_DEINIT?GPIO
FF4D63H   FF4E66H   000104H   BYTE   INSEG    CODE           ?PR?OLED_SHOWFLOAT?OLED_SPI
FF4E67H   FF4F69H   000103H   BYTE   INSEG    CODE           ?PR?USB_GET_STATUS?USB_REQ_STD
FF4F6AH   FF5051H   0000E8H   BYTE   INSEG    CODE           ?PR?OLED_SHOWNUM?OLED_SPI
FF5052H   FF5135H   0000E4H   BYTE   INSEG    CODE           ?PR?CAN_MKS_CONTROL?MKS
FF5136H   FF520DH   0000D8H   BYTE   INSEG    CODE           ?PR?MKS_PID_SET?MKS
FF520EH   FF52E4H   0000D7H   BYTE   INSEG    CODE           ?PR?OLED_INIT?OLED_SPI
FF52E5H   FF53ADH   0000C9H   BYTE   INSEG    CODE           ?PR?MKS_HOME?MKS
FF53AEH   FF5471H   0000C4H   BYTE   INSEG    CODE           ?PR?USB_OUT_EP1?USB
FF5472H   FF5534H   0000C3H   BYTE   INSEG    CODE           ?PR?OLED_SHOWCHAR?OLED_SPI
FF5535H   FF55F5H   0000C1H   BYTE   INSEG    CODE           ?PR?CAR_PID_CONTROL?D2CAR
FF55F6H   FF56AEH   0000B9H   BYTE   INSEG    CODE           ?PR?CANREADMSG?CAN
FF56AFH   FF5753H   0000A5H   BYTE   INSEG    CODE           ?PR?USB_SET_CONFIGURATION?USB_REQ_STD
FF5754H   FF57F3H   0000A0H   BYTE   INSEG    CODE           ?PR?ESD_INIT_IIC?IIC
FF57F4H   FF5892H   00009FH   BYTE   INSEG    CODE           ?PR?USB_CLEAR_FEATURE?USB_REQ_STD
FF5893H   FF5931H   00009FH   BYTE   INSEG    CODE           ?PR?USB_GET_DESCRIPTOR?USB_REQ_STD
FF5932H   FF59CDH   00009CH   BYTE   INSEG    CODE           ?PR?MAIN?MAIN
FF59CEH   FF5A66H   000099H   BYTE   INSEG    CODE           ?PR?USB_SET_FEATURE?USB_REQ_STD
FF5A67H   FF5AFEH   000098H   BYTE   INSEG    CODE           ?PR?OLED12864_SETBUFFER?UTIL
FF5AFFH   FF5B96H   000098H   BYTE   INSEG    CODE           ?PR?LCD12864_SETBUFFER?UTIL
FF5B97H   FF5C2EH   000098H   BYTE   INSEG    CODE           ?PR?OLED_SHOWINT?OLED_SPI
FF5C2FH   FF5CC5H   000097H   BYTE   INSEG    CODE           ?PR?OLED_DRAWBMP?OLED_SPI
FF5CC6H   FF5D5CH   000097H   BYTE   INSEG    CODE           ?PR?CAN_MKS_SPD_CONTROL?MKS
FF5D5DH   FF5DF2H   000096H   BYTE   INSEG    CODE           ?PR?CANREADFIFO?CAN
FF5DF3H   FF5E85H   000093H   BYTE   INSEG    CODE           ?PR?PIT_INIT_US?PIT
FF5E86H   FF5F11H   00008CH   BYTE   INSEG    CODE           ?PR?PIT_INIT_MS?PIT
FF5F12H   FF5F9BH   00008AH   BYTE   INSEG    CODE           ?PR?USB_SETUP?USB
FF5F9CH   FF601FH   000084H   BYTE   INSEG    CODE           ?PR?USB_ISR?USB
FF6020H   FF60A2H   000083H   BYTE   UNIT     CODE           ?CO?USB_DESC
FF60A3H   FF6124H   000082H   BYTE   INSEG    CODE           ?PR?ESD_READ_IIC?IIC
FF6125H   FF61A0H   00007CH   BYTE   INSEG    CODE           ?PR?OLED_SHOWCHINESE?OLED_SPI
FF61A1H   FF6213H   000073H   BYTE   INSEG    CODE           ?PR?OLED_SHOWSTRING?OLED_SPI
FF6214H   FF6283H   000070H   BYTE   INSEG    CODE           ?PR?SYSTEM_INIT?CONFIG
FF6284H   FF62F2H   00006FH   BYTE   INSEG    CODE           ?PR?USB_SENDDATA?USB
FF62F3H   FF6361H   00006FH   BYTE   INSEG    CODE           ?PR?ESD_M1_POS_CONTROL?D2CAR
FF6362H   FF63CEH   00006DH   BYTE   INSEG    CODE           ?PR?ADC_INIT?ADC
FF63CFH   FF643AH   00006CH   BYTE   INSEG    CODE           ?PR?MPU6050_IIC_INIT?MPU6050
FF643BH   FF64A5H   00006BH   BYTE   INSEG    CODE           ?PR?CAN1_I?ISR
FF64A6H   FF6510H   00006BH   BYTE   INSEG    CODE           ?PR?CAN2_I?ISR
FF6511H   FF657BH   00006BH   BYTE   INSEG    CODE           ?PR?FLOOR?_?FLOOR
FF657CH   FF65E2H   000067H   BYTE   INSEG    CODE           ?PR?USB_CTRL_IN?USB
FF65E3H   FF6648H   000066H   BYTE   INSEG    CODE           ?PR?GPIO_INIT_ALLPIN?GPIO
FF6649H   FF66A9H   000061H   BYTE   INSEG    CODE           ?PR?REVERSE4?UTIL
FF66AAH   FF6707H   00005EH   BYTE   INSEG    CODE           ?PR?USB_INIT?USB
FF6708H   FF6763H   00005CH   BYTE   INSEG    CODE           ?PR?USB_REQ_STD?USB_REQ_STD
FF6764H   FF67BBH   000058H   BYTE   INSEG    CODE           ?PR?ESD_WRITE_IIC?IIC
FF67BCH   FF6810H   000055H   BYTE   INSEG    CODE           ?PR?MPU6050_SIMIIC_READ_REGS?MPU6050
FF6811H   FF6864H   000054H   BYTE   INSEG    CODE           ?PR?USB_GET_CONFIGURATION?USB_REQ_STD
FF6865H   FF68B8H   000054H   BYTE   INSEG    CODE           ?PR?MPU6050_INIT?MPU6050
FF68B9H   FF690BH   000053H   BYTE   INSEG    CODE           ?PR?PIT_COUNT_CLEAN?PIT
FF690CH   FF695CH   000051H   BYTE   INSEG    CODE           ?PR?UART_SEND_BYTE?UART
FF695DH   FF69ACH   000050H   BYTE   INSEG    CODE           ?PR?MPU6050_GET_GYRO?MPU6050
FF69ADH   FF69FBH   00004FH   BYTE   INSEG    CODE           ?PR?LCD12864_SHOWPICTURE?UTIL
FF69FCH   FF6A48H   00004DH   BYTE   INSEG    CODE           ?PR?USB_CTRL_OUT?USB
FF6A49H   FF6A95H   00004DH   BYTE   INSEG    CODE           ?PR?PIT_COUNT_GET?PIT
FF6A96H   FF6AE1H   00004CH   BYTE   INSEG    CODE           ?PR?PIT_INIT_ENCODER?PIT
FF6AE2H   FF6AF4H   000013H   BYTE   UNIT     CODE           ?C_C51STARTUP
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 7


FF6AF5H   FF6B00H   00000CH   BYTE   UNIT     CODE           ?C_C51STARTUP?1
FF6B01H   FF6B29H   000029H   BYTE   UNIT     CODE           ?C_C51STARTUP?2
FF6B2AH   FF6B2CH   000003H   BYTE   UNIT     CODE           ?C_C51STARTUP?3
FF6B2DH   FF6B76H   00004AH   BYTE   INSEG    CODE           ?PR?USB_SET_ADDRESS?USB_REQ_STD
FF6B77H   FF6BBFH   000049H   BYTE   INSEG    CODE           ?PR?SEG7_SHOWSTRING?UTIL
FF6BC0H   FF6C07H   000048H   BYTE   INSEG    CODE           ?PR?USB_RESET?USB
FF6C08H   FF6C4EH   000047H   BYTE   INSEG    CODE           ?PR?LED40_SENDDATA?UTIL
FF6C4FH   FF6C95H   000047H   BYTE   INSEG    CODE           ?PR?LED64_SENDDATA?UTIL
FF6C96H   FF6CD9H   000044H   BYTE   INSEG    CODE           ?PR?ADC_GET?ADC
FF6CDAH   FF6D1BH   000042H   BYTE   INSEG    CODE           ?PR?DMA_UART1_RXD_ISR?ISR
FF6D1CH   FF6D5DH   000042H   BYTE   INSEG    CODE           ?PR?DMA_UART2_RXD_ISR?ISR
FF6D5EH   FF6D9FH   000042H   BYTE   INSEG    CODE           ?PR?DMA_UART3_RXD_ISR?ISR
FF6DA0H   FF6DE1H   000042H   BYTE   INSEG    CODE           ?PR?DMA_UART4_RXD_ISR?ISR
FF6DE2H   FF6E22H   000041H   BYTE   INSEG    CODE           ?PR?YIJIELVBO?MPU6050
FF6E23H   FF6E62H   000040H   BYTE   INSEG    CODE           ?PR?OLED12864_SHOWPICTURE?UTIL
FF6E63H   FF6EA2H   000040H   BYTE   INSEG    CODE           ?PR?MPU6050_GET_ACC?MPU6050
FF6EA3H   FF6EE1H   00003FH   BYTE   INSEG    CODE           ?PR?KEY_RST?CONFIG
FF6EE2H   FF6F1FH   00003EH   BYTE   INSEG    CODE           ?PR?USB_GET_INTERFACE?USB_REQ_STD
FF6F20H   FF6F5CH   00003DH   BYTE   INSEG    CODE           ?PR?LCD12864_SHOWSTRING?UTIL
FF6F5DH   FF6F97H   00003BH   BYTE   INSEG    CODE           ?PR?MPU6050_READ_CH?MPU6050
FF6F98H   FF6FD1H   00003AH   BYTE   INSEG    CODE           ?PR?USB_READ_FIFO?_?USB
FF6FD2H   FF700BH   00003AH   BYTE   INSEG    CODE           ?PR?OLED12864_SCROLLRIGHT?UTIL
FF700CH   FF7045H   00003AH   BYTE   INSEG    CODE           ?PR?OLED12864_SCROLLUP?UTIL
FF7046H   FF707EH   000039H   BYTE   INSEG    CODE           ?PR?OLED12864_SCROLLLEFT?UTIL
FF707FH   FF70B4H   000036H   BYTE   INSEG    CODE           ?PR?TIMER0_I?ISR
FF70B5H   FF70EAH   000036H   BYTE   INSEG    CODE           ?PR?OLED_CLEAR?OLED_SPI
FF70EBH   FF711FH   000035H   BYTE   INSEG    CODE           ?PR?SEG7_SHOWCODE?UTIL
FF7120H   FF7154H   000035H   BYTE   INSEG    CODE           ?PR?REVERSE2?UTIL
FF7155H   FF7187H   000033H   BYTE   INSEG    CODE           ?PR?SEG7_SHOWFLOAT?UTIL
FF7188H   FF71BAH   000033H   BYTE   INSEG    CODE           ?PR?ESD_M1_SPD_CONTROL?D2CAR
FF71BBH   FF71ECH   000032H   BYTE   INSEG    CODE           ?PR?INT_INIT?INT
FF71EDH   FF721DH   000031H   BYTE   INSEG    CODE           ?PR?ESD_M1_PWM_CONTROL?D2CAR
FF721EH   FF724DH   000030H   BYTE   INSEG    CODE           ?PR?INT2_I?ISR
FF724EH   FF727CH   00002FH   BYTE   INSEG    CODE           ?PR?USB_IN?USB
FF727DH   FF72AAH   00002EH   BYTE   INSEG    CODE           ?PR?MPU6050_SEND_CH?MPU6050
FF72ABH   FF72D7H   00002DH   BYTE   INSEG    CODE           ?PR?USB_IN_EP1?USB
FF72D8H   FF7304H   00002DH   BYTE   INSEG    CODE           ?PR?PRINTF_HID?UTIL
FF7305H   FF7330H   00002CH   BYTE   INSEG    CODE           ?PR?OLED_DISPLAYTURN?OLED_SPI
FF7331H   FF735CH   00002CH   BYTE   INSEG    CODE           ?PR?EEPROM_READ?EEPROM
FF735DH   FF7388H   00002CH   BYTE   INSEG    CODE           ?PR?EEPROM_CHANGE?EEPROM
FF7389H   FF73B4H   00002CH   BYTE   INSEG    CODE           ?PR?ESD_SPI_INIT?SPI
FF73B5H   FF73DFH   00002BH   BYTE   INSEG    CODE           ?PR?USB_IN_EP2?USB
FF73E0H   FF7408H   000029H   BYTE   INSEG    CODE           ?PR?USB_WRITE_FIFO?_?USB
FF7409H   FF7431H   000029H   BYTE   INSEG    CODE           ?PR?MPU6050_SIMIIC_READ_REG?MPU6050
FF7432H   FF745AH   000029H   BYTE   INSEG    CODE           ?PR?OLED_SET_POS?OLED_SPI
FF745BH   FF7482H   000028H   BYTE   INSEG    CODE           ?PR?USB_SET_INTERFACE?USB_REQ_STD
FF7483H   FF74A9H   000027H   BYTE   INSEG    CODE           ?PR?LCD12864_SCROLLUP?UTIL
FF74AAH   FF74CFH   000026H   BYTE   INSEG    CODE           ?PR?USB_GET_LINE_CODING?USB_REQ_CLASS
FF74D0H   FF74F5H   000026H   BYTE   INSEG    CODE           ?PR?USB_SET_LINE_CODING?USB_REQ_CLASS
FF74F6H   FF751AH   000025H   BYTE   INSEG    CODE           ?PR?MEMCPY?_?MEMCPY
FF751BH   FF753DH   000023H   BYTE   INSEG    CODE           ?PR?ESD_SPI_READMULTIBYTES?SPI
FF753EH   FF755FH   000022H   BYTE   INSEG    CODE           ?PR?MPU6050_SIMIIC_WRITE_REG?MPU6050
FF7560H   FF7581H   000022H   BYTE   INSEG    CODE           ?PR?EEPROM_DELETE?EEPROM
FF7582H   FF75A2H   000021H   BYTE   INSEG    CODE           ?PR?LIMIT_FLOAT?CONFIG
FF75A3H   FF75C3H   000021H   BYTE   INSEG    CODE           ?PR?OLED12864_SETADDRESSMODE?UTIL
FF75C4H   FF75E4H   000021H   BYTE   INSEG    CODE           ?PR?OLED12864_SETCONTRAST?UTIL
FF75E5H   FF7605H   000021H   BYTE   INSEG    CODE           ?PR?LCD12864_REVERSELINE?UTIL
FF7606H   FF7625H   000020H   BYTE   INSEG    CODE           ?PR?MPU6050_SIMIIC_SENDACK?MPU6050
FF7626H   FF7645H   000020H   BYTE   INSEG    CODE           ?PR?ESD_SPI_WRITEMULTIBYTES?SPI
FF7646H   FF7663H   00001EH   BYTE   INSEG    CODE           ?PR?PIN0_I?ISR
FF7664H   FF7681H   00001EH   BYTE   INSEG    CODE           ?PR?PIN1_I?ISR
FF7682H   FF769FH   00001EH   BYTE   INSEG    CODE           ?PR?PIN2_I?ISR
FF76A0H   FF76BDH   00001EH   BYTE   INSEG    CODE           ?PR?PIN3_I?ISR
FF76BEH   FF76DBH   00001EH   BYTE   INSEG    CODE           ?PR?PIN4_I?ISR
FF76DCH   FF76F9H   00001EH   BYTE   INSEG    CODE           ?PR?PIN5_I?ISR
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 8


FF76FAH   FF7717H   00001EH   BYTE   INSEG    CODE           ?PR?PIN6_I?ISR
FF7718H   FF7735H   00001EH   BYTE   INSEG    CODE           ?PR?PIN7_I?ISR
FF7736H   FF7753H   00001EH   BYTE   INSEG    CODE           ?PR?OLED_COLORTURN?OLED_SPI
FF7754H   FF7771H   00001EH   BYTE   INSEG    CODE           ?PR?UART_SEND_STRING?UART
FF7772H   FF778EH   00001DH   BYTE   INSEG    CODE           ?PR?DMA_UART2_TXD_ISR?ISR
FF778FH   FF77ABH   00001DH   BYTE   INSEG    CODE           ?PR?DMA_UART3_TXD_ISR?ISR
FF77ACH   FF77C8H   00001DH   BYTE   INSEG    CODE           ?PR?DMA_UART4_TXD_ISR?ISR
FF77C9H   FF77E5H   00001DH   BYTE   INSEG    CODE           ?PR?SLEEP_US?UTIL
FF77E6H   FF7801H   00001CH   BYTE   INSEG    CODE           ?PR?UART2_I?ISR
FF7802H   FF781DH   00001CH   BYTE   INSEG    CODE           ?PR?UART3_I?ISR
FF781EH   FF7839H   00001CH   BYTE   INSEG    CODE           ?PR?UART4_I?ISR
FF783AH   FF7855H   00001CH   BYTE   INSEG    CODE           ?PR?MPU6050_SCCB_WAITACK?MPU6050
FF7856H   FF7871H   00001CH   BYTE   INSEG    CODE           ?PR?ESD_IIC_RECV_DATA?IIC
FF7872H   FF788DH   00001CH   BYTE   INSEG    CODE           ?PR?ESD_IIC_READ_NACK_BYTE?IIC
FF788EH   FF78A9H   00001CH   BYTE   INSEG    CODE           ?PR?ESD_IIC_READ_ACK_BYTE?IIC
FF78AAH   FF78C4H   00001BH   BYTE   INSEG    CODE           ?PR?BCD2HEX?CONFIG
FF78C5H   FF78DFH   00001BH   BYTE   INSEG    CODE           ?PR?USB_REQ_CLASS?USB_REQ_CLASS
FF78E0H   FF78F9H   00001AH   BYTE   INSEG    CODE           ?PR?OLED12864_SCROLLSTART?UTIL
FF78FAH   FF7913H   00001AH   BYTE   INSEG    CODE           ?PR?OLED12864_DISPLAYON?UTIL
FF7914H   FF792DH   00001AH   BYTE   INSEG    CODE           ?PR?LCD12864_SCROLLRIGHT?UTIL
FF792EH   FF7947H   00001AH   BYTE   INSEG    CODE           ?PR?LCD12864_AUTOWRAPON?UTIL
FF7948H   FF7961H   00001AH   BYTE   INSEG    CODE           ?PR?OLED12864_VERTICALMIRROR?UTIL
FF7962H   FF797BH   00001AH   BYTE   INSEG    CODE           ?PR?LCD12864_CURSORRETURNHOME?UTIL
FF797CH   FF7995H   00001AH   BYTE   INSEG    CODE           ?PR?OLED12864_DISPLAYENTIRE?UTIL
FF7996H   FF79AFH   00001AH   BYTE   INSEG    CODE           ?PR?LCD12864_DISPLAYON?UTIL
FF79B0H   FF79C9H   00001AH   BYTE   INSEG    CODE           ?PR?LCD12864_CURSORMOVERIGHT?UTIL
FF79CAH   FF79E3H   00001AH   BYTE   INSEG    CODE           ?PR?LCD12864_CURSORON?UTIL
FF79E4H   FF79FDH   00001AH   BYTE   INSEG    CODE           ?PR?OLED_WR_BYTE?OLED_SPI
FF79FEH   FF7A17H   00001AH   BYTE   INSEG    CODE           ?PR?OLED_POW?OLED_SPI
FF7A18H   FF7A30H   000019H   BYTE   INSEG    CODE           ?PR?USB_BULK_INTR_OUT?USB
FF7A31H   FF7A49H   000019H   BYTE   INSEG    CODE           ?PR?OLED12864_DISPLAYOFF?UTIL
FF7A4AH   FF7A62H   000019H   BYTE   INSEG    CODE           ?PR?OLED12864_SETHEADER?UTIL
FF7A63H   FF7A7BH   000019H   BYTE   INSEG    CODE           ?PR?LCD12864_AUTOWRAPOFF?UTIL
FF7A7CH   FF7A94H   000019H   BYTE   INSEG    CODE           ?PR?OLED12864_SCROLLSTOP?UTIL
FF7A95H   FF7AADH   000019H   BYTE   INSEG    CODE           ?PR?LCD12864_DISPLAYOFF?UTIL
FF7AAEH   FF7AC6H   000019H   BYTE   INSEG    CODE           ?PR?LCD12864_SETHEADER?UTIL
FF7AC7H   FF7ADFH   000019H   BYTE   INSEG    CODE           ?PR?LCD12864_SCROLLLEFT?UTIL
FF7AE0H   FF7AF8H   000019H   BYTE   INSEG    CODE           ?PR?OLED12864_HORIZONTALMIRROR?UTIL
FF7AF9H   FF7B11H   000019H   BYTE   INSEG    CODE           ?PR?OLED12864_DISPLAYCONTENT?UTIL
FF7B12H   FF7B2AH   000019H   BYTE   INSEG    CODE           ?PR?LCD12864_CURSOROFF?UTIL
FF7B2BH   FF7B43H   000019H   BYTE   INSEG    CODE           ?PR?LCD12864_CURSORMOVELEFT?UTIL
FF7B44H   FF7B5CH   000019H   BYTE   INSEG    CODE           ?PR?SEG7_SETHEADER?UTIL
FF7B5DH   FF7B75H   000019H   BYTE   INSEG    CODE           ?PR?ESD_IIC_SEND_NACK?IIC
FF7B76H   FF7B8DH   000018H   BYTE   INSEG    CODE           ?PR?UART1_I?ISR
FF7B8EH   FF7BA5H   000018H   BYTE   INSEG    CODE           ?PR?USB_OUT_DONE?USB
FF7BA6H   FF7BBDH   000018H   BYTE   INSEG    CODE           ?PR?SLEEP_MS?UTIL
FF7BBEH   FF7BD5H   000018H   BYTE   INSEG    CODE           ?PR?ESD_IIC_SEND_ACK?IIC
FF7BD6H   FF7BECH   000017H   BYTE   INSEG    CODE           ?PR?LIMIT_INT?CONFIG
FF7BEDH   FF7C03H   000017H   BYTE   INSEG    CODE           ?PR?HEX2BCD?CONFIG
FF7C04H   FF7C1AH   000017H   BYTE   INSEG    CODE           ?PR?DELAY_X_MS?DELAY
FF7C1BH   FF7C31H   000017H   BYTE   INSEG    CODE           ?PR?DELAY_X_US?DELAY
FF7C32H   FF7C48H   000017H   BYTE   INSEG    CODE           ?PR?ESD_IIC_WRITE_START_BYTE?IIC
FF7C49H   FF7C5FH   000017H   BYTE   INSEG    CODE           ?PR?ESD_IIC_WAIT?IIC
FF7C60H   FF7C76H   000017H   BYTE   INSEG    CODE           ?PR?ESD_IIC_WRITE_ONE_BYTE?IIC
FF7C77H   FF7C8DH   000017H   BYTE   INSEG    CODE           ?PR?ESD_IIC_SEND_DATA?IIC
FF7C8EH   FF7CA3H   000016H   BYTE   INSEG    CODE           ?PR?USB_SET_CTRL_LINE_STATE?USB_REQ_CLASS
FF7CA4H   FF7CB8H   000015H   BYTE   INSEG    CODE           ?PR?OLED_DISPLAY_ON?OLED_SPI
FF7CB9H   FF7CCDH   000015H   BYTE   INSEG    CODE           ?PR?OLED_DISPLAY_OFF?OLED_SPI
FF7CCEH   FF7CE2H   000015H   BYTE   INSEG    CODE           ?PR?CANWRITEREG?CAN
FF7CE3H   FF7CF6H   000014H   BYTE   INSEG    CODE           ?PR?LCD12864_DISPLAYCLEAR?UTIL
FF7CF7H   FF7D0AH   000014H   BYTE   INSEG    CODE           ?PR?OLED12864_DISPLAYREVERSE?UTIL
FF7D0BH   FF7D1DH   000013H   BYTE   INSEG    CODE           ?PR?USB_WRITE_REG?USB
FF7D1EH   FF7D30H   000013H   BYTE   INSEG    CODE           ?PR?EEPROM_OFF?EEPROM
FF7D31H   FF7D43H   000013H   BYTE   INSEG    CODE           ?PR?CANREADREG?CAN
FF7D44H   FF7D54H   000011H   BYTE   INSEG    CODE           ?PR?USB_SETUP_IN?USB
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 9


FF7D55H   FF7D65H   000011H   BYTE   INSEG    CODE           ?PR?MPU6050_SIMIIC_STOP?MPU6050
FF7D66H   FF7D75H   000010H   BYTE   INSEG    CODE           ?PR?ESD_IIC_START?IIC
FF7D76H   FF7D85H   000010H   BYTE   INSEG    CODE           ?PR?ESD_IIC_RECV_ACK?IIC
FF7D86H   FF7D95H   000010H   BYTE   INSEG    CODE           ?PR?ESD_IIC_STOP?IIC
FF7D96H   FF7DA5H   000010H   BYTE   INSEG    CODE           ?PR?ABS?_?ABS
FF7DA6H   FF7DB5H   000010H   BYTE   INSEG    CODE           ?PR?LABS?_?LABS
FF7DB6H   FF7DC4H   00000FH   BYTE   INSEG    CODE           ?PR?FMAX?CONFIG
FF7DC5H   FF7DD3H   00000FH   BYTE   INSEG    CODE           ?PR?MPU6050_SIMIIC_START?MPU6050
FF7DD4H   FF7DE1H   00000EH   BYTE   INSEG    CODE           ?PR?USB_SETUP_OUT?USB
FF7DE2H   FF7DEFH   00000EH   BYTE   INSEG    CODE           ?PR?USB_SETUP_STALL?USB
FF7DF0H   FF7DFDH   00000EH   BYTE   INSEG    CODE           ?PR?ESD_SPI_READBYTE?SPI
FF7DFEH   FF7E0BH   00000EH   BYTE   INSEG    CODE           ?PR?ESD_SPI_RW?SPI
FF7E0CH   FF7E19H   00000EH   BYTE   INSEG    CODE           ?PR?STRLEN?_?STRLEN
FF7E1AH   FF7E25H   00000CH   BYTE   INSEG    CODE           ?PR?ESD_SPI_WRITEBYTE?SPI
FF7E26H   FF7E30H   00000BH   BYTE   INSEG    CODE           ?PR?MPU6050_SIMIIC_DELAY?MPU6050
FF7E31H   FF7E3AH   00000AH   BYTE   INSEG    CODE           ?PR?USB_RST?CONFIG
FF7E3BH   FF8042H   000208H   BYTE   UNIT     HCONST         ?C_INITEDATA
FF8043H   FF809AH   000058H   BYTE   UNIT     HCONST         ?HC?PWM
FF809BH   FF80B7H   00001DH   BYTE   UNIT     HCONST         ?HC?MAIN
FF80B8H   FF80CFH   000018H   BYTE   UNIT     HCONST         ?HC?GPIO
FF80D0H   FF80D8H   000009H   BYTE   UNIT     HCONST         ?HC?CONFIG
FF80D9H   FF80DFH   000007H   BYTE   UNIT     HCONST         ?HC?OLED_SPI



OVERLAY MAP OF MODULE:   .\Objects\mode (main)


FUNCTION/MODULE                            EDATA_GROUP
--> CALLED FUNCTION/MODULE                 START  STOP
======================================================
?C_C51STARTUP?1                            ----- -----

*** NEW ROOT *****************************

IIC_I/isr                                  ----- -----

*** NEW ROOT *****************************

CMP_I/isr                                  ----- -----

*** NEW ROOT *****************************

LVD_I/isr                                  ----- -----

*** NEW ROOT *****************************

SPI_I/isr                                  ----- -----

*** NEW ROOT *****************************

UART1_I/isr                                ----- -----

*** NEW ROOT *****************************

UART2_I/isr                                ----- -----

*** NEW ROOT *****************************

UART3_I/isr                                ----- -----

*** NEW ROOT *****************************

UART4_I/isr                                ----- -----

L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 10


*** NEW ROOT *****************************

DMA_UART1_RXD_isr/isr                      ----- -----

*** NEW ROOT *****************************

DMA_UART2_RXD_isr/isr                      ----- -----

*** NEW ROOT *****************************

DMA_UART3_RXD_isr/isr                      ----- -----

*** NEW ROOT *****************************

DMA_UART1_TXD_isr/isr                      ----- -----

*** NEW ROOT *****************************

DMA_UART4_RXD_isr/isr                      ----- -----

*** NEW ROOT *****************************

DMA_UART2_TXD_isr/isr                      ----- -----

*** NEW ROOT *****************************

DMA_UART3_TXD_isr/isr                      ----- -----

*** NEW ROOT *****************************

DMA_UART4_TXD_isr/isr                      ----- -----

*** NEW ROOT *****************************

DMA_ADC_isr/isr                            ----- -----

*** NEW ROOT *****************************

CAN1_I/isr                                 ----- -----
  +--> CanReadReg/CAN

CanReadReg/CAN                             ----- -----

*** NEW ROOT *****************************

CAN2_I/isr                                 ----- -----
  +--> CanReadReg/CAN

*** NEW ROOT *****************************

DMA_M2M_isr/isr                            ----- -----

*** NEW ROOT *****************************

PIN0_I/isr                                 ----- -----

*** NEW ROOT *****************************

PIN1_I/isr                                 ----- -----

*** NEW ROOT *****************************

PIN2_I/isr                                 ----- -----

*** NEW ROOT *****************************
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 11



PIN3_I/isr                                 ----- -----

*** NEW ROOT *****************************

PIN4_I/isr                                 ----- -----

*** NEW ROOT *****************************

INT0_I/isr                                 ----- -----

*** NEW ROOT *****************************

PIN5_I/isr                                 ----- -----

*** NEW ROOT *****************************

INT1_I/isr                                 ----- -----

*** NEW ROOT *****************************

PIN6_I/isr                                 ----- -----

*** NEW ROOT *****************************

INT2_I/isr                                 ----- -----

*** NEW ROOT *****************************

PIN7_I/isr                                 ----- -----

*** NEW ROOT *****************************

INT3_I/isr                                 ----- -----

*** NEW ROOT *****************************

INT4_I/isr                                 ----- -----

*** NEW ROOT *****************************

DMA_SPI_isr/isr                            ----- -----

*** NEW ROOT *****************************

TIMER0_I/isr                               ----- -----
  +--> Key_Rst/config
  +--> Usb_Rst/config

Key_Rst/config                             ----- -----
  +--> Delay_X_mS/Delay

Delay_X_mS/Delay                           ----- -----

Usb_Rst/config                             ----- -----
  +--> HID_isr/config
  +--> usb_OUT_done/usb

HID_isr/config                             ----- -----

usb_OUT_done/usb                           ----- -----
  +--> usb_write_reg/usb

usb_write_reg/usb                          ----- -----

L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 12


*** NEW ROOT *****************************

TIMER1_I/isr                               ----- -----

*** NEW ROOT *****************************

TIMER2_I/isr                               ----- -----

*** NEW ROOT *****************************

PWMA_I/isr                                 ----- -----

*** NEW ROOT *****************************

TIMER3_I/isr                               ----- -----

*** NEW ROOT *****************************

PWMB_I/isr                                 ----- -----

*** NEW ROOT *****************************

TIMER4_I/isr                               ----- -----

*** NEW ROOT *****************************

ADC_I/isr                                  ----- -----

*** NEW ROOT *****************************

usb_isr/usb                                ----- -----
  +--> usb_read_reg/usb
  +--> usb_resume/usb
  +--> usb_reset/usb
  +--> usb_setup/usb
  +--> usb_in_ep1/usb
  +--> usb_in_ep2/usb
  +--> usb_out_ep1/usb
  +--> usb_suspend/usb

usb_read_reg/usb                           ----- -----

usb_resume/usb                             ----- -----

usb_reset/usb                              ----- -----
  +--> usb_write_reg/usb

usb_setup/usb                              ----- -----
  +--> usb_write_reg/usb
  +--> usb_read_reg/usb
  +--> usb_read_fifo?_/usb
  +--> reverse2/util
  +--> usb_req_std/usb_req_std
  +--> usb_req_class/usb_req_class
  +--> usb_req_vendor/usb_req_vendor
  +--> usb_setup_stall/usb
  +--> usb_ctrl_in/usb
  +--> usb_ctrl_out/usb

usb_read_fifo?_/usb                        ----- -----
  +--> usb_read_reg/usb

reverse2/util                              00FCH 00FFH

usb_req_std/usb_req_std                    ----- -----
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 13


  +--> usb_get_status/usb_req_std
  +--> usb_clear_feature/usb_req_std
  +--> usb_set_feature/usb_req_std
  +--> usb_set_address/usb_req_std
  +--> usb_get_descriptor/usb_req_std
  +--> usb_set_descriptor/usb_req_std
  +--> usb_get_configuration/usb_req_std
  +--> usb_set_configuration/usb_req_std
  +--> usb_get_interface/usb_req_std
  +--> usb_set_interface/usb_req_std
  +--> usb_synch_frame/usb_req_std
  +--> usb_setup_stall/usb

usb_get_status/usb_req_std                 ----- -----
  +--> usb_setup_stall/usb
  +--> usb_setup_in/usb

usb_setup_stall/usb                        ----- -----
  +--> usb_write_reg/usb

usb_setup_in/usb                           ----- -----
  +--> usb_write_reg/usb
  +--> usb_ctrl_in/usb

usb_ctrl_in/usb                            ----- -----
  +--> usb_write_reg/usb
  +--> usb_read_reg/usb
  +--> usb_write_fifo?_/usb

usb_write_fifo?_/usb                       ----- -----
  +--> usb_write_reg/usb

usb_clear_feature/usb_req_std              ----- -----
  +--> usb_write_reg/usb
  +--> usb_setup_stall/usb
  +--> usb_setup_status/usb

usb_setup_status/usb                       ----- -----
  +--> usb_write_reg/usb

usb_set_feature/usb_req_std                ----- -----
  +--> usb_write_reg/usb
  +--> usb_setup_stall/usb
  +--> usb_setup_status/usb

usb_set_address/usb_req_std                ----- -----
  +--> usb_setup_stall/usb
  +--> usb_write_reg/usb
  +--> usb_setup_status/usb

usb_get_descriptor/usb_req_std             ----- -----
  +--> usb_setup_stall/usb
  +--> usb_setup_in/usb

usb_set_descriptor/usb_req_std             ----- -----
  +--> usb_setup_stall/usb

usb_get_configuration/usb_req_std          ----- -----
  +--> usb_setup_stall/usb
  +--> usb_setup_in/usb

usb_set_configuration/usb_req_std          ----- -----
  +--> usb_setup_stall/usb
  +--> usb_write_reg/usb
  +--> usb_setup_status/usb
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 14



usb_get_interface/usb_req_std              ----- -----
  +--> usb_setup_stall/usb
  +--> usb_setup_in/usb

usb_set_interface/usb_req_std              ----- -----
  +--> usb_setup_stall/usb
  +--> usb_setup_status/usb

usb_synch_frame/usb_req_std                ----- -----
  +--> usb_setup_stall/usb

usb_req_class/usb_req_class                ----- -----
  +--> usb_set_line_coding/usb_req_class
  +--> usb_get_line_coding/usb_req_class
  +--> usb_set_ctrl_line_state/usb_req_class
  +--> usb_setup_stall/usb

usb_set_line_coding/usb_req_class          ----- -----
  +--> usb_setup_stall/usb
  +--> usb_setup_out/usb

usb_setup_out/usb                          ----- -----
  +--> usb_write_reg/usb

usb_get_line_coding/usb_req_class          ----- -----
  +--> usb_setup_stall/usb
  +--> usb_setup_in/usb

usb_set_ctrl_line_state/usb_req_class      ----- -----
  +--> usb_setup_stall/usb
  +--> usb_setup_status/usb

usb_req_vendor/usb_req_vendor              ----- -----
  +--> usb_setup_stall/usb

usb_ctrl_out/usb                           ----- -----
  +--> usb_write_reg/usb
  +--> usb_read_reg/usb
  +--> usb_read_fifo?_/usb

usb_in_ep1/usb                             ----- -----
  +--> usb_write_reg/usb
  +--> usb_read_reg/usb

usb_in_ep2/usb                             ----- -----
  +--> usb_write_reg/usb
  +--> usb_read_reg/usb

usb_out_ep1/usb                            ----- -----
  +--> usb_write_reg/usb
  +--> usb_read_reg/usb
  +--> usb_read_fifo?_/usb
  +--> sleep_ms/util

sleep_ms/util                              ----- -----
  +--> sleep_us/util

sleep_us/util                              ----- -----

usb_suspend/usb                            ----- -----

*** NEW ROOT *****************************

?C_C51STARTUP                              ----- -----
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 15



*** NEW ROOT *****************************

?C_C51STARTUP?3                            ----- -----
  +--> main/main

main/main                                  ----- -----
  +--> System_init/config
  +--> PIT_init_ms/PIT
  +--> INT_init/INT
  +--> PWM_init/PWM
  +--> printf_hid/util

System_init/config                         ----- -----
  +--> usb_init/usb

usb_init/usb                               ----- -----
  +--> usb_write_reg/usb

PIT_init_ms/PIT                            ----- -----
  +--> ?C?ULIDIV/?C?ULDIV

?C?ULIDIV/?C?ULDIV                         ----- -----

INT_init/INT                               ----- -----

PWM_init/PWM                               0100H 0103H
  +--> ?C?ULIDIV/?C?ULDIV

printf_hid/util                            0100H 012BH
  +--> VSPRINTF/VSPRINTF
  +--> usb_IN/usb

VSPRINTF/VSPRINTF                          012CH 012FH
  +--> OUT/VSPRINTF

OUT/VSPRINTF                               ----- -----
  +--> PUTCH/VSPRINTF

PUTCH/VSPRINTF                             ----- -----

usb_IN/usb                                 ----- -----
  +--> usb_write_reg/usb
  +--> usb_read_reg/usb
  +--> usb_bulk_intr_in/usb

usb_bulk_intr_in/usb                       ----- -----
  +--> usb_write_fifo?_/usb
  +--> usb_write_reg/usb

*** NEW ROOT *****************************

?C_C51STARTUP?2                            ----- -----



PUBLIC SYMBOLS OF MODULE:  .\Objects\mode (main)


      VALUE       CLASS    TYPE      PUBLIC SYMBOL NAME
      =================================================
      00FF043EH   CODE     NEAR LAB  ?C?CASTF
      000000FFH   NUMBER   ---       ?C?CODESEG
      00FF040BH   CODE     NEAR LAB  ?C?FCASTC
      00FF0406H   CODE     NEAR LAB  ?C?FCASTI
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 16


      00FF0401H   CODE     NEAR LAB  ?C?FCASTL
      00FF0CFDH   CODE     ---       ?C?FP2SERIES
      00FF01D4H   CODE     ---       ?C?FPADD
      00FF03AFH   CODE     ---       ?C?FPCMP
      00FF03ADH   CODE     ---       ?C?FPCMP3
      00FF0C02H   CODE     ---       ?C?FPCONVERT
      00FF032CH   CODE     ---       ?C?FPDIV
      00FF0584H   CODE     ---       ?C?FPGETOPN2
      00FF028CH   CODE     ---       ?C?FPMUL
      00FF05C1H   CODE     ---       ?C?FPNANRESULT
      00FF03F3H   CODE     ---       ?C?FPNEG
      00FF05C9H   CODE     ---       ?C?FPOVERFLOW
      00FF059DH   CODE     ---       ?C?FPRESULT
      00FF05B3H   CODE     ---       ?C?FPRESULT2
      00FF0CBAH   CODE     ---       ?C?FPROUND
      00FF0D06H   CODE     ---       ?C?FPSERIES
      00FF01D1H   CODE     ---       ?C?FPSUB
      00FF05C6H   CODE     ---       ?C?FPUNDERFLOW
      00FF0D5AH   CODE     ---       ?C?FTNPWR
      00FF6B01H   CODE     ---       ?C?INITEDATA
      00FF8041H   HCONST   WORD      ?C?INITEDATA_END
      00FF0DC3H   CODE     ---       ?C?LMUL
      00FF0602H   CODE     ---       ?C?PRNFMT
      00FF0D91H   CODE     ---       ?C?SIDIV
      00FF0E27H   CODE     NEAR LAB  ?C?SLDIV
      00FF0000H   CODE     ---       ?C?STARTUP
      00FF0DD6H   CODE     NEAR LAB  ?C?ULDIV
      00FF0DD4H   CODE     NEAR LAB  ?C?ULIDIV
      00000001H   NUMBER   ---       ?C?XDATASEG
      00FF0000H   CODE     ---       ?C_STARTUP
      00000021H.0 BIT      BIT       ?CAN_MKS_CONTROL?BIT
      00000021H.5 BIT      BIT       ?CANInit?BIT
      00000021H.6 BIT      BIT       ?CanReadMsg?BIT
      00000021H.7 BIT      BIT       ?CanSendMsg?BIT
      00000274H   EDATA    BYTE      ?CanSendMsg?BYTE
      00000021H.1 BIT      BIT       ?ESD_M1_POS_CONTROL?BIT
      00000004H   EDATA    BYTE      ?IMUupdate?BYTE
      000002DCH   EDATA    BYTE      ?Limit_float?BYTE
      000002D8H   EDATA    BYTE      ?Limit_int?BYTE
      00000021H.3 BIT      BIT       ?Out_IO?BIT
      00000128H   EDATA    BYTE      ?printf_hid?BYTE
      000000FAH   EDATA    BYTE      ?PWM_init?BYTE
      00000158H   EDATA    BYTE      ?SEG7_ShowString?BYTE
      000001AEH   EDATA    ---       ?sprintf?BYTE
      00000252H   EDATA    BYTE      ?UART_Send_float?BYTE
      00000262H   EDATA    BYTE      ?UART_Send_int?BYTE
      00000124H   EDATA    ---       ?vsprintf?BYTE
      00FF7D96H   CODE     ---       abs?_
      000000B5H   EDATA    INT       ACC_X
      000000B7H   EDATA    INT       ACC_Y
      000000B9H   EDATA    INT       ACC_Z
*SFR* 000000BCH   DATA     BYTE      ADC_CONTR
*SFR* 000000BCH.5 DATA     BIT       ADC_FLAG
      00FF6C96H   CODE     ---       ADC_get
      00FF0051H   CODE     ---       ADC_I
      00FF6362H   CODE     ---       ADC_init
*SFR* 000000BCH.7 DATA     BIT       ADC_POWER
*SFR* 000000BDH   DATA     BYTE      ADC_RES
*SFR* 000000BEH   DATA     BYTE      ADC_RESL
*SFR* 000000BCH.6 DATA     BIT       ADC_START
*SFR* 000000DEH   DATA     BYTE      ADCCFG
      000000C3H   EDATA    FLOAT     ANG_ERR
      000000DEH   EDATA    FLOAT     ANG_ERR_OLD
      000000CDH   EDATA    FLOAT     ANG_OUT
      00000030H   EDATA    FLOAT     angle
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 17


      00000082H   EDATA    FLOAT     angle_dot
      00000040H   EDATA    FLOAT     Angle_err
      00000092H   EDATA    FLOAT     AngleX
      00000096H   EDATA    FLOAT     AngleY
      00FF0E5BH   CODE     ---       asc2_0806
      00FF1083H   CODE     ---       asc2_1608
      00FF0564H   CODE     ---       asin?_
      00FF0B22H   CODE     ---       ATAN?_
*SFR* 000000EFH   DATA     BYTE      AUXINTIF
*SFR* 0000008EH   DATA     BYTE      AUXR
*SFR* 00000097H   DATA     BYTE      AUXR2
      00FF78AAH   CODE     ---       BCD2HEX
      000002DDH   EDATA    BYTE      BRP
      00000020H.4 BIT      BIT       bUsbFeatureReady
      00000020H.5 BIT      BIT       bUsbInBusy
      00000020H.3 BIT      BIT       bUsbOutReady
      000000A2H   EDATA    CHAR      C_0
      00FF643BH   CODE     ---       CAN1_I
      00FF64A6H   CODE     ---       CAN2_I
*SFR* 000000F1H.5 DATA     BIT       CAN2IE
      00FF5052H   CODE     ---       CAN_MKS_CONTROL
      00FF5CC6H   CODE     ---       CAN_MKS_SPD_CONTROL
      000002DEH   EDATA    WORD      CAN_time
      00000021H.4 BIT      BIT       CAN_TX_OK
*SFR* 000000F1H   DATA     BYTE      CANICR
*SFR* 000000F1H.1 DATA     BIT       CANIE
      00FF3DEAH   CODE     ---       CANInit
      00FF5D5DH   CODE     ---       CanReadFifo
      00FF55F6H   CODE     ---       CanReadMsg
      00FF7D31H   CODE     ---       CanReadReg
*SFR* 00000097H.3 DATA     BIT       CANSEL
      00FF4636H   CODE     ---       CanSendMsg
      00FF7CCEH   CODE     ---       CanWriteReg
      00FF4B43H   CODE     ---       CAR_ADD_ANGLE
      000000BBH   EDATA    FLOAT     CAR_ANG
      00FF5535H   CODE     ---       CAR_PID_CONTROL
      000001FBH   EDATA    ---       char_uchar
*SFR* 000000EAH   DATA     BYTE      CKCON
      00FF0019H   CODE     ---       CMP_I
*SFR* 000000E6H   DATA     BYTE      CMPCR1
*SFR* 000000E7H   DATA     BYTE      CMPCR2
      000001E3H   EDATA    INT       collision_counter
      000001D7H   EDATA    LONG      collision_pos
      00FF6054H   CODE     ---       CONFIGDESC
      000000D1H   EDATA    ---       D2_CAN_TX
      000000D9H   EDATA    ---       D2int_uchar
      000000BFH   EDATA    ---       D2long_uchar
      00000086H   EDATA    FLOAT     d_t
      00FF7C04H   CODE     ---       Delay_X_mS
      00FF7C1BH   CODE     ---       Delay_X_uS
      00FF6042H   CODE     ---       DEVICEDESC
      00000249H   EDATA    BYTE      DeviceState
      00FF0022H   CODE     ---       DMA_ADC_isr
      00FF0029H   CODE     ---       DMA_M2M_isr
      00FF479EH   CODE     ---       DMA_RXD_init
      00FF0047H   CODE     ---       DMA_SPI_isr
      00FF48E6H   CODE     ---       DMA_TXD_init
      00FF6CDAH   CODE     ---       DMA_UART1_RXD_isr
      00FF0066H   CODE     ---       DMA_UART1_TXD_isr
      00FF6D1CH   CODE     ---       DMA_UART2_RXD_isr
      00FF7772H   CODE     ---       DMA_UART2_TXD_isr
      00FF6D5EH   CODE     ---       DMA_UART3_RXD_isr
      00FF778FH   CODE     ---       DMA_UART3_TXD_isr
      00FF6DA0H   CODE     ---       DMA_UART4_RXD_isr
      00FF77ACH   CODE     ---       DMA_UART4_TXD_isr
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 18


      0000009AH   EDATA    FLOAT     E
*SFR* 000000A8H.7 DATA     BIT       EA
*SFR* 000000BAH.7 DATA     BIT       EAXFR
      00FF735DH   CODE     ---       EEPROM_Change
      00FF7560H   CODE     ---       EEPROM_Delete
      00FF7D1EH   CODE     ---       EEPROM_OFF
      00FF7331H   CODE     ---       EEPROM_Read
      0000024AH   EDATA    ---       Ep0State
      000001EFH   EDATA    ---       err_spd
*SFR* 000000A8H.4 DATA     BIT       ES
*SFR* 000000AFH.0 DATA     BIT       ES2
*SFR* 000000AFH.3 DATA     BIT       ES3
*SFR* 000000AFH.4 DATA     BIT       ES4
      00FF788EH   CODE     ---       ESD_IIC_READ_ACK_BYTE
      00FF7872H   CODE     ---       ESD_IIC_READ_NACK_BYTE
      00FF7D76H   CODE     ---       ESD_IIC_Recv_ack
      00FF7856H   CODE     ---       ESD_IIC_Recv_data
      00FF7BBEH   CODE     ---       ESD_IIC_Send_ack
      00FF7C77H   CODE     ---       ESD_IIC_Send_data
      00FF7B5DH   CODE     ---       ESD_IIC_Send_nack
      00FF7D66H   CODE     ---       ESD_IIC_Start
      00FF7D86H   CODE     ---       ESD_IIC_Stop
      00FF7C49H   CODE     ---       ESD_IIC_Wait
      00FF7C60H   CODE     ---       ESD_IIC_WRITE_ONE_BYTE
      00FF7C32H   CODE     ---       ESD_IIC_WRITE_START_BYTE
      00FF5754H   CODE     ---       ESD_Init_IIC
      00FF62F3H   CODE     ---       ESD_M1_POS_CONTROL
      00FF71EDH   CODE     ---       ESD_M1_PWM_CONTROL
      00FF7188H   CODE     ---       ESD_M1_SPD_CONTROL
      00FF60A3H   CODE     ---       ESD_Read_IIC
      00FF000EH   CODE     ---       ESD_SPI_Deinit
      00FF7389H   CODE     ---       ESD_SPI_Init
      00FF7DF0H   CODE     ---       ESD_SPI_ReadByte
      00FF751BH   CODE     ---       ESD_SPI_ReadMultiBytes
      00FF7DFEH   CODE     ---       ESD_SPI_RW
      00FF7E1AH   CODE     ---       ESD_SPI_WriteByte
      00FF7626H   CODE     ---       ESD_SPI_WriteMultiBytes
      00FF6764H   CODE     ---       ESD_Write_IIC
*SFR* 000000A8H.1 DATA     BIT       ET0
*SFR* 000000A8H.3 DATA     BIT       ET1
*SFR* 000000AFH.7 DATA     BIT       EUSB
*SFR* 000000A8H.0 DATA     BIT       EX0
*SFR* 000000A8H.2 DATA     BIT       EX1
      00000034H   EDATA    FLOAT     exInt
      00000038H   EDATA    FLOAT     eyInt
      0000003CH   EDATA    FLOAT     ezInt
      00FF04B7H   CODE     NEAR LAB  fabs?_
      000001F7H   EDATA    ---       float_uchar
      00FF6511H   CODE     ---       floor?_
      00FF7DB6H   CODE     ---       fmax
      00FF3718H   CODE     ---       Get_IO
      00FF434CH   CODE     ---       GPIO_init_8pin
      00FF65E3H   CODE     ---       GPIO_init_allpin
      00FF340AH   CODE     ---       GPIO_init_pin
      00FF4C56H   CODE     ---       GPIO_isr_deinit
      00FF2739H   CODE     ---       GPIO_isr_init
      00FF41A5H   CODE     ---       GPIO_pull_pin
      00000020H.0 BIT      BIT       gripper_mode
      000001D5H   EDATA    INT       gripper_state
      000000F0H   EDATA    FLOAT     GYRO_X
      000000F4H   EDATA    FLOAT     GYRO_Y
      000000F8H   EDATA    FLOAT     GYRO_Z
      000000CBH   EDATA    INT       GYRO_Z_OFFSET
      000000DBH   EDATA    INT       GYRO_Z_OFFSET_ADD
      000000DDH   EDATA    BYTE      GYRO_Z_OFFSET_N
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 19


      00FF7BEDH   CODE     ---       HEX2BCD
      00FF0016H   CODE     ---       HID_isr
      00FF1673H   CODE     ---       Hzk
*SFR* 000000F6H   DATA     BYTE      IAP_ADDRE
*SFR* 000000C3H   DATA     BYTE      IAP_ADDRH
*SFR* 000000C4H   DATA     BYTE      IAP_ADDRL
*SFR* 000000C5H   DATA     BYTE      IAP_CMD
*SFR* 000000C7H   DATA     BYTE      IAP_CONTR
*SFR* 000000C2H   DATA     BYTE      IAP_DATA
*SFR* 000000F5H   DATA     BYTE      IAP_TPS
*SFR* 000000C6H   DATA     BYTE      IAP_TRIG
*SFR* 000000A8H   DATA     BYTE      IE
*SFR* 000000AFH   DATA     BYTE      IE2
      00FF0012H   CODE     ---       IIC_I
      00FF2D0CH   CODE     ---       IMUupdate
      00000251H   EDATA    BYTE      InEpState
      00FF002AH   CODE     ---       INT0_I
      00FF0031H   CODE     ---       INT1_I
      00FF721EH   CODE     ---       INT2_I
      *EXTERN*    CODE     ---       INT2_isr
      00FF0032H   CODE     ---       INT3_I
      00FF0046H   CODE     ---       INT4_I
      00FF71BBH   CODE     ---       INT_init
      00000200H   EDATA    ---       int_uchar
*SFR* 0000008FH   DATA     BYTE      INTCLKO
*SFR* 000000B8H   DATA     BYTE      IP
*SFR* 000000B5H   DATA     BYTE      IP2
*SFR* 000000B6H   DATA     BYTE      IP2H
*SFR* 000000DFH   DATA     BYTE      IP3
*SFR* 000000EEH   DATA     BYTE      IP3H
*SFR* 000000B7H   DATA     BYTE      IPH
*SFR* 0000009DH   DATA     BYTE      IRCBAND
*SFR* 0000009FH   DATA     BYTE      IRTRIM
*SFR* 00000088H.0 DATA     BIT       IT0
*SFR* 00000088H.2 DATA     BIT       IT1
      000000B1H   EDATA    FLOAT     K1
      000000A3H   EDATA    FLOAT     K_0
      000000A7H   EDATA    FLOAT     K_1
      00FF3FF8H   CODE     ---       Kalman_Filter
*SFR* 000000B0H.2 DATA     BIT       KEY1
      00000020H.2 BIT      BIT       key1_flag
      00000020H.1 BIT      BIT       key1_old
      00000206H   EDATA    WORD      Key_cnt
      00000020H.6 BIT      BIT       Key_Flag
      00FF6EA3H   CODE     ---       Key_Rst
      00FF7DA6H   CODE     ---       labs?_
      00FF603EH   CODE     ---       LANGIDDESC
      00FF7A63H   CODE     ---       LCD12864_AutoWrapOff
      00FF792EH   CODE     ---       LCD12864_AutoWrapOn
      00FF7B2BH   CODE     ---       LCD12864_CursorMoveLeft
      00FF79B0H   CODE     ---       LCD12864_CursorMoveRight
      00FF7B12H   CODE     ---       LCD12864_CursorOff
      00FF79CAH   CODE     ---       LCD12864_CursorOn
      00FF7962H   CODE     ---       LCD12864_CursorReturnHome
      00FF7CE3H   CODE     ---       LCD12864_DisplayClear
      00FF7A95H   CODE     ---       LCD12864_DisplayOff
      00FF7996H   CODE     ---       LCD12864_DisplayOn
      00FF75E5H   CODE     ---       LCD12864_ReverseLine
      00FF7AC7H   CODE     ---       LCD12864_ScrollLeft
      00FF7914H   CODE     ---       LCD12864_ScrollRight
      00FF7483H   CODE     ---       LCD12864_ScrollUp
      00FF69ADH   CODE     ---       LCD12864_ShowPicture
      00FF6F20H   CODE     ---       LCD12864_ShowString
      00FF6C08H   CODE     ---       LED40_SendData
      00FF6C4FH   CODE     ---       LED64_SendData
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 20


      00FF7582H   CODE     ---       Limit_float
      00FF7BD6H   CODE     ---       Limit_int
      000002BAH   EDATA    ---       LineCoding
*SFR* 000000F9H   DATA     BYTE      LINICR
      00FF04C6H   CODE     NEAR LAB  log10?_
      00FF0A3DH   CODE     NEAR LAB  LOG?_
      000001FCH   EDATA    ---       long_uchar
      00000216H   EDATA    ---       long_uchar1
      00FF001AH   CODE     ---       LVD_I
      00FF5932H   CODE     ---       main
      00FF609BH   CODE     ---       MANUFACTDESC
      00FF74F6H   CODE     ---       memcpy?_
      00FF0057H   CODE     ---       MKS_ALL_RUN
      00FF52E5H   CODE     ---       MKS_HOME
      0000021AH   EDATA    WORD      MKS_KD
      0000021CH   EDATA    WORD      MKS_KI
      0000021EH   EDATA    WORD      MKS_KP
      00000220H   EDATA    WORD      MKS_KV
      00FF5136H   CODE     ---       MKS_PID_SET
      00000222H   EDATA    ---       MKS_TX
      000001EBH   EDATA    LONG      motor_pos
      000001DBH   EDATA    LONG      motor_pos_old
      000001E9H   EDATA    INT       motor_spd
      00000214H   EDATA    WORD      MOTOR_state
      00000022H   EDATA    INT       mpu6050_acc_x
      00000024H   EDATA    INT       mpu6050_acc_y
      0000002AH   EDATA    INT       mpu6050_acc_z
      00FF6E63H   CODE     ---       mpu6050_get_acc
      00FF695DH   CODE     ---       mpu6050_get_gyro
      0000005CH   EDATA    INT       mpu6050_gyro_x
      0000005EH   EDATA    INT       mpu6050_gyro_y
      00000060H   EDATA    INT       mpu6050_gyro_z
      00FF63CFH   CODE     ---       mpu6050_iic_init
      00FF6865H   CODE     ---       mpu6050_init
      00FF7409H   CODE     ---       mpu6050_simiic_read_reg
      00FF67BCH   CODE     ---       mpu6050_simiic_read_regs
      000000E2H   EDATA    ---       MPU_data
      000000ABH   EDATA    INT       mpu_temp
      00FF7AF9H   CODE     ---       OLED12864_DisplayContent
      00FF797CH   CODE     ---       OLED12864_DisplayEntire
      00FF7A31H   CODE     ---       OLED12864_DisplayOff
      00FF78FAH   CODE     ---       OLED12864_DisplayOn
      00FF7CF7H   CODE     ---       OLED12864_DisplayReverse
      00FF7AE0H   CODE     ---       OLED12864_HorizontalMirror
      00FF7046H   CODE     ---       OLED12864_ScrollLeft
      00FF6FD2H   CODE     ---       OLED12864_ScrollRight
      00FF78E0H   CODE     ---       OLED12864_ScrollStart
      00FF7A7CH   CODE     ---       OLED12864_ScrollStop
      00FF700CH   CODE     ---       OLED12864_ScrollUp
      00FF75A3H   CODE     ---       OLED12864_SetAddressMode
      00FF75C4H   CODE     ---       OLED12864_SetContrast
      00FF6E23H   CODE     ---       OLED12864_ShowPicture
      00FF7948H   CODE     ---       OLED12864_VerticalMirror
      00FF70B5H   CODE     ---       OLED_Clear
      00FF7736H   CODE     ---       OLED_ColorTurn
*SFR* 000000A0H.2 DATA     BIT       OLED_CS
*SFR* 000000A0H.4 DATA     BIT       OLED_DC
      00FF7CB9H   CODE     ---       OLED_Display_Off
      00FF7CA4H   CODE     ---       OLED_Display_On
      00FF7305H   CODE     ---       OLED_DisplayTurn
      00FF5C2FH   CODE     ---       OLED_DrawBMP
      00FF520EH   CODE     ---       OLED_Init
      00FF79FEH   CODE     ---       oled_pow
*SFR* 000000A0H.6 DATA     BIT       OLED_RES
      00FF7432H   CODE     ---       OLED_Set_Pos
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 21


      00FF5472H   CODE     ---       OLED_ShowChar
      00FF6125H   CODE     ---       OLED_ShowChinese
      00FF4D63H   CODE     ---       OLED_ShowFloat
      00FF5B97H   CODE     ---       OLED_ShowInt
      00FF4F6AH   CODE     ---       OLED_ShowNum
      00FF61A1H   CODE     ---       OLED_ShowString
      00FF79E4H   CODE     ---       OLED_WR_Byte
      00FF3BC9H   CODE     ---       Out_IO
      00000252H   EDATA    BYTE      OutEpState
      00000247H   EDATA    BYTE      OutNumber
*SFR* 00000080H   DATA     BYTE      P0
*SFR* 00000080H.0 DATA     BIT       P00
*SFR* 00000080H.1 DATA     BIT       P01
*SFR* 00000080H.2 DATA     BIT       P02
*SFR* 00000080H.3 DATA     BIT       P03
*SFR* 00000080H.4 DATA     BIT       P04
*SFR* 00000080H.5 DATA     BIT       P05
*SFR* 00000080H.6 DATA     BIT       P06
*SFR* 00000080H.7 DATA     BIT       P07
*SFR* 00000094H   DATA     BYTE      P0M0
*SFR* 00000093H   DATA     BYTE      P0M1
*SFR* 00000090H   DATA     BYTE      P1
*SFR* 00000090H.0 DATA     BIT       P10
*SFR* 00000090H.1 DATA     BIT       P11
*SFR* 00000090H.2 DATA     BIT       P12
*SFR* 00000090H.3 DATA     BIT       P13
*SFR* 00000090H.4 DATA     BIT       P14
*SFR* 00000090H.5 DATA     BIT       P15
*SFR* 00000090H.6 DATA     BIT       P16
*SFR* 00000090H.7 DATA     BIT       P17
*SFR* 00000092H   DATA     BYTE      P1M0
*SFR* 00000091H   DATA     BYTE      P1M1
*SFR* 000000A0H   DATA     BYTE      P2
*SFR* 000000A0H.0 DATA     BIT       P20
*SFR* 000000A0H.1 DATA     BIT       P21
*SFR* 000000A0H.2 DATA     BIT       P22
*SFR* 000000A0H.3 DATA     BIT       P23
*SFR* 000000A0H.4 DATA     BIT       P24
*SFR* 000000A0H.5 DATA     BIT       P25
*SFR* 000000A0H.6 DATA     BIT       P26
*SFR* 000000A0H.7 DATA     BIT       P27
*SFR* 00000096H   DATA     BYTE      P2M0
*SFR* 00000095H   DATA     BYTE      P2M1
*SFR* 000000B0H   DATA     BYTE      P3
*SFR* 000000B0H.0 DATA     BIT       P30
*SFR* 000000B0H.1 DATA     BIT       P31
*SFR* 000000B0H.2 DATA     BIT       P32
*SFR* 000000B0H.3 DATA     BIT       P33
*SFR* 000000B0H.4 DATA     BIT       P34
*SFR* 000000B0H.5 DATA     BIT       P35
*SFR* 000000B0H.6 DATA     BIT       P36
*SFR* 000000B0H.7 DATA     BIT       P37
*SFR* 000000B2H   DATA     BYTE      P3M0
*SFR* 000000B1H   DATA     BYTE      P3M1
*SFR* 000000C0H   DATA     BYTE      P4
*SFR* 000000C0H.0 DATA     BIT       P40
*SFR* 000000C0H.1 DATA     BIT       P41
*SFR* 000000C0H.2 DATA     BIT       P42
*SFR* 000000C0H.3 DATA     BIT       P43
*SFR* 000000C0H.4 DATA     BIT       P44
*SFR* 000000B4H   DATA     BYTE      P4M0
*SFR* 000000B3H   DATA     BYTE      P4M1
*SFR* 000000C8H   DATA     BYTE      P5
*SFR* 000000C8H.0 DATA     BIT       P50
*SFR* 000000C8H.1 DATA     BIT       P51
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 22


*SFR* 000000C8H.2 DATA     BIT       P52
*SFR* 000000C8H.3 DATA     BIT       P53
*SFR* 000000C8H.4 DATA     BIT       P54
*SFR* 000000C8H.5 DATA     BIT       P55
*SFR* 000000CAH   DATA     BYTE      P5M0
*SFR* 000000C9H   DATA     BYTE      P5M1
*SFR* 000000E8H   DATA     BYTE      P6
*SFR* 000000E8H.0 DATA     BIT       P60
*SFR* 000000E8H.1 DATA     BIT       P61
*SFR* 000000E8H.2 DATA     BIT       P62
*SFR* 000000E8H.3 DATA     BIT       P63
*SFR* 000000E8H.4 DATA     BIT       P64
*SFR* 000000E8H.5 DATA     BIT       P65
*SFR* 000000E8H.6 DATA     BIT       P66
*SFR* 000000E8H.7 DATA     BIT       P67
*SFR* 000000CCH   DATA     BYTE      P6M0
*SFR* 000000CBH   DATA     BYTE      P6M1
*SFR* 000000F8H   DATA     BYTE      P7
*SFR* 000000F8H.0 DATA     BIT       P70
*SFR* 000000F8H.1 DATA     BIT       P71
*SFR* 000000F8H.2 DATA     BIT       P72
*SFR* 000000F8H.3 DATA     BIT       P73
*SFR* 000000F8H.4 DATA     BIT       P74
*SFR* 000000F8H.5 DATA     BIT       P75
*SFR* 000000F8H.6 DATA     BIT       P76
*SFR* 000000F8H.7 DATA     BIT       P77
*SFR* 000000E2H   DATA     BYTE      P7M0
*SFR* 000000E1H   DATA     BYTE      P7M1
*SFR* 000000A2H   DATA     BYTE      P_SW1
*SFR* 000000BAH   DATA     BYTE      P_SW2
*SFR* 000000BBH   DATA     BYTE      P_SW3
      00FF6097H   CODE     ---       PACKET0
      00FF6099H   CODE     ---       PACKET1
*SFR* 00000087H   DATA     BYTE      PCON
      00000044H   EDATA    FLOAT     PCt_0
      00000058H   EDATA    FLOAT     PCt_1
      00000048H   EDATA    ---       Pdot
      00FF7646H   CODE     ---       PIN0_I
      00FF7664H   CODE     ---       PIN1_I
      00FF7682H   CODE     ---       PIN2_I
      00FF76A0H   CODE     ---       PIN3_I
      00FF76BEH   CODE     ---       PIN4_I
      00FF76DCH   CODE     ---       PIN5_I
      00FF76FAH   CODE     ---       PIN6_I
      00FF7718H   CODE     ---       PIN7_I
      00FF68B9H   CODE     ---       PIT_count_clean
      00FF6A49H   CODE     ---       PIT_count_get
      00FF6A96H   CODE     ---       PIT_init_encoder
      00FF5E86H   CODE     ---       PIT_init_ms
      00FF5DF3H   CODE     ---       PIT_init_us
      00000062H   EDATA    ---       PP
      00FF72D8H   CODE     ---       printf_hid
      00FF6020H   CODE     ---       PRODUCTDESC
*SFR* 000000D0H   DATA     BYTE      PSW
*SFR* 000000B5H.7 DATA     BIT       PUSB
*SFR* 000000B6H.7 DATA     BIT       PUSBH
      00FF8063H   HCONST   ---       PWM_ARR_ADDR
      00FF806BH   HCONST   ---       PWM_CCER_ADDR
      00FF807BH   HCONST   ---       PWM_CCMR_ADDR
      00FF8043H   HCONST   ---       PWM_CCR_ADDR
      00FF399FH   CODE     ---       PWM_change
      00FF30BEH   CODE     ---       PWM_init
      000001DFH   EDATA    LONG      pwm_out
      00FF004AH   CODE     ---       PWMA_I
      00FF004FH   CODE     ---       PWMB_I
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 23


      00000072H   EDATA    FLOAT     q0
      00000076H   EDATA    FLOAT     q1
      0000007AH   EDATA    FLOAT     q2
      0000007EH   EDATA    FLOAT     q3
      0000008AH   EDATA    FLOAT     Q_angle
      0000009EH   EDATA    FLOAT     Q_bias
      000000ADH   EDATA    FLOAT     Q_gyro
      0000008EH   EDATA    FLOAT     R_angle
*SFR* 000000B0H.2 DATA     BIT       Reset_PIN
      00FF7120H   CODE     ---       reverse2
      00FF6649H   CODE     ---       reverse4
*SFR* 00000098H.0 DATA     BIT       RI
*SFR* 000000FFH   DATA     BYTE      RSTCFG
*SFR* 0000009BH   DATA     BYTE      S2BUF
*SFR* 0000009AH   DATA     BYTE      S2CON
*SFR* 0000009AH.0 DATA     BIT       S2RI
*SFR* 0000009AH.1 DATA     BIT       S2TI
*SFR* 000000ADH   DATA     BYTE      S3BUF
*SFR* 000000ACH   DATA     BYTE      S3CON
*SFR* 000000ACH.0 DATA     BIT       S3RI
*SFR* 000000ACH.1 DATA     BIT       S3TI
*SFR* 000000FEH   DATA     BYTE      S4BUF
*SFR* 000000FDH   DATA     BYTE      S4CON
*SFR* 000000FDH.0 DATA     BIT       S4RI
*SFR* 000000FDH.1 DATA     BIT       S4TI
*SFR* 00000099H   DATA     BYTE      SBUF
*SFR* 00000098H   DATA     BYTE      SCON
      00FF70EBH   CODE     ---       SEG7_ShowCode
      00FF7155H   CODE     ---       SEG7_ShowFloat
      00FF00EEH   CODE     ---       SEG7_ShowLong
      00FF6B77H   CODE     ---       SEG7_ShowString
      000000C7H   EDATA    FLOAT     SET_ANG
      00FF4A26H   CODE     ---       set_clk
      000001F5H   EDATA    INT       set_spd
      0000023FH   EDATA    ---       Setup
      00FF7BA6H   CODE     ---       sleep_ms
      00FF77C9H   CODE     ---       sleep_us
*SFR* 000000CEH   DATA     BYTE      SPCTL
*SFR* 000000CFH   DATA     BYTE      SPDAT
      00FF0021H   CODE     ---       SPI_I
      00FF047BH   CODE     NEAR LAB  sprintf
*SFR* 000000CDH   DATA     BYTE      SPSTAT
      00FF0006H   CODE     ---       sq
      00FF04D4H   CODE     ---       sqrt?_
      00FF7E0CH   CODE     ---       strlen?_
      00000210H   EDATA    DWORD     sys_clk
      00FF6214H   CODE     ---       System_init
      0000026FH   EDATA    WORD      T0_cnt
      00000271H   EDATA    WORD      T1_cnt
      00000273H   EDATA    WORD      T2_cnt
*SFR* 000000D6H   DATA     BYTE      T2H
*SFR* 000000D7H   DATA     BYTE      T2L
*SFR* 0000008EH.4 DATA     BIT       T2R
*SFR* 0000008EH.2 DATA     BIT       T2x12
      00000275H   EDATA    WORD      T3_cnt
*SFR* 000000D4H   DATA     BYTE      T3H
*SFR* 000000D5H   DATA     BYTE      T3L
*SFR* 000000DDH.3 DATA     BIT       T3R
*SFR* 000000DDH.1 DATA     BIT       T3x12
      00000277H   EDATA    WORD      T4_cnt
*SFR* 000000D2H   DATA     BYTE      T4H
*SFR* 000000D3H   DATA     BYTE      T4L
*SFR* 000000DDH.7 DATA     BIT       T4R
*SFR* 000000DDH   DATA     BYTE      T4T3M
*SFR* 000000DDH.5 DATA     BIT       T4x12
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 24


      00000026H   EDATA    FLOAT     t_0
      0000002CH   EDATA    FLOAT     t_1
      000001E5H   EDATA    LONG      target_pos
*SFR* 00000088H   DATA     BYTE      TCON
*SFR* 0000008CH   DATA     BYTE      TH0
*SFR* 0000008DH   DATA     BYTE      TH1
*SFR* 00000098H.1 DATA     BIT       TI
      00FF707FH   CODE     ---       TIMER0_I
      00FF0048H   CODE     ---       TIMER1_I
      00FF0049H   CODE     ---       TIMER2_I
      00FF004EH   CODE     ---       TIMER3_I
      00FF0050H   CODE     ---       TIMER4_I
*SFR* 0000008AH   DATA     BYTE      TL0
*SFR* 0000008BH   DATA     BYTE      TL1
      *EXTERN*    CODE     ---       TM0_isr
*SFR* 00000089H   DATA     BYTE      TMOD
*SFR* 00000088H.4 DATA     BIT       TR0
*SFR* 00000088H.6 DATA     BIT       TR1
      000002DBH   EDATA    BYTE      TSG1
      000002DCH   EDATA    BYTE      TSG2
      00FF7B76H   CODE     ---       UART1_I
      000002FCH   EDATA    BYTE      UART1_OK
      00FF77E6H   CODE     ---       UART2_I
      000002FDH   EDATA    BYTE      UART2_OK
      00FF7802H   CODE     ---       UART3_I
      000002FEH   EDATA    BYTE      UART3_OK
      00FF781EH   CODE     ---       UART4_I
      000002FFH   EDATA    BYTE      UART4_OK
      00FF44CAH   CODE     ---       UART_init
      00FF690CH   CODE     ---       UART_Send_byte
      00FF1793H   CODE     ---       UART_Send_float
      00FF1FB0H   CODE     ---       UART_Send_int
      00FF7754H   CODE     ---       UART_Send_string
      00FF00AEH   CODE     ---       usb_bulk_intr_in
      00FF7A18H   CODE     ---       usb_bulk_intr_out
      00FF57F4H   CODE     ---       usb_clear_feature
      00FF657CH   CODE     ---       usb_ctrl_in
      00FF69FCH   CODE     ---       usb_ctrl_out
      00000020H.7 BIT      BIT       USB_flag
      00FF6811H   CODE     ---       usb_get_configuration
      00FF5893H   CODE     ---       usb_get_descriptor
      00FF6EE2H   CODE     ---       usb_get_interface
      00FF74AAH   CODE     ---       usb_get_line_coding
      00FF4E67H   CODE     ---       usb_get_status
      00FF724EH   CODE     ---       usb_IN
      00FF72ABH   CODE     ---       usb_in_ep1
      00FF73B5H   CODE     ---       usb_in_ep2
      00FF66AAH   CODE     ---       usb_init
      00FF5F9CH   CODE     ---       usb_isr
      00FF7B8EH   CODE     ---       usb_OUT_done
      00FF53AEH   CODE     ---       usb_out_ep1
      00FF6F98H   CODE     ---       usb_read_fifo?_
      00FF0166H   CODE     ---       usb_read_reg
      00FF78C5H   CODE     ---       usb_req_class
      00FF6708H   CODE     ---       usb_req_std
      00FF002EH   CODE     ---       usb_req_vendor
      00FF6BC0H   CODE     ---       usb_reset
      00FF0056H   CODE     ---       usb_resume
      00FF7E31H   CODE     ---       Usb_Rst
      00FF6284H   CODE     ---       USB_SendData
      00FF6B2DH   CODE     ---       usb_set_address
      00FF56AFH   CODE     ---       usb_set_configuration
      00FF7C8EH   CODE     ---       usb_set_ctrl_line_state
      00FF001EH   CODE     ---       usb_set_descriptor
      00FF59CEH   CODE     ---       usb_set_feature
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 25


      00FF745BH   CODE     ---       usb_set_interface
      00FF74D0H   CODE     ---       usb_set_line_coding
      00FF5F12H   CODE     ---       usb_setup
      00FF7D44H   CODE     ---       usb_setup_in
      00FF7DD4H   CODE     ---       usb_setup_out
      00FF7DE2H   CODE     ---       usb_setup_stall
      00FF0036H   CODE     ---       usb_setup_status
      00FF0052H   CODE     ---       usb_suspend
      00FF0026H   CODE     ---       usb_synch_frame
      00FF73E0H   CODE     ---       usb_write_fifo?_
      00FF7D0BH   CODE     ---       usb_write_reg
*SFR* 000000FCH   DATA     BYTE      USBADR
*SFR* 000000DCH   DATA     BYTE      USBCLK
*SFR* 000000F4H   DATA     BYTE      USBCON
*SFR* 000000ECH   DATA     BYTE      USBDAT
      00010080H   XDATA    ---       UsbFeatureBuffer
      00010000H   XDATA    ---       UsbInBuffer
      00010040H   XDATA    ---       UsbOutBuffer
      0000020CH   EDATA    ---       USER_DEVICEDESC
      00000208H   EDATA    ---       USER_PRODUCTDESC
      00000202H   EDATA    ---       USER_STCISPCMD
*SFR* 000000A6H   DATA     BYTE      VRTRIM
      00FF049BH   CODE     NEAR LAB  vsprintf
*SFR* 000000C1H   DATA     BYTE      WDT_CONTR
*SFR* 000000E9H   DATA     BYTE      WTST
      00FF6DE2H   CODE     ---       Yijielvbo



UNRESOLVED EXTERNAL SYMBOLS:
   INT2_isr
   TM0_isr



SYMBOL TABLE OF MODULE:  .\Objects\mode (main)

      VALUE       REP       CLASS    TYPE      SYMBOL NAME
      ====================================================
      ---         MODULE    ---      ---       main
      00FF5932H   PUBLIC    CODE     ---       main
      000001D5H   PUBLIC    EDATA    INT       gripper_state
      000001D7H   PUBLIC    EDATA    LONG      collision_pos
      000001DBH   PUBLIC    EDATA    LONG      motor_pos_old
      000001DFH   PUBLIC    EDATA    LONG      pwm_out
      000001E3H   PUBLIC    EDATA    INT       collision_counter
      000001E5H   PUBLIC    EDATA    LONG      target_pos
      000001E9H   PUBLIC    EDATA    INT       motor_spd
      000001EBH   PUBLIC    EDATA    LONG      motor_pos
      000001EFH   PUBLIC    EDATA    ---       err_spd
      000001F5H   PUBLIC    EDATA    INT       set_spd
      00000020H.0 PUBLIC    BIT      BIT       gripper_mode
      00000020H.1 PUBLIC    BIT      BIT       key1_old
      00000020H.2 PUBLIC    BIT      BIT       key1_flag
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 26


      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      00000090H.1 SFRSYM    DATA     BIT       P11
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000AFH.7 SFRSYM    DATA     BIT       EUSB
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B0H.2 SFRSYM    DATA     BIT       KEY1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF5932H   BLOCK     CODE     ---       LVL=0
      00FF5932H   LINE      CODE     ---       #59
      00FF5932H   LINE      CODE     ---       #61
      00FF5935H   LINE      CODE     ---       #62
      00FF593DH   LINE      CODE     ---       #64
      00FF5945H   LINE      CODE     ---       #66
      00FF5952H   LINE      CODE     ---       #67
      00FF5954H   LINE      CODE     ---       #69
      00FF5957H   LINE      CODE     ---       #70
      00FF5959H   LINE      CODE     ---       #72
      00FF5959H   LINE      CODE     ---       #75
      00FF5961H   LINE      CODE     ---       #76
      00FF5965H   LINE      CODE     ---       #77
      00FF596BH   LINE      CODE     ---       #78
      00FF596DH   LINE      CODE     ---       #79
      00FF596DH   LINE      CODE     ---       #83
      00FF5970H   LINE      CODE     ---       #84
      00FF5972H   LINE      CODE     ---       #85
      00FF597BH   LINE      CODE     ---       #87
      00FF597EH   LINE      CODE     ---       #88
      00FF5986H   LINE      CODE     ---       #89
      00FF598EH   LINE      CODE     ---       #90
      00FF5990H   LINE      CODE     ---       #91
      00FF5998H   LINE      CODE     ---       #92
      00FF59A4H   LINE      CODE     ---       #93
      00FF59A4H   LINE      CODE     ---       #94
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 27


      00FF59A4H   LINE      CODE     ---       #96
      00FF59A7H   LINE      CODE     ---       #98
      00FF59CAH   LINE      CODE     ---       #99
      00FF59CCH   LINE      CODE     ---       #100
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       isr
      00FF0012H   PUBLIC    CODE     ---       IIC_I
      00FF0019H   PUBLIC    CODE     ---       CMP_I
      00FF001AH   PUBLIC    CODE     ---       LVD_I
      00FF0021H   PUBLIC    CODE     ---       SPI_I
      00FF7B76H   PUBLIC    CODE     ---       UART1_I
      00FF77E6H   PUBLIC    CODE     ---       UART2_I
      00FF7802H   PUBLIC    CODE     ---       UART3_I
      00FF781EH   PUBLIC    CODE     ---       UART4_I
      00FF6CDAH   PUBLIC    CODE     ---       DMA_UART1_RXD_isr
      00FF6D1CH   PUBLIC    CODE     ---       DMA_UART2_RXD_isr
      00FF6D5EH   PUBLIC    CODE     ---       DMA_UART3_RXD_isr
      00FF0066H   PUBLIC    CODE     ---       DMA_UART1_TXD_isr
      00FF6DA0H   PUBLIC    CODE     ---       DMA_UART4_RXD_isr
      00FF7772H   PUBLIC    CODE     ---       DMA_UART2_TXD_isr
      00FF778FH   PUBLIC    CODE     ---       DMA_UART3_TXD_isr
      00FF77ACH   PUBLIC    CODE     ---       DMA_UART4_TXD_isr
      00FF0022H   PUBLIC    CODE     ---       DMA_ADC_isr
      00FF643BH   PUBLIC    CODE     ---       CAN1_I
      00FF64A6H   PUBLIC    CODE     ---       CAN2_I
      00FF0029H   PUBLIC    CODE     ---       DMA_M2M_isr
      00FF7646H   PUBLIC    CODE     ---       PIN0_I
      00FF7664H   PUBLIC    CODE     ---       PIN1_I
      00FF7682H   PUBLIC    CODE     ---       PIN2_I
      00FF76A0H   PUBLIC    CODE     ---       PIN3_I
      00FF76BEH   PUBLIC    CODE     ---       PIN4_I
      00FF002AH   PUBLIC    CODE     ---       INT0_I
      00FF76DCH   PUBLIC    CODE     ---       PIN5_I
      00FF0031H   PUBLIC    CODE     ---       INT1_I
      00FF76FAH   PUBLIC    CODE     ---       PIN6_I
      00FF721EH   PUBLIC    CODE     ---       INT2_I
      00FF7718H   PUBLIC    CODE     ---       PIN7_I
      00FF0032H   PUBLIC    CODE     ---       INT3_I
      00FF0046H   PUBLIC    CODE     ---       INT4_I
      00FF0047H   PUBLIC    CODE     ---       DMA_SPI_isr
      00FF707FH   PUBLIC    CODE     ---       TIMER0_I
      00FF0048H   PUBLIC    CODE     ---       TIMER1_I
      00FF0049H   PUBLIC    CODE     ---       TIMER2_I
      00FF004AH   PUBLIC    CODE     ---       PWMA_I
      00FF004EH   PUBLIC    CODE     ---       TIMER3_I
      00FF004FH   PUBLIC    CODE     ---       PWMB_I
      00FF0050H   PUBLIC    CODE     ---       TIMER4_I
      00FF0051H   PUBLIC    CODE     ---       ADC_I
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 28


      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000097H.3 SFRSYM    DATA     BIT       CANSEL
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000FDH.1 SFRSYM    DATA     BIT       S4TI
      000000ACH.1 SFRSYM    DATA     BIT       S3TI
      0000009AH.1 SFRSYM    DATA     BIT       S2TI
      000000FDH.0 SFRSYM    DATA     BIT       S4RI
      000000ACH.0 SFRSYM    DATA     BIT       S3RI
      0000009AH.0 SFRSYM    DATA     BIT       S2RI
      00000098H.1 SFRSYM    DATA     BIT       TI
      00000098H.0 SFRSYM    DATA     BIT       RI
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF002AH   BLOCK     CODE     ---       LVL=0
      00FF002AH   LINE      CODE     ---       #4
      00FF002AH   LINE      CODE     ---       #7
      ---         BLOCKEND  ---      ---       LVL=0

      00FF707FH   BLOCK     CODE     ---       LVL=0
      00FF707FH   LINE      CODE     ---       #8
      00FF7095H   LINE      CODE     ---       #10
      00FF7098H   LINE      CODE     ---       #11
      00FF709BH   LINE      CODE     ---       #12
      00FF709EH   LINE      CODE     ---       #13
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0031H   BLOCK     CODE     ---       LVL=0
      00FF0031H   LINE      CODE     ---       #14
      00FF0031H   LINE      CODE     ---       #17
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0048H   BLOCK     CODE     ---       LVL=0
      00FF0048H   LINE      CODE     ---       #18
      00FF0048H   LINE      CODE     ---       #21
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7B76H   BLOCK     CODE     ---       LVL=0
      00FF7B76H   LINE      CODE     ---       #22
      00FF7B7AH   LINE      CODE     ---       #24
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 29


      00FF7B7DH   LINE      CODE     ---       #26
      00FF7B7FH   LINE      CODE     ---       #27
      00FF7B84H   LINE      CODE     ---       #28
      00FF7B84H   LINE      CODE     ---       #29
      00FF7B87H   LINE      CODE     ---       #32
      00FF7B89H   LINE      CODE     ---       #33
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0051H   BLOCK     CODE     ---       LVL=0
      00FF0051H   LINE      CODE     ---       #36
      00FF0051H   LINE      CODE     ---       #39
      ---         BLOCKEND  ---      ---       LVL=0

      00FF001AH   BLOCK     CODE     ---       LVL=0
      00FF001AH   LINE      CODE     ---       #40
      00FF001AH   LINE      CODE     ---       #43
      ---         BLOCKEND  ---      ---       LVL=0

      00FF77E6H   BLOCK     CODE     ---       LVL=0
      00FF77E6H   LINE      CODE     ---       #44
      00FF77EAH   LINE      CODE     ---       #46
      00FF77EEH   LINE      CODE     ---       #48
      00FF77F1H   LINE      CODE     ---       #49
      00FF77F6H   LINE      CODE     ---       #50
      00FF77F6H   LINE      CODE     ---       #51
      00FF77FAH   LINE      CODE     ---       #54
      00FF77FDH   LINE      CODE     ---       #55
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0021H   BLOCK     CODE     ---       LVL=0
      00FF0021H   LINE      CODE     ---       #57
      00FF0021H   LINE      CODE     ---       #60
      ---         BLOCKEND  ---      ---       LVL=0

      00FF721EH   BLOCK     CODE     ---       LVL=0
      00FF721EH   LINE      CODE     ---       #61
      00FF7234H   LINE      CODE     ---       #63
      00FF7237H   LINE      CODE     ---       #64
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0032H   BLOCK     CODE     ---       LVL=0
      00FF0032H   LINE      CODE     ---       #65
      00FF0032H   LINE      CODE     ---       #68
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0049H   BLOCK     CODE     ---       LVL=0
      00FF0049H   LINE      CODE     ---       #69
      00FF0049H   LINE      CODE     ---       #72
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0046H   BLOCK     CODE     ---       LVL=0
      00FF0046H   LINE      CODE     ---       #73
      00FF0046H   LINE      CODE     ---       #76
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7802H   BLOCK     CODE     ---       LVL=0
      00FF7802H   LINE      CODE     ---       #77
      00FF7806H   LINE      CODE     ---       #79
      00FF780AH   LINE      CODE     ---       #81
      00FF780DH   LINE      CODE     ---       #82
      00FF7812H   LINE      CODE     ---       #83
      00FF7812H   LINE      CODE     ---       #84
      00FF7816H   LINE      CODE     ---       #87
      00FF7819H   LINE      CODE     ---       #88
      ---         BLOCKEND  ---      ---       LVL=0
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 30



      00FF781EH   BLOCK     CODE     ---       LVL=0
      00FF781EH   LINE      CODE     ---       #90
      00FF7822H   LINE      CODE     ---       #92
      00FF7826H   LINE      CODE     ---       #94
      00FF7829H   LINE      CODE     ---       #95
      00FF782EH   LINE      CODE     ---       #96
      00FF782EH   LINE      CODE     ---       #97
      00FF7832H   LINE      CODE     ---       #100
      00FF7835H   LINE      CODE     ---       #101
      ---         BLOCKEND  ---      ---       LVL=0

      00FF004EH   BLOCK     CODE     ---       LVL=0
      00FF004EH   LINE      CODE     ---       #103
      00FF004EH   LINE      CODE     ---       #106
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0050H   BLOCK     CODE     ---       LVL=0
      00FF0050H   LINE      CODE     ---       #107
      00FF0050H   LINE      CODE     ---       #110
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0019H   BLOCK     CODE     ---       LVL=0
      00FF0019H   LINE      CODE     ---       #111
      00FF0019H   LINE      CODE     ---       #114
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0012H   BLOCK     CODE     ---       LVL=0
      00FF0012H   LINE      CODE     ---       #115
      00FF0012H   LINE      CODE     ---       #118
      ---         BLOCKEND  ---      ---       LVL=0

      00FF004AH   BLOCK     CODE     ---       LVL=0
      00FF004AH   LINE      CODE     ---       #123
      00FF004AH   LINE      CODE     ---       #126
      ---         BLOCKEND  ---      ---       LVL=0

      00FF004FH   BLOCK     CODE     ---       LVL=0
      00FF004FH   LINE      CODE     ---       #127
      00FF004FH   LINE      CODE     ---       #130
      ---         BLOCKEND  ---      ---       LVL=0

      00FF643BH   BLOCK     CODE     ---       LVL=0
      00FF6451H   BLOCK     CODE     NEAR LAB  LVL=1
      R10         REGSYM    ---      BYTE      isr
      ---         BLOCKEND  ---      ---       LVL=1
      00FF643BH   LINE      CODE     ---       #132
      00FF6451H   LINE      CODE     ---       #133
      00FF6451H   LINE      CODE     ---       #136
      00FF6454H   LINE      CODE     ---       #137
      00FF645BH   LINE      CODE     ---       #138
      00FF6468H   LINE      CODE     ---       #139
      00FF6473H   LINE      CODE     ---       #141
      00FF647AH   LINE      CODE     ---       #143
      00FF647CH   LINE      CODE     ---       #144
      00FF647CH   LINE      CODE     ---       #145
      00FF647CH   LINE      CODE     ---       #148
      00FF647CH   LINE      CODE     ---       #149
      00FF647CH   LINE      CODE     ---       #152
      00FF647CH   LINE      CODE     ---       #153
      00FF6483H   LINE      CODE     ---       #155
      00FF6487H   LINE      CODE     ---       #156
      00FF648FH   LINE      CODE     ---       #157
      00FF648FH   LINE      CODE     ---       #158
      00FF648FH   LINE      CODE     ---       #161
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 31


      00FF648FH   LINE      CODE     ---       #162
      00FF648FH   LINE      CODE     ---       #165
      00FF648FH   LINE      CODE     ---       #166
      00FF648FH   LINE      CODE     ---       #169
      ---         BLOCKEND  ---      ---       LVL=0

      00FF64A6H   BLOCK     CODE     ---       LVL=0
      00FF64BCH   BLOCK     CODE     NEAR LAB  LVL=1
      R10         REGSYM    ---      BYTE      isr
      ---         BLOCKEND  ---      ---       LVL=1
      00FF64A6H   LINE      CODE     ---       #172
      00FF64BCH   LINE      CODE     ---       #173
      00FF64BCH   LINE      CODE     ---       #176
      00FF64BFH   LINE      CODE     ---       #177
      00FF64C6H   LINE      CODE     ---       #178
      00FF64D3H   LINE      CODE     ---       #179
      00FF64DEH   LINE      CODE     ---       #181
      00FF64E5H   LINE      CODE     ---       #183
      00FF64E7H   LINE      CODE     ---       #184
      00FF64E7H   LINE      CODE     ---       #185
      00FF64E7H   LINE      CODE     ---       #188
      00FF64E7H   LINE      CODE     ---       #189
      00FF64E7H   LINE      CODE     ---       #192
      00FF64E7H   LINE      CODE     ---       #193
      00FF64EEH   LINE      CODE     ---       #195
      00FF64F2H   LINE      CODE     ---       #196
      00FF64FAH   LINE      CODE     ---       #197
      00FF64FAH   LINE      CODE     ---       #198
      00FF64FAH   LINE      CODE     ---       #201
      00FF64FAH   LINE      CODE     ---       #202
      00FF64FAH   LINE      CODE     ---       #205
      00FF64FAH   LINE      CODE     ---       #206
      00FF64FAH   LINE      CODE     ---       #209
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7646H   BLOCK     CODE     ---       LVL=0
      00FF764CH   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      intf
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7646H   LINE      CODE     ---       #212
      00FF764CH   LINE      CODE     ---       #213
      00FF764CH   LINE      CODE     ---       #215
      00FF7657H   LINE      CODE     ---       #216
      00FF7659H   LINE      CODE     ---       #218
      00FF765DH   LINE      CODE     ---       #219
      00FF765DH   LINE      CODE     ---       #222
      00FF765DH   LINE      CODE     ---       #223
      00FF765DH   LINE      CODE     ---       #226
      00FF765DH   LINE      CODE     ---       #227
      00FF765DH   LINE      CODE     ---       #230
      00FF765DH   LINE      CODE     ---       #231
      00FF765DH   LINE      CODE     ---       #234
      00FF765DH   LINE      CODE     ---       #235
      00FF765DH   LINE      CODE     ---       #238
      00FF765DH   LINE      CODE     ---       #239
      00FF765DH   LINE      CODE     ---       #242
      00FF765DH   LINE      CODE     ---       #243
      00FF765DH   LINE      CODE     ---       #246
      00FF765DH   LINE      CODE     ---       #247
      00FF765DH   LINE      CODE     ---       #250
      00FF765DH   LINE      CODE     ---       #252
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7664H   BLOCK     CODE     ---       LVL=0
      00FF766AH   BLOCK     CODE     NEAR LAB  LVL=1
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 32


      R11         REGSYM    ---      BYTE      intf
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7664H   LINE      CODE     ---       #253
      00FF766AH   LINE      CODE     ---       #254
      00FF766AH   LINE      CODE     ---       #256
      00FF7675H   LINE      CODE     ---       #257
      00FF7677H   LINE      CODE     ---       #259
      00FF767BH   LINE      CODE     ---       #260
      00FF767BH   LINE      CODE     ---       #263
      00FF767BH   LINE      CODE     ---       #264
      00FF767BH   LINE      CODE     ---       #267
      00FF767BH   LINE      CODE     ---       #268
      00FF767BH   LINE      CODE     ---       #271
      00FF767BH   LINE      CODE     ---       #272
      00FF767BH   LINE      CODE     ---       #275
      00FF767BH   LINE      CODE     ---       #276
      00FF767BH   LINE      CODE     ---       #279
      00FF767BH   LINE      CODE     ---       #280
      00FF767BH   LINE      CODE     ---       #283
      00FF767BH   LINE      CODE     ---       #284
      00FF767BH   LINE      CODE     ---       #287
      00FF767BH   LINE      CODE     ---       #288
      00FF767BH   LINE      CODE     ---       #291
      00FF767BH   LINE      CODE     ---       #293
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7682H   BLOCK     CODE     ---       LVL=0
      00FF7688H   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      intf
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7682H   LINE      CODE     ---       #294
      00FF7688H   LINE      CODE     ---       #295
      00FF7688H   LINE      CODE     ---       #297
      00FF7693H   LINE      CODE     ---       #298
      00FF7695H   LINE      CODE     ---       #300
      00FF7699H   LINE      CODE     ---       #301
      00FF7699H   LINE      CODE     ---       #304
      00FF7699H   LINE      CODE     ---       #305
      00FF7699H   LINE      CODE     ---       #308
      00FF7699H   LINE      CODE     ---       #309
      00FF7699H   LINE      CODE     ---       #312
      00FF7699H   LINE      CODE     ---       #313
      00FF7699H   LINE      CODE     ---       #316
      00FF7699H   LINE      CODE     ---       #317
      00FF7699H   LINE      CODE     ---       #320
      00FF7699H   LINE      CODE     ---       #321
      00FF7699H   LINE      CODE     ---       #324
      00FF7699H   LINE      CODE     ---       #325
      00FF7699H   LINE      CODE     ---       #328
      00FF7699H   LINE      CODE     ---       #329
      00FF7699H   LINE      CODE     ---       #332
      00FF7699H   LINE      CODE     ---       #334
      ---         BLOCKEND  ---      ---       LVL=0

      00FF76A0H   BLOCK     CODE     ---       LVL=0
      00FF76A6H   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      intf
      ---         BLOCKEND  ---      ---       LVL=1
      00FF76A0H   LINE      CODE     ---       #335
      00FF76A6H   LINE      CODE     ---       #336
      00FF76A6H   LINE      CODE     ---       #338
      00FF76B1H   LINE      CODE     ---       #339
      00FF76B3H   LINE      CODE     ---       #341
      00FF76B7H   LINE      CODE     ---       #342
      00FF76B7H   LINE      CODE     ---       #345
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 33


      00FF76B7H   LINE      CODE     ---       #346
      00FF76B7H   LINE      CODE     ---       #349
      00FF76B7H   LINE      CODE     ---       #350
      00FF76B7H   LINE      CODE     ---       #353
      00FF76B7H   LINE      CODE     ---       #354
      00FF76B7H   LINE      CODE     ---       #357
      00FF76B7H   LINE      CODE     ---       #358
      00FF76B7H   LINE      CODE     ---       #361
      00FF76B7H   LINE      CODE     ---       #362
      00FF76B7H   LINE      CODE     ---       #365
      00FF76B7H   LINE      CODE     ---       #366
      00FF76B7H   LINE      CODE     ---       #369
      00FF76B7H   LINE      CODE     ---       #370
      00FF76B7H   LINE      CODE     ---       #373
      00FF76B7H   LINE      CODE     ---       #375
      ---         BLOCKEND  ---      ---       LVL=0

      00FF76BEH   BLOCK     CODE     ---       LVL=0
      00FF76C4H   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      intf
      ---         BLOCKEND  ---      ---       LVL=1
      00FF76BEH   LINE      CODE     ---       #376
      00FF76C4H   LINE      CODE     ---       #377
      00FF76C4H   LINE      CODE     ---       #379
      00FF76CFH   LINE      CODE     ---       #380
      00FF76D1H   LINE      CODE     ---       #382
      00FF76D5H   LINE      CODE     ---       #383
      00FF76D5H   LINE      CODE     ---       #386
      00FF76D5H   LINE      CODE     ---       #387
      00FF76D5H   LINE      CODE     ---       #390
      00FF76D5H   LINE      CODE     ---       #391
      00FF76D5H   LINE      CODE     ---       #394
      00FF76D5H   LINE      CODE     ---       #395
      00FF76D5H   LINE      CODE     ---       #398
      00FF76D5H   LINE      CODE     ---       #399
      00FF76D5H   LINE      CODE     ---       #402
      00FF76D5H   LINE      CODE     ---       #403
      00FF76D5H   LINE      CODE     ---       #406
      00FF76D5H   LINE      CODE     ---       #407
      00FF76D5H   LINE      CODE     ---       #410
      00FF76D5H   LINE      CODE     ---       #411
      00FF76D5H   LINE      CODE     ---       #414
      00FF76D5H   LINE      CODE     ---       #416
      ---         BLOCKEND  ---      ---       LVL=0

      00FF76DCH   BLOCK     CODE     ---       LVL=0
      00FF76E2H   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      intf
      ---         BLOCKEND  ---      ---       LVL=1
      00FF76DCH   LINE      CODE     ---       #417
      00FF76E2H   LINE      CODE     ---       #418
      00FF76E2H   LINE      CODE     ---       #420
      00FF76EDH   LINE      CODE     ---       #421
      00FF76EFH   LINE      CODE     ---       #423
      00FF76F3H   LINE      CODE     ---       #424
      00FF76F3H   LINE      CODE     ---       #427
      00FF76F3H   LINE      CODE     ---       #428
      00FF76F3H   LINE      CODE     ---       #431
      00FF76F3H   LINE      CODE     ---       #432
      00FF76F3H   LINE      CODE     ---       #435
      00FF76F3H   LINE      CODE     ---       #436
      00FF76F3H   LINE      CODE     ---       #439
      00FF76F3H   LINE      CODE     ---       #440
      00FF76F3H   LINE      CODE     ---       #443
      00FF76F3H   LINE      CODE     ---       #444
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 34


      00FF76F3H   LINE      CODE     ---       #447
      00FF76F3H   LINE      CODE     ---       #449
      ---         BLOCKEND  ---      ---       LVL=0

      00FF76FAH   BLOCK     CODE     ---       LVL=0
      00FF7700H   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      intf
      ---         BLOCKEND  ---      ---       LVL=1
      00FF76FAH   LINE      CODE     ---       #450
      00FF7700H   LINE      CODE     ---       #451
      00FF7700H   LINE      CODE     ---       #453
      00FF770BH   LINE      CODE     ---       #454
      00FF770DH   LINE      CODE     ---       #456
      00FF7711H   LINE      CODE     ---       #457
      00FF7711H   LINE      CODE     ---       #460
      00FF7711H   LINE      CODE     ---       #461
      00FF7711H   LINE      CODE     ---       #464
      00FF7711H   LINE      CODE     ---       #465
      00FF7711H   LINE      CODE     ---       #468
      00FF7711H   LINE      CODE     ---       #469
      00FF7711H   LINE      CODE     ---       #472
      00FF7711H   LINE      CODE     ---       #473
      00FF7711H   LINE      CODE     ---       #476
      00FF7711H   LINE      CODE     ---       #477
      00FF7711H   LINE      CODE     ---       #480
      00FF7711H   LINE      CODE     ---       #481
      00FF7711H   LINE      CODE     ---       #484
      00FF7711H   LINE      CODE     ---       #485
      00FF7711H   LINE      CODE     ---       #488
      00FF7711H   LINE      CODE     ---       #490
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7718H   BLOCK     CODE     ---       LVL=0
      00FF771EH   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      intf
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7718H   LINE      CODE     ---       #491
      00FF771EH   LINE      CODE     ---       #492
      00FF771EH   LINE      CODE     ---       #494
      00FF7729H   LINE      CODE     ---       #495
      00FF772BH   LINE      CODE     ---       #497
      00FF772FH   LINE      CODE     ---       #498
      00FF772FH   LINE      CODE     ---       #501
      00FF772FH   LINE      CODE     ---       #502
      00FF772FH   LINE      CODE     ---       #505
      00FF772FH   LINE      CODE     ---       #506
      00FF772FH   LINE      CODE     ---       #509
      00FF772FH   LINE      CODE     ---       #510
      00FF772FH   LINE      CODE     ---       #513
      00FF772FH   LINE      CODE     ---       #514
      00FF772FH   LINE      CODE     ---       #517
      00FF772FH   LINE      CODE     ---       #518
      00FF772FH   LINE      CODE     ---       #521
      00FF772FH   LINE      CODE     ---       #522
      00FF772FH   LINE      CODE     ---       #525
      00FF772FH   LINE      CODE     ---       #526
      00FF772FH   LINE      CODE     ---       #529
      00FF772FH   LINE      CODE     ---       #531
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0029H   BLOCK     CODE     ---       LVL=0
      00FF0029H   LINE      CODE     ---       #535
      00FF0029H   LINE      CODE     ---       #538
      ---         BLOCKEND  ---      ---       LVL=0

L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 35


      00FF0022H   BLOCK     CODE     ---       LVL=0
      00FF0022H   LINE      CODE     ---       #540
      00FF0022H   LINE      CODE     ---       #543
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0047H   BLOCK     CODE     ---       LVL=0
      00FF0047H   LINE      CODE     ---       #545
      00FF0047H   LINE      CODE     ---       #548
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0066H   BLOCK     CODE     ---       LVL=0
      00FF0066H   LINE      CODE     ---       #550
      00FF006CH   LINE      CODE     ---       #552
      00FF007CH   LINE      CODE     ---       #553
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6CDAH   BLOCK     CODE     ---       LVL=0
      00FF6CDAH   LINE      CODE     ---       #554
      00FF6CE2H   LINE      CODE     ---       #556
      00FF6CF0H   LINE      CODE     ---       #558
      00FF6CF8H   LINE      CODE     ---       #560
      00FF6D05H   LINE      CODE     ---       #561
      00FF6D05H   LINE      CODE     ---       #562
      00FF6D0BH   LINE      CODE     ---       #564
      00FF6D13H   LINE      CODE     ---       #565
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7772H   BLOCK     CODE     ---       LVL=0
      00FF7772H   LINE      CODE     ---       #568
      00FF7778H   LINE      CODE     ---       #570
      00FF7788H   LINE      CODE     ---       #571
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6D1CH   BLOCK     CODE     ---       LVL=0
      00FF6D1CH   LINE      CODE     ---       #572
      00FF6D24H   LINE      CODE     ---       #574
      00FF6D32H   LINE      CODE     ---       #576
      00FF6D3AH   LINE      CODE     ---       #578
      00FF6D47H   LINE      CODE     ---       #579
      00FF6D47H   LINE      CODE     ---       #580
      00FF6D4DH   LINE      CODE     ---       #582
      00FF6D55H   LINE      CODE     ---       #583
      ---         BLOCKEND  ---      ---       LVL=0

      00FF778FH   BLOCK     CODE     ---       LVL=0
      00FF778FH   LINE      CODE     ---       #585
      00FF7795H   LINE      CODE     ---       #587
      00FF77A5H   LINE      CODE     ---       #588
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6D5EH   BLOCK     CODE     ---       LVL=0
      00FF6D5EH   LINE      CODE     ---       #589
      00FF6D66H   LINE      CODE     ---       #591
      00FF6D74H   LINE      CODE     ---       #593
      00FF6D7CH   LINE      CODE     ---       #595
      00FF6D89H   LINE      CODE     ---       #596
      00FF6D89H   LINE      CODE     ---       #597
      00FF6D8FH   LINE      CODE     ---       #599
      00FF6D97H   LINE      CODE     ---       #600
      ---         BLOCKEND  ---      ---       LVL=0

      00FF77ACH   BLOCK     CODE     ---       LVL=0
      00FF77ACH   LINE      CODE     ---       #602
      00FF77B2H   LINE      CODE     ---       #604
      00FF77C2H   LINE      CODE     ---       #605
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 36


      ---         BLOCKEND  ---      ---       LVL=0

      00FF6DA0H   BLOCK     CODE     ---       LVL=0
      00FF6DA0H   LINE      CODE     ---       #606
      00FF6DA8H   LINE      CODE     ---       #608
      00FF6DB6H   LINE      CODE     ---       #610
      00FF6DBEH   LINE      CODE     ---       #612
      00FF6DCBH   LINE      CODE     ---       #613
      00FF6DCBH   LINE      CODE     ---       #614
      00FF6DD1H   LINE      CODE     ---       #616
      00FF6DD9H   LINE      CODE     ---       #617
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       config
      00FF0016H   PUBLIC    CODE     ---       HID_isr
      00FF6214H   PUBLIC    CODE     ---       System_init
      00FF7DB6H   PUBLIC    CODE     ---       fmax
      00FF7BD6H   PUBLIC    CODE     ---       Limit_int
      00FF6EA3H   PUBLIC    CODE     ---       Key_Rst
      00FF7E31H   PUBLIC    CODE     ---       Usb_Rst
      00FF7BEDH   PUBLIC    CODE     ---       HEX2BCD
      00FF78AAH   PUBLIC    CODE     ---       BCD2HEX
      00FF0006H   PUBLIC    CODE     ---       sq
      00FF4A26H   PUBLIC    CODE     ---       set_clk
      00FF7582H   PUBLIC    CODE     ---       Limit_float
      00000020H.6 PUBLIC    BIT      BIT       Key_Flag
      00000020H.7 PUBLIC    BIT      BIT       USB_flag
      000001F7H   PUBLIC    EDATA    ---       float_uchar
      000001FBH   PUBLIC    EDATA    ---       char_uchar
      000001FCH   PUBLIC    EDATA    ---       long_uchar
      00000200H   PUBLIC    EDATA    ---       int_uchar
      00000202H   PUBLIC    EDATA    ---       USER_STCISPCMD
      00000206H   PUBLIC    EDATA    WORD      Key_cnt
      00000208H   PUBLIC    EDATA    ---       USER_PRODUCTDESC
      0000020CH   PUBLIC    EDATA    ---       USER_DEVICEDESC
      00000210H   PUBLIC    EDATA    DWORD     sys_clk
      000002D8H   PUBLIC    EDATA    BYTE      ?Limit_int?BYTE
      000002DCH   PUBLIC    EDATA    BYTE      ?Limit_float?BYTE
      00000094H   SFRSYM    DATA     BYTE      P0M0
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000BAH.7 SFRSYM    DATA     BIT       EAXFR
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EAH   SFRSYM    DATA     BYTE      CKCON
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000A6H   SFRSYM    DATA     BYTE      VRTRIM
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      0000009FH   SFRSYM    DATA     BYTE      IRTRIM
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000E9H   SFRSYM    DATA     BYTE      WTST
      000000B0H.2 SFRSYM    DATA     BIT       Reset_PIN
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000DCH   SFRSYM    DATA     BYTE      USBCLK
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 37


      000000B5H.7 SFRSYM    DATA     BIT       PUSB
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000AFH.7 SFRSYM    DATA     BIT       EUSB
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E1H   SFRSYM    DATA     BYTE      P7M1
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000CBH   SFRSYM    DATA     BYTE      P6M1
      000000E2H   SFRSYM    DATA     BYTE      P7M0
      000000B6H.7 SFRSYM    DATA     BIT       PUSBH
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000C9H   SFRSYM    DATA     BYTE      P5M1
      000000CCH   SFRSYM    DATA     BYTE      P6M0
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000B3H   SFRSYM    DATA     BYTE      P4M1
      000000CAH   SFRSYM    DATA     BYTE      P5M0
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B1H   SFRSYM    DATA     BYTE      P3M1
      000000B4H   SFRSYM    DATA     BYTE      P4M0
      00000090H   SFRSYM    DATA     BYTE      P1
      00000095H   SFRSYM    DATA     BYTE      P2M1
      000000B2H   SFRSYM    DATA     BYTE      P3M0
      00000080H   SFRSYM    DATA     BYTE      P0
      00000091H   SFRSYM    DATA     BYTE      P1M1
      00000096H   SFRSYM    DATA     BYTE      P2M0
      00000092H   SFRSYM    DATA     BYTE      P1M0
      00000093H   SFRSYM    DATA     BYTE      P0M1

      00FF6214H   BLOCK     CODE     ---       LVL=0
      00FF6214H   LINE      CODE     ---       #31
      00FF6214H   LINE      CODE     ---       #36
      00FF6217H   LINE      CODE     ---       #37
      00FF621AH   LINE      CODE     ---       #38
      00FF621DH   LINE      CODE     ---       #40
      00FF6223H   LINE      CODE     ---       #41
      00FF6229H   LINE      CODE     ---       #42
      00FF622FH   LINE      CODE     ---       #43
      00FF6235H   LINE      CODE     ---       #44
      00FF623BH   LINE      CODE     ---       #45
      00FF6241H   LINE      CODE     ---       #46
      00FF6247H   LINE      CODE     ---       #47
      00FF624DH   LINE      CODE     ---       #50
      00FF6250H   LINE      CODE     ---       #51
      00FF6253H   LINE      CODE     ---       #52
      00FF6256H   LINE      CODE     ---       #53
      00FF6263H   LINE      CODE     ---       #54
      00FF6271H   LINE      CODE     ---       #55
      00FF6274H   LINE      CODE     ---       #56
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 38


      00FF6277H   LINE      CODE     ---       #57
      00FF627AH   LINE      CODE     ---       #60
      00FF627DH   LINE      CODE     ---       #61
      00FF6280H   LINE      CODE     ---       #62
      00FF6283H   LINE      CODE     ---       #63
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0016H   BLOCK     CODE     ---       LVL=0
      00FF0016H   LINE      CODE     ---       #65
      00FF0016H   LINE      CODE     ---       #67
      00FF0018H   LINE      CODE     ---       #68
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7E31H   BLOCK     CODE     ---       LVL=0
      00FF7E31H   LINE      CODE     ---       #70
      00FF7E31H   LINE      CODE     ---       #72
      00FF7E34H   LINE      CODE     ---       #74
      00FF7E37H   LINE      CODE     ---       #76
      00FF7E3AH   LINE      CODE     ---       #77
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6EA3H   BLOCK     CODE     ---       LVL=0
      00FF6EA3H   LINE      CODE     ---       #80
      00FF6EA3H   LINE      CODE     ---       #82
      00FF6EA6H   LINE      CODE     ---       #84
      00FF6EA9H   LINE      CODE     ---       #86
      00FF6EB3H   LINE      CODE     ---       #87
      00FF6EB9H   LINE      CODE     ---       #89
      00FF6EBBH   LINE      CODE     ---       #91
      00FF6EBEH   LINE      CODE     ---       #92
      00FF6EC1H   LINE      CODE     ---       #93
      00FF6ECDH   LINE      CODE     ---       #95
      00FF6ED4H   LINE      CODE     ---       #96
      00FF6ED7H   LINE      CODE     ---       #97
      00FF6ED9H   LINE      CODE     ---       #103
      00FF6EDFH   LINE      CODE     ---       #104
      00FF6EE1H   LINE      CODE     ---       #105
      00FF6EE1H   LINE      CODE     ---       #106
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7DB6H   BLOCK     CODE     ---       LVL=0
      DR24        REGSYM    ---      FLOAT     a
      DR28        REGSYM    ---      FLOAT     b
      00FF7DB6H   LINE      CODE     ---       #109
      00FF7DBAH   LINE      CODE     ---       #110
      00FF7DC4H   LINE      CODE     ---       #111
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7BD6H   BLOCK     CODE     ---       LVL=0
      DR28        REGSYM    ---      LONG      min
      DR8         REGSYM    ---      LONG      num
      000002E0H   SYMBOL    EDATA    LONG      max
      00FF7BD6H   LINE      CODE     ---       #113
      00FF7BDAH   LINE      CODE     ---       #115
      00FF7BE4H   LINE      CODE     ---       #116
      00FF7BEAH   LINE      CODE     ---       #117
      00FF7BECH   LINE      CODE     ---       #118
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7582H   BLOCK     CODE     ---       LVL=0
      DR24        REGSYM    ---      FLOAT     min
      DR28        REGSYM    ---      FLOAT     num
      000002E4H   SYMBOL    EDATA    FLOAT     max
      00FF7582H   LINE      CODE     ---       #120
      00FF7586H   LINE      CODE     ---       #122
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 39


      00FF7595H   LINE      CODE     ---       #123
      00FF75A0H   LINE      CODE     ---       #124
      00FF75A2H   LINE      CODE     ---       #125
      ---         BLOCKEND  ---      ---       LVL=0

      00FF78AAH   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      val
      R11         REGSYM    ---      BYTE      L
      00FF78AAH   LINE      CODE     ---       #127
      00FF78AAH   LINE      CODE     ---       #128
      00FF78AAH   LINE      CODE     ---       #130
      00FF78C4H   LINE      CODE     ---       #131
      00FF78C4H   LINE      CODE     ---       #132
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7BEDH   BLOCK     CODE     ---       LVL=0
      R7          REGSYM    ---      BYTE      val
      R11         REGSYM    ---      BYTE      L
      00FF7BEDH   LINE      CODE     ---       #134
      00FF7BEDH   LINE      CODE     ---       #135
      00FF7BEDH   LINE      CODE     ---       #137
      00FF7C03H   LINE      CODE     ---       #138
      00FF7C03H   LINE      CODE     ---       #139
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0006H   BLOCK     CODE     ---       LVL=0
      DR4         REGSYM    ---      FLOAT     num
      00FF0006H   LINE      CODE     ---       #141
      00FF0006H   LINE      CODE     ---       #143
      ---         BLOCKEND  ---      ---       LVL=0

      00FF4A26H   BLOCK     CODE     ---       LVL=0
      00FF4A26H   LINE      CODE     ---       #146
      00FF4A26H   LINE      CODE     ---       #149
      00FF4A29H   LINE      CODE     ---       #151
      00FF4A39H   LINE      CODE     ---       #154
      00FF4A46H   LINE      CODE     ---       #155
      00FF4A4AH   LINE      CODE     ---       #156
      00FF4A4AH   LINE      CODE     ---       #157
      00FF4A4AH   LINE      CODE     ---       #158
      00FF4A4AH   LINE      CODE     ---       #159
      00FF4A4CH   LINE      CODE     ---       #160
      00FF4A58H   LINE      CODE     ---       #163
      00FF4A65H   LINE      CODE     ---       #164
      00FF4A69H   LINE      CODE     ---       #165
      00FF4A69H   LINE      CODE     ---       #166
      00FF4A69H   LINE      CODE     ---       #167
      00FF4A69H   LINE      CODE     ---       #168
      00FF4A6BH   LINE      CODE     ---       #169
      00FF4A77H   LINE      CODE     ---       #172
      00FF4A84H   LINE      CODE     ---       #173
      00FF4A88H   LINE      CODE     ---       #174
      00FF4A88H   LINE      CODE     ---       #175
      00FF4A88H   LINE      CODE     ---       #176
      00FF4A88H   LINE      CODE     ---       #177
      00FF4A8AH   LINE      CODE     ---       #178
      00FF4A96H   LINE      CODE     ---       #182
      00FF4AA3H   LINE      CODE     ---       #183
      00FF4AA7H   LINE      CODE     ---       #184
      00FF4AA7H   LINE      CODE     ---       #185
      00FF4AA7H   LINE      CODE     ---       #186
      00FF4AA7H   LINE      CODE     ---       #187
      00FF4AA9H   LINE      CODE     ---       #188
      00FF4AB5H   LINE      CODE     ---       #191
      00FF4AC2H   LINE      CODE     ---       #192
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 40


      00FF4ACFH   LINE      CODE     ---       #193
      00FF4AD8H   LINE      CODE     ---       #194
      00FF4ADBH   LINE      CODE     ---       #195
      00FF4ADBH   LINE      CODE     ---       #196
      00FF4ADDH   LINE      CODE     ---       #197
      00FF4AE9H   LINE      CODE     ---       #200
      00FF4AF6H   LINE      CODE     ---       #201
      00FF4B03H   LINE      CODE     ---       #202
      00FF4B0CH   LINE      CODE     ---       #203
      00FF4B0FH   LINE      CODE     ---       #204
      00FF4B13H   LINE      CODE     ---       #205
      00FF4B14H   LINE      CODE     ---       #208
      00FF4B18H   LINE      CODE     ---       #210
      00FF4B25H   LINE      CODE     ---       #211
      00FF4B32H   LINE      CODE     ---       #212
      00FF4B3BH   LINE      CODE     ---       #213
      00FF4B3EH   LINE      CODE     ---       #214
      00FF4B42H   LINE      CODE     ---       #215
      00FF4B42H   LINE      CODE     ---       #216
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       usb
      00FF7D44H   PUBLIC    CODE     ---       usb_setup_in
      00FF7DD4H   PUBLIC    CODE     ---       usb_setup_out
      00FF657CH   PUBLIC    CODE     ---       usb_ctrl_in
      00FF69FCH   PUBLIC    CODE     ---       usb_ctrl_out
      00FF7B8EH   PUBLIC    CODE     ---       usb_OUT_done
      00FF0052H   PUBLIC    CODE     ---       usb_suspend
      00FF00AEH   PUBLIC    CODE     ---       usb_bulk_intr_in
      00FF7A18H   PUBLIC    CODE     ---       usb_bulk_intr_out
      00FF0056H   PUBLIC    CODE     ---       usb_resume
      00FF724EH   PUBLIC    CODE     ---       usb_IN
      00FF6F98H   PUBLIC    CODE     ---       usb_read_fifo?_
      00FF6BC0H   PUBLIC    CODE     ---       usb_reset
      00FF6284H   PUBLIC    CODE     ---       USB_SendData
      00FF5F12H   PUBLIC    CODE     ---       usb_setup
      00FF73E0H   PUBLIC    CODE     ---       usb_write_fifo?_
      00FF66AAH   PUBLIC    CODE     ---       usb_init
      00FF7DE2H   PUBLIC    CODE     ---       usb_setup_stall
      00FF0036H   PUBLIC    CODE     ---       usb_setup_status
      00FF0166H   PUBLIC    CODE     ---       usb_read_reg
      00FF72ABH   PUBLIC    CODE     ---       usb_in_ep1
      00FF73B5H   PUBLIC    CODE     ---       usb_in_ep2
      00FF53AEH   PUBLIC    CODE     ---       usb_out_ep1
      00FF7D0BH   PUBLIC    CODE     ---       usb_write_reg
      00FF5F9CH   PUBLIC    CODE     ---       usb_isr
      0000023FH   PUBLIC    EDATA    ---       Setup
      00000247H   PUBLIC    EDATA    BYTE      OutNumber
      00000249H   PUBLIC    EDATA    BYTE      DeviceState
      0000024AH   PUBLIC    EDATA    ---       Ep0State
      00000251H   PUBLIC    EDATA    BYTE      InEpState
      00000252H   PUBLIC    EDATA    BYTE      OutEpState
      00000020H.3 PUBLIC    BIT      BIT       bUsbOutReady
      00000020H.4 PUBLIC    BIT      BIT       bUsbFeatureReady
      00000020H.5 PUBLIC    BIT      BIT       bUsbInBusy
      00010000H   PUBLIC    XDATA    ---       UsbInBuffer
      00010040H   PUBLIC    XDATA    ---       UsbOutBuffer
      00010080H   PUBLIC    XDATA    ---       UsbFeatureBuffer
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 41


      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000DCH   SFRSYM    DATA     BYTE      USBCLK
      000000ECH   SFRSYM    DATA     BYTE      USBDAT
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      000000FCH   SFRSYM    DATA     BYTE      USBADR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000AFH.7 SFRSYM    DATA     BIT       EUSB
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      ---         MODULE    ---      ---       util
      00FF7CE3H   PUBLIC    CODE     ---       LCD12864_DisplayClear
      00FF6FD2H   PUBLIC    CODE     ---       OLED12864_ScrollRight
      00FF7A31H   PUBLIC    CODE     ---       OLED12864_DisplayOff
      00FF7046H   PUBLIC    CODE     ---       OLED12864_ScrollLeft
      00FF75A3H   PUBLIC    CODE     ---       OLED12864_SetAddressMode
      00FF75C4H   PUBLIC    CODE     ---       OLED12864_SetContrast
      00FF7A63H   PUBLIC    CODE     ---       LCD12864_AutoWrapOff
      00FF6E23H   PUBLIC    CODE     ---       OLED12864_ShowPicture
      00FF78E0H   PUBLIC    CODE     ---       OLED12864_ScrollStart
      00FF7155H   PUBLIC    CODE     ---       SEG7_ShowFloat
      00FF6B77H   PUBLIC    CODE     ---       SEG7_ShowString
      00FF70EBH   PUBLIC    CODE     ---       SEG7_ShowCode
      00FF6C08H   PUBLIC    CODE     ---       LED40_SendData
      00FF78FAH   PUBLIC    CODE     ---       OLED12864_DisplayOn
      00FF75E5H   PUBLIC    CODE     ---       LCD12864_ReverseLine
      00FF7A7CH   PUBLIC    CODE     ---       OLED12864_ScrollStop
      00FF6C4FH   PUBLIC    CODE     ---       LED64_SendData
      00FF72D8H   PUBLIC    CODE     ---       printf_hid
      00FF00EEH   PUBLIC    CODE     ---       SEG7_ShowLong
      00FF7914H   PUBLIC    CODE     ---       LCD12864_ScrollRight
      00FF792EH   PUBLIC    CODE     ---       LCD12864_AutoWrapOn
      00FF7A95H   PUBLIC    CODE     ---       LCD12864_DisplayOff
      00FF7120H   PUBLIC    CODE     ---       reverse2
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 42


      00FF6649H   PUBLIC    CODE     ---       reverse4
      00FF7AC7H   PUBLIC    CODE     ---       LCD12864_ScrollLeft
      00FF7AE0H   PUBLIC    CODE     ---       OLED12864_HorizontalMirror
      00FF69ADH   PUBLIC    CODE     ---       LCD12864_ShowPicture
      00FF700CH   PUBLIC    CODE     ---       OLED12864_ScrollUp
      00FF7AF9H   PUBLIC    CODE     ---       OLED12864_DisplayContent
      00FF7CF7H   PUBLIC    CODE     ---       OLED12864_DisplayReverse
      00FF7948H   PUBLIC    CODE     ---       OLED12864_VerticalMirror
      00FF7962H   PUBLIC    CODE     ---       LCD12864_CursorReturnHome
      00FF797CH   PUBLIC    CODE     ---       OLED12864_DisplayEntire
      00FF7996H   PUBLIC    CODE     ---       LCD12864_DisplayOn
      00FF79B0H   PUBLIC    CODE     ---       LCD12864_CursorMoveRight
      00FF6F20H   PUBLIC    CODE     ---       LCD12864_ShowString
      00FF7B12H   PUBLIC    CODE     ---       LCD12864_CursorOff
      00FF7BA6H   PUBLIC    CODE     ---       sleep_ms
      00FF7B2BH   PUBLIC    CODE     ---       LCD12864_CursorMoveLeft
      00FF77C9H   PUBLIC    CODE     ---       sleep_us
      00FF7483H   PUBLIC    CODE     ---       LCD12864_ScrollUp
      00FF79CAH   PUBLIC    CODE     ---       LCD12864_CursorOn
      00000158H   PUBLIC    EDATA    BYTE      ?SEG7_ShowString?BYTE
      00000128H   PUBLIC    EDATA    BYTE      ?printf_hid?BYTE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      ---         MODULE    ---      ---       usb_req_class
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 43


      00FF7C8EH   PUBLIC    CODE     ---       usb_set_ctrl_line_state
      00FF74AAH   PUBLIC    CODE     ---       usb_get_line_coding
      00FF74D0H   PUBLIC    CODE     ---       usb_set_line_coding
      00FF78C5H   PUBLIC    CODE     ---       usb_req_class
      000002BAH   PUBLIC    EDATA    ---       LineCoding
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      ---         MODULE    ---      ---       usb_req_std
      00FF6EE2H   PUBLIC    CODE     ---       usb_get_interface
      00FF57F4H   PUBLIC    CODE     ---       usb_clear_feature
      00FF745BH   PUBLIC    CODE     ---       usb_set_interface
      00FF5893H   PUBLIC    CODE     ---       usb_get_descriptor
      00FF001EH   PUBLIC    CODE     ---       usb_set_descriptor
      00FF6708H   PUBLIC    CODE     ---       usb_req_std
      00FF0026H   PUBLIC    CODE     ---       usb_synch_frame
      00FF6B2DH   PUBLIC    CODE     ---       usb_set_address
      00FF59CEH   PUBLIC    CODE     ---       usb_set_feature
      00FF6811H   PUBLIC    CODE     ---       usb_get_configuration
      00FF56AFH   PUBLIC    CODE     ---       usb_set_configuration
      00FF4E67H   PUBLIC    CODE     ---       usb_get_status
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 44


      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      ---         MODULE    ---      ---       usb_req_vendor
      00FF002EH   PUBLIC    CODE     ---       usb_req_vendor
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 45


      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      ---         MODULE    ---      ---       usb_desc
      00FF6020H   PUBLIC    CODE     ---       PRODUCTDESC
      00FF603EH   PUBLIC    CODE     ---       LANGIDDESC
      00FF6042H   PUBLIC    CODE     ---       DEVICEDESC
      00FF6054H   PUBLIC    CODE     ---       CONFIGDESC
      00FF6097H   PUBLIC    CODE     ---       PACKET0
      00FF6099H   PUBLIC    CODE     ---       PACKET1
      00FF609BH   PUBLIC    CODE     ---       MANUFACTDESC
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 46


      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      ---         MODULE    ---      ---       MPU6050
      00FF67BCH   PUBLIC    CODE     ---       mpu6050_simiic_read_regs
      00FF7409H   PUBLIC    CODE     ---       mpu6050_simiic_read_reg
      00FF3FF8H   PUBLIC    CODE     ---       Kalman_Filter
      00FF6DE2H   PUBLIC    CODE     ---       Yijielvbo
      00FF6865H   PUBLIC    CODE     ---       mpu6050_init
      00FF6E63H   PUBLIC    CODE     ---       mpu6050_get_acc
      00FF63CFH   PUBLIC    CODE     ---       mpu6050_iic_init
      00FF695DH   PUBLIC    CODE     ---       mpu6050_get_gyro
      00FF2D0CH   PUBLIC    CODE     ---       IMUupdate
      00000022H   PUBLIC    EDATA    INT       mpu6050_acc_x
      00000024H   PUBLIC    EDATA    INT       mpu6050_acc_y
      00000026H   PUBLIC    EDATA    FLOAT     t_0
      0000002AH   PUBLIC    EDATA    INT       mpu6050_acc_z
      0000002CH   PUBLIC    EDATA    FLOAT     t_1
      00000030H   PUBLIC    EDATA    FLOAT     angle
      00000034H   PUBLIC    EDATA    FLOAT     exInt
      00000038H   PUBLIC    EDATA    FLOAT     eyInt
      0000003CH   PUBLIC    EDATA    FLOAT     ezInt
      00000040H   PUBLIC    EDATA    FLOAT     Angle_err
      00000044H   PUBLIC    EDATA    FLOAT     PCt_0
      00000048H   PUBLIC    EDATA    ---       Pdot
      00000058H   PUBLIC    EDATA    FLOAT     PCt_1
      0000005CH   PUBLIC    EDATA    INT       mpu6050_gyro_x
      0000005EH   PUBLIC    EDATA    INT       mpu6050_gyro_y
      00000060H   PUBLIC    EDATA    INT       mpu6050_gyro_z
      00000062H   PUBLIC    EDATA    ---       PP
      00000072H   PUBLIC    EDATA    FLOAT     q0
      00000076H   PUBLIC    EDATA    FLOAT     q1
      0000007AH   PUBLIC    EDATA    FLOAT     q2
      0000007EH   PUBLIC    EDATA    FLOAT     q3
      00000082H   PUBLIC    EDATA    FLOAT     angle_dot
      00000086H   PUBLIC    EDATA    FLOAT     d_t
      0000008AH   PUBLIC    EDATA    FLOAT     Q_angle
      0000008EH   PUBLIC    EDATA    FLOAT     R_angle
      00000092H   PUBLIC    EDATA    FLOAT     AngleX
      00000096H   PUBLIC    EDATA    FLOAT     AngleY
      0000009AH   PUBLIC    EDATA    FLOAT     E
      0000009EH   PUBLIC    EDATA    FLOAT     Q_bias
      000000A2H   PUBLIC    EDATA    CHAR      C_0
      000000A3H   PUBLIC    EDATA    FLOAT     K_0
      000000A7H   PUBLIC    EDATA    FLOAT     K_1
      000000ABH   PUBLIC    EDATA    INT       mpu_temp
      000000ADH   PUBLIC    EDATA    FLOAT     Q_gyro
      000000B1H   PUBLIC    EDATA    FLOAT     K1
      00000004H   PUBLIC    EDATA    BYTE      ?IMUupdate?BYTE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 47


      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0
      00FF7DC5H   SYMBOL    CODE     ---       mpu6050_simiic_start
      00FF7D55H   SYMBOL    CODE     ---       mpu6050_simiic_stop
      00FF753EH   SYMBOL    CODE     ---       mpu6050_simiic_write_reg
      00FF7606H   SYMBOL    CODE     ---       mpu6050_simiic_sendack
      00FF6F5DH   SYMBOL    CODE     ---       mpu6050_read_ch
      00FF727DH   SYMBOL    CODE     ---       mpu6050_send_ch
      00FF783AH   SYMBOL    CODE     ---       mpu6050_sccb_waitack
      00FF7E26H   SYMBOL    CODE     ---       mpu6050_simiic_delay

      00FF7E26H   BLOCK     CODE     ---       LVL=0
      WR6         REGSYM    ---      WORD      j
      00FF7E26H   LINE      CODE     ---       #21
      00FF7E26H   LINE      CODE     ---       #22
      00FF7E26H   LINE      CODE     ---       #23
      00FF7E28H   LINE      CODE     ---       #24
      00FF7E30H   LINE      CODE     ---       #25
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7DC5H   BLOCK     CODE     ---       LVL=0
      00FF7DC5H   LINE      CODE     ---       #28
      00FF7DC5H   LINE      CODE     ---       #30
      00FF7DC7H   LINE      CODE     ---       #31
      00FF7DC9H   LINE      CODE     ---       #32
      00FF7DCCH   LINE      CODE     ---       #33
      00FF7DCEH   LINE      CODE     ---       #34
      00FF7DD1H   LINE      CODE     ---       #35
      00FF7DD3H   LINE      CODE     ---       #36
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7D55H   BLOCK     CODE     ---       LVL=0
      00FF7D55H   LINE      CODE     ---       #39
      00FF7D55H   LINE      CODE     ---       #41
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 48


      00FF7D57H   LINE      CODE     ---       #42
      00FF7D59H   LINE      CODE     ---       #43
      00FF7D5CH   LINE      CODE     ---       #44
      00FF7D5EH   LINE      CODE     ---       #45
      00FF7D61H   LINE      CODE     ---       #46
      00FF7D63H   LINE      CODE     ---       #47
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7606H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      ack_dat
      00FF7606H   LINE      CODE     ---       #52
      00FF760AH   LINE      CODE     ---       #54
      00FF760CH   LINE      CODE     ---       #55
      00FF760FH   LINE      CODE     ---       #56
      00FF7617H   LINE      CODE     ---       #57
      00FF7619H   LINE      CODE     ---       #59
      00FF761BH   LINE      CODE     ---       #60
      00FF761EH   LINE      CODE     ---       #61
      00FF7620H   LINE      CODE     ---       #62
      00FF7623H   LINE      CODE     ---       #63
      ---         BLOCKEND  ---      ---       LVL=0

      00FF783AH   BLOCK     CODE     ---       LVL=0
      00FF783AH   LINE      CODE     ---       #66
      00FF783AH   LINE      CODE     ---       #68
      00FF783CH   LINE      CODE     ---       #70
      00FF783FH   LINE      CODE     ---       #72
      00FF7841H   LINE      CODE     ---       #73
      00FF7844H   LINE      CODE     ---       #75
      00FF7847H   LINE      CODE     ---       #78
      00FF7849H   LINE      CODE     ---       #79
      00FF784CH   LINE      CODE     ---       #80
      00FF784CH   LINE      CODE     ---       #82
      00FF784EH   LINE      CODE     ---       #83
      00FF7851H   LINE      CODE     ---       #84
      00FF7855H   LINE      CODE     ---       #85
      ---         BLOCKEND  ---      ---       LVL=0

      00FF727DH   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      c
      00FF7281H   BLOCK     CODE     NEAR LAB  LVL=1
      R14         REGSYM    ---      BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      00FF727DH   LINE      CODE     ---       #91
      00FF7281H   LINE      CODE     ---       #92
      00FF7281H   LINE      CODE     ---       #93
      00FF7284H   LINE      CODE     ---       #94
      00FF7286H   LINE      CODE     ---       #96
      00FF728FH   LINE      CODE     ---       #97
      00FF7291H   LINE      CODE     ---       #98
      00FF7293H   LINE      CODE     ---       #99
      00FF7296H   LINE      CODE     ---       #100
      00FF7298H   LINE      CODE     ---       #101
      00FF729BH   LINE      CODE     ---       #102
      00FF729DH   LINE      CODE     ---       #103
      00FF72A5H   LINE      CODE     ---       #104
      00FF72A8H   LINE      CODE     ---       #105
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6F5DH   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      ack_x
      00FF6F63H   BLOCK     CODE     NEAR LAB  LVL=1
      R13         REGSYM    ---      BYTE      i
      R14         REGSYM    ---      BYTE      c
      ---         BLOCKEND  ---      ---       LVL=1
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 49


      00FF6F5DH   LINE      CODE     ---       #111
      00FF6F63H   LINE      CODE     ---       #112
      00FF6F63H   LINE      CODE     ---       #115
      00FF6F65H   LINE      CODE     ---       #116
      00FF6F67H   LINE      CODE     ---       #117
      00FF6F6AH   LINE      CODE     ---       #118
      00FF6F6CH   LINE      CODE     ---       #120
      00FF6F6FH   LINE      CODE     ---       #122
      00FF6F72H   LINE      CODE     ---       #123
      00FF6F74H   LINE      CODE     ---       #124
      00FF6F77H   LINE      CODE     ---       #125
      00FF6F79H   LINE      CODE     ---       #126
      00FF6F7CH   LINE      CODE     ---       #127
      00FF6F7EH   LINE      CODE     ---       #128
      00FF6F81H   LINE      CODE     ---       #130
      00FF6F83H   LINE      CODE     ---       #131
      00FF6F87H   LINE      CODE     ---       #134
      00FF6F89H   LINE      CODE     ---       #135
      00FF6F8CH   LINE      CODE     ---       #136
      00FF6F91H   LINE      CODE     ---       #138
      00FF6F93H   LINE      CODE     ---       #139
      ---         BLOCKEND  ---      ---       LVL=0

      00FF753EH   BLOCK     CODE     ---       LVL=0
      R13         REGSYM    ---      BYTE      dev_add
      R14         REGSYM    ---      BYTE      reg
      R15         REGSYM    ---      BYTE      dat
      00FF753EH   LINE      CODE     ---       #151
      00FF7546H   LINE      CODE     ---       #153
      00FF7549H   LINE      CODE     ---       #154
      00FF754EH   LINE      CODE     ---       #155
      00FF7553H   LINE      CODE     ---       #156
      00FF7558H   LINE      CODE     ---       #157
      00FF755BH   LINE      CODE     ---       #158
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7409H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      dev_add
      R14         REGSYM    ---      BYTE      reg
      00FF740FH   BLOCK     CODE     NEAR LAB  LVL=1
      R15         REGSYM    ---      BYTE      dat
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7409H   LINE      CODE     ---       #169
      00FF740FH   LINE      CODE     ---       #170
      00FF740FH   LINE      CODE     ---       #172
      00FF7412H   LINE      CODE     ---       #173
      00FF7417H   LINE      CODE     ---       #174
      00FF741CH   LINE      CODE     ---       #177
      00FF741FH   LINE      CODE     ---       #178
      00FF7428H   LINE      CODE     ---       #179
      00FF742CH   LINE      CODE     ---       #180
      00FF742FH   LINE      CODE     ---       #182
      00FF742FH   LINE      CODE     ---       #183
      ---         BLOCKEND  ---      ---       LVL=0

      00FF67BCH   BLOCK     CODE     ---       LVL=0
      00000300H   SYMBOL    EDATA    BYTE      dev_add
      00000301H   SYMBOL    EDATA    BYTE      reg
      REG=3       REGSYM    ---      ---       dat_add
      00000302H   SYMBOL    EDATA    BYTE      num
      00FF67BCH   LINE      CODE     ---       #196
      00FF67CCH   LINE      CODE     ---       #198
      00FF67CFH   LINE      CODE     ---       #199
      00FF67D8H   LINE      CODE     ---       #200
      00FF67DFH   LINE      CODE     ---       #203
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 50


      00FF67E2H   LINE      CODE     ---       #204
      00FF67EDH   LINE      CODE     ---       #205
      00FF67EFH   LINE      CODE     ---       #207
      00FF67F7H   LINE      CODE     ---       #208
      00FF67F9H   LINE      CODE     ---       #209
      00FF6804H   LINE      CODE     ---       #210
      00FF680BH   LINE      CODE     ---       #211
      00FF680EH   LINE      CODE     ---       #212
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6865H   BLOCK     CODE     ---       LVL=0
      00FF6865H   LINE      CODE     ---       #220
      00FF6865H   LINE      CODE     ---       #222
      00FF686CH   LINE      CODE     ---       #224
      00FF6876H   LINE      CODE     ---       #225
      00FF6881H   LINE      CODE     ---       #226
      00FF688CH   LINE      CODE     ---       #227
      00FF6897H   LINE      CODE     ---       #228
      00FF68A2H   LINE      CODE     ---       #229
      00FF68ACH   LINE      CODE     ---       #230
      00FF68B7H   LINE      CODE     ---       #231
      00FF68B8H   LINE      CODE     ---       #232
      ---         BLOCKEND  ---      ---       LVL=0

      00FF63CFH   BLOCK     CODE     ---       LVL=0
      00FF63CFH   LINE      CODE     ---       #234
      00FF63CFH   LINE      CODE     ---       #236
      00FF63D6H   LINE      CODE     ---       #237
      00FF63E3H   LINE      CODE     ---       #238
      00FF63F2H   LINE      CODE     ---       #239
      00FF6401H   LINE      CODE     ---       #240
      00FF6410H   LINE      CODE     ---       #241
      00FF641FH   LINE      CODE     ---       #242
      00FF642CH   LINE      CODE     ---       #243
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6E63H   BLOCK     CODE     ---       LVL=0
      000002C1H   SYMBOL    EDATA    ---       dat
      00FF6E63H   LINE      CODE     ---       #254
      00FF6E63H   LINE      CODE     ---       #255
      00FF6E63H   LINE      CODE     ---       #258
      00FF6E72H   LINE      CODE     ---       #259
      00FF6E82H   LINE      CODE     ---       #260
      00FF6E92H   LINE      CODE     ---       #261
      00FF6EA2H   LINE      CODE     ---       #262
      ---         BLOCKEND  ---      ---       LVL=0

      00FF695DH   BLOCK     CODE     ---       LVL=0
      0000028AH   SYMBOL    EDATA    ---       dat
      00FF695DH   LINE      CODE     ---       #270
      00FF695DH   LINE      CODE     ---       #271
      00FF695DH   LINE      CODE     ---       #274
      00FF696CH   LINE      CODE     ---       #275
      00FF697CH   LINE      CODE     ---       #276
      00FF698CH   LINE      CODE     ---       #277
      00FF699CH   LINE      CODE     ---       #278
      00FF69ACH   LINE      CODE     ---       #279
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3FF8H   BLOCK     CODE     ---       LVL=0
      DR24        REGSYM    ---      FLOAT     Accel
      DR28        REGSYM    ---      FLOAT     Gyro
      00FF3FF8H   LINE      CODE     ---       #304
      00FF3FFEH   LINE      CODE     ---       #306
      00FF401BH   LINE      CODE     ---       #307
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 51


      00FF4035H   LINE      CODE     ---       #309
      00FF4040H   LINE      CODE     ---       #310
      00FF4044H   LINE      CODE     ---       #311
      00FF404CH   LINE      CODE     ---       #312
      00FF4060H   LINE      CODE     ---       #313
      00FF4072H   LINE      CODE     ---       #314
      00FF4084H   LINE      CODE     ---       #315
      00FF4098H   LINE      CODE     ---       #317
      00FF40A5H   LINE      CODE     ---       #319
      00FF40BBH   LINE      CODE     ---       #320
      00FF40CAH   LINE      CODE     ---       #322
      00FF40E0H   LINE      CODE     ---       #324
      00FF40EFH   LINE      CODE     ---       #325
      00FF40FCH   LINE      CODE     ---       #327
      00FF4100H   LINE      CODE     ---       #328
      00FF410FH   LINE      CODE     ---       #330
      00FF412BH   LINE      CODE     ---       #331
      00FF413FH   LINE      CODE     ---       #332
      00FF4155H   LINE      CODE     ---       #333
      00FF416BH   LINE      CODE     ---       #335
      00FF4183H   LINE      CODE     ---       #336
      00FF4195H   LINE      CODE     ---       #337
      00FF41A2H   LINE      CODE     ---       #338
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6DE2H   BLOCK     CODE     ---       LVL=0
      DR24        REGSYM    ---      FLOAT     angle_m
      DR28        REGSYM    ---      FLOAT     gyro_m
      00FF6DE2H   LINE      CODE     ---       #345
      00FF6DE6H   LINE      CODE     ---       #347
      00FF6E22H   LINE      CODE     ---       #348
      ---         BLOCKEND  ---      ---       LVL=0

      00FF2D0CH   BLOCK     CODE     ---       LVL=0
      00000008H   SYMBOL    EDATA    FLOAT     gx
      DR12        REGSYM    ---      FLOAT     gy
      0000000CH   SYMBOL    EDATA    FLOAT     gz
      00000010H   SYMBOL    EDATA    FLOAT     ax
      00000014H   SYMBOL    EDATA    FLOAT     ay
      00000018H   SYMBOL    EDATA    FLOAT     az
      00FF2D14H   BLOCK     CODE     NEAR LAB  LVL=1
      DR28        REGSYM    ---      FLOAT     norm
      0000001CH   SYMBOL    EDATA    FLOAT     vx
      DR24        REGSYM    ---      FLOAT     vy
      DR28        REGSYM    ---      FLOAT     vz
      DR16        REGSYM    ---      FLOAT     ex
      DR20        REGSYM    ---      FLOAT     ey
      DR24        REGSYM    ---      FLOAT     ez
      ---         BLOCKEND  ---      ---       LVL=1
      00FF2D0CH   LINE      CODE     ---       #366
      00FF2D14H   LINE      CODE     ---       #367
      00FF2D14H   LINE      CODE     ---       #372
      00FF2D42H   LINE      CODE     ---       #373
      00FF2D4FH   LINE      CODE     ---       #374
      00FF2D5CH   LINE      CODE     ---       #375
      00FF2D69H   LINE      CODE     ---       #381
      00FF2D99H   LINE      CODE     ---       #382
      00FF2DBBH   LINE      CODE     ---       #383
      00FF2DF8H   LINE      CODE     ---       #385
      00FF2E13H   LINE      CODE     ---       #386
      00FF2E30H   LINE      CODE     ---       #387
      00FF2E4DH   LINE      CODE     ---       #389
      00FF2E67H   LINE      CODE     ---       #390
      00FF2E79H   LINE      CODE     ---       #391
      00FF2E8BH   LINE      CODE     ---       #393
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 52


      00FF2EACH   LINE      CODE     ---       #394
      00FF2EC1H   LINE      CODE     ---       #395
      00FF2EDAH   LINE      CODE     ---       #397
      00FF2F2AH   LINE      CODE     ---       #398
      00FF2F65H   LINE      CODE     ---       #399
      00FF2F9AH   LINE      CODE     ---       #400
      00FF2FD7H   LINE      CODE     ---       #402
      00FF3017H   LINE      CODE     ---       #403
      00FF3024H   LINE      CODE     ---       #404
      00FF3031H   LINE      CODE     ---       #405
      00FF303EH   LINE      CODE     ---       #406
      00FF304BH   LINE      CODE     ---       #408
      00FF3083H   LINE      CODE     ---       #409
      00FF30BBH   LINE      CODE     ---       #410
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       OLED_SPI
      00FF7CA4H   PUBLIC    CODE     ---       OLED_Display_On
      00FF7305H   PUBLIC    CODE     ---       OLED_DisplayTurn
      00FF520EH   PUBLIC    CODE     ---       OLED_Init
      00FF4D63H   PUBLIC    CODE     ---       OLED_ShowFloat
      00FF61A1H   PUBLIC    CODE     ---       OLED_ShowString
      00FF79E4H   PUBLIC    CODE     ---       OLED_WR_Byte
      00FF5472H   PUBLIC    CODE     ---       OLED_ShowChar
      00FF7736H   PUBLIC    CODE     ---       OLED_ColorTurn
      00FF7432H   PUBLIC    CODE     ---       OLED_Set_Pos
      00FF5B97H   PUBLIC    CODE     ---       OLED_ShowInt
      00FF4F6AH   PUBLIC    CODE     ---       OLED_ShowNum
      00FF7CB9H   PUBLIC    CODE     ---       OLED_Display_Off
      00FF79FEH   PUBLIC    CODE     ---       oled_pow
      00FF6125H   PUBLIC    CODE     ---       OLED_ShowChinese
      00FF70B5H   PUBLIC    CODE     ---       OLED_Clear
      00FF5C2FH   PUBLIC    CODE     ---       OLED_DrawBMP
      00FF0E5BH   PUBLIC    CODE     ---       asc2_0806
      00FF1083H   PUBLIC    CODE     ---       asc2_1608
      00FF1673H   PUBLIC    CODE     ---       Hzk
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000A0H.6 SFRSYM    DATA     BIT       OLED_RES
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000A0H.2 SFRSYM    DATA     BIT       OLED_CS
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 53


      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A0H.4 SFRSYM    DATA     BIT       OLED_DC
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF7736H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      i
      00FF7736H   LINE      CODE     ---       #222
      00FF773AH   LINE      CODE     ---       #224
      00FF773EH   LINE      CODE     ---       #226
      00FF7745H   LINE      CODE     ---       #227
      00FF7745H   LINE      CODE     ---       #228
      00FF774AH   LINE      CODE     ---       #230
      00FF7751H   LINE      CODE     ---       #231
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7305H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      i
      00FF7305H   LINE      CODE     ---       #235
      00FF7309H   LINE      CODE     ---       #237
      00FF730DH   LINE      CODE     ---       #239
      00FF7314H   LINE      CODE     ---       #240
      00FF731BH   LINE      CODE     ---       #241
      00FF731BH   LINE      CODE     ---       #242
      00FF7320H   LINE      CODE     ---       #244
      00FF7327H   LINE      CODE     ---       #245
      00FF732EH   LINE      CODE     ---       #246
      ---         BLOCKEND  ---      ---       LVL=0

      00FF79E4H   BLOCK     CODE     ---       LVL=0
      R7          REGSYM    ---      BYTE      dat
      R10         REGSYM    ---      BYTE      cmd
      00FF79E4H   LINE      CODE     ---       #276
      00FF79E8H   LINE      CODE     ---       #278
      00FF79ECH   LINE      CODE     ---       #279
      00FF79F0H   LINE      CODE     ---       #281
      00FF79F2H   LINE      CODE     ---       #282
      00FF79F4H   LINE      CODE     ---       #283
      00FF79F9H   LINE      CODE     ---       #284
      00FF79FBH   LINE      CODE     ---       #285
      00FF79FDH   LINE      CODE     ---       #286
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7432H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      x
      R10         REGSYM    ---      BYTE      y
      00FF7432H   LINE      CODE     ---       #290
      00FF7436H   LINE      CODE     ---       #292
      00FF7441H   LINE      CODE     ---       #293
      00FF744FH   LINE      CODE     ---       #294
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 54


      00FF7458H   LINE      CODE     ---       #295
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7CA4H   BLOCK     CODE     ---       LVL=0
      00FF7CA4H   LINE      CODE     ---       #297
      00FF7CA4H   LINE      CODE     ---       #299
      00FF7CABH   LINE      CODE     ---       #300
      00FF7CB2H   LINE      CODE     ---       #301
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7CB9H   BLOCK     CODE     ---       LVL=0
      00FF7CB9H   LINE      CODE     ---       #304
      00FF7CB9H   LINE      CODE     ---       #306
      00FF7CC0H   LINE      CODE     ---       #307
      00FF7CC7H   LINE      CODE     ---       #308
      ---         BLOCKEND  ---      ---       LVL=0

      00FF70B5H   BLOCK     CODE     ---       LVL=0
      00FF70B7H   BLOCK     CODE     NEAR LAB  LVL=1
      R15         REGSYM    ---      BYTE      i
      R14         REGSYM    ---      BYTE      n
      ---         BLOCKEND  ---      ---       LVL=1
      00FF70B5H   LINE      CODE     ---       #311
      00FF70B7H   LINE      CODE     ---       #312
      00FF70B7H   LINE      CODE     ---       #314
      00FF70B9H   LINE      CODE     ---       #316
      00FF70C4H   LINE      CODE     ---       #317
      00FF70CAH   LINE      CODE     ---       #318
      00FF70D1H   LINE      CODE     ---       #319
      00FF70E1H   LINE      CODE     ---       #320
      00FF70E8H   LINE      CODE     ---       #321
      ---         BLOCKEND  ---      ---       LVL=0

      00FF5472H   BLOCK     CODE     ---       LVL=0
      R13         REGSYM    ---      BYTE      x
      R14         REGSYM    ---      BYTE      y
      R10         REGSYM    ---      BYTE      chr
      R15         REGSYM    ---      BYTE      sizey
      00FF547CH   BLOCK     CODE     NEAR LAB  LVL=1
      R12         REGSYM    ---      BYTE      c
      000002D6H   SYMBOL    EDATA    BYTE      sizex
      000002D7H   SYMBOL    EDATA    WORD      i
      000002D9H   SYMBOL    EDATA    WORD      size1
      ---         BLOCKEND  ---      ---       LVL=1
      00FF5472H   LINE      CODE     ---       #327
      00FF547CH   LINE      CODE     ---       #328
      00FF547CH   LINE      CODE     ---       #329
      00FF5486H   LINE      CODE     ---       #330
      00FF548CH   LINE      CODE     ---       #331
      00FF549BH   LINE      CODE     ---       #332
      00FF54B9H   LINE      CODE     ---       #333
      00FF54C1H   LINE      CODE     ---       #334
      00FF54C8H   LINE      CODE     ---       #335
      00FF54CCH   LINE      CODE     ---       #337
      00FF54EAH   LINE      CODE     ---       #338
      00FF54FEH   LINE      CODE     ---       #339
      00FF551EH   LINE      CODE     ---       #341
      00FF551EH   LINE      CODE     ---       #342
      00FF5532H   LINE      CODE     ---       #343
      ---         BLOCKEND  ---      ---       LVL=0

      00FF79FEH   BLOCK     CODE     ---       LVL=0
      R8          REGSYM    ---      BYTE      m
      R9          REGSYM    ---      BYTE      n
      00FF7A02H   BLOCK     CODE     NEAR LAB  LVL=1
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 55


      DR4         REGSYM    ---      DWORD     result
      ---         BLOCKEND  ---      ---       LVL=1
      00FF79FEH   LINE      CODE     ---       #345
      00FF7A02H   LINE      CODE     ---       #346
      00FF7A02H   LINE      CODE     ---       #347
      00FF7A06H   LINE      CODE     ---       #348
      00FF7A17H   LINE      CODE     ---       #349
      00FF7A17H   LINE      CODE     ---       #350
      ---         BLOCKEND  ---      ---       LVL=0

      00FF4F6AH   BLOCK     CODE     ---       LVL=0
      00000292H   SYMBOL    EDATA    BYTE      x
      00000293H   SYMBOL    EDATA    BYTE      y
      DR12        REGSYM    ---      DWORD     num
      00000294H   SYMBOL    EDATA    BYTE      len
      00000295H   SYMBOL    EDATA    BYTE      sizey
      00FF4F7EH   BLOCK     CODE     NEAR LAB  LVL=1
      00000296H   SYMBOL    EDATA    BYTE      t
      00000297H   SYMBOL    EDATA    BYTE      temp
      00000298H   SYMBOL    EDATA    BYTE      m
      00000299H   SYMBOL    EDATA    BYTE      enshow
      ---         BLOCKEND  ---      ---       LVL=1
      00FF4F6AH   LINE      CODE     ---       #356
      00FF4F7EH   LINE      CODE     ---       #357
      00FF4F7EH   LINE      CODE     ---       #358
      00FF4F83H   LINE      CODE     ---       #359
      00FF4F87H   LINE      CODE     ---       #360
      00FF4F94H   LINE      CODE     ---       #361
      00FF4F98H   LINE      CODE     ---       #363
      00FF4FBFH   LINE      CODE     ---       #364
      00FF4FD7H   LINE      CODE     ---       #366
      00FF4FDDH   LINE      CODE     ---       #368
      00FF4FFEH   LINE      CODE     ---       #369
      00FF5000H   LINE      CODE     ---       #370
      00FF5006H   LINE      CODE     ---       #371
      00FF5006H   LINE      CODE     ---       #372
      00FF5039H   LINE      CODE     ---       #373
      00FF504FH   LINE      CODE     ---       #374
      ---         BLOCKEND  ---      ---       LVL=0

      00FF61A1H   BLOCK     CODE     ---       LVL=0
      000002ECH   SYMBOL    EDATA    BYTE      x
      000002EDH   SYMBOL    EDATA    BYTE      y
      REG=3       REGSYM    ---      ---       chr
      000002EEH   SYMBOL    EDATA    BYTE      sizey
      00FF61B1H   BLOCK     CODE     NEAR LAB  LVL=1
      000002EFH   SYMBOL    EDATA    BYTE      j
      ---         BLOCKEND  ---      ---       LVL=1
      00FF61A1H   LINE      CODE     ---       #376
      00FF61B1H   LINE      CODE     ---       #377
      00FF61B1H   LINE      CODE     ---       #378
      00FF61B6H   LINE      CODE     ---       #379
      00FF61B8H   LINE      CODE     ---       #381
      00FF61DCH   LINE      CODE     ---       #382
      00FF61F1H   LINE      CODE     ---       #383
      00FF6202H   LINE      CODE     ---       #384
      00FF6211H   LINE      CODE     ---       #385
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6125H   BLOCK     CODE     ---       LVL=0
      R12         REGSYM    ---      BYTE      x
      R14         REGSYM    ---      BYTE      y
      R13         REGSYM    ---      BYTE      no
      R15         REGSYM    ---      BYTE      sizey
      00FF612FH   BLOCK     CODE     NEAR LAB  LVL=1
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 56


      000002F0H   SYMBOL    EDATA    WORD      i
      000002F2H   SYMBOL    EDATA    WORD      size1
      ---         BLOCKEND  ---      ---       LVL=1
      00FF6125H   LINE      CODE     ---       #387
      00FF612FH   LINE      CODE     ---       #388
      00FF612FH   LINE      CODE     ---       #389
      00FF6151H   LINE      CODE     ---       #390
      00FF6155H   LINE      CODE     ---       #392
      00FF616AH   LINE      CODE     ---       #393
      00FF618AH   LINE      CODE     ---       #395
      00FF618AH   LINE      CODE     ---       #396
      00FF619EH   LINE      CODE     ---       #397
      ---         BLOCKEND  ---      ---       LVL=0

      00FF5C2FH   BLOCK     CODE     ---       LVL=0
      0000029AH   SYMBOL    EDATA    BYTE      x
      0000029BH   SYMBOL    EDATA    BYTE      y
      0000029CH   SYMBOL    EDATA    BYTE      sizex
      0000029DH   SYMBOL    EDATA    BYTE      sizey
      REG=3       REGSYM    ---      ---       BMP
      00FF5C43H   BLOCK     CODE     NEAR LAB  LVL=1
      0000029EH   SYMBOL    EDATA    WORD      j
      000002A0H   SYMBOL    EDATA    BYTE      i
      000002A1H   SYMBOL    EDATA    BYTE      m
      ---         BLOCKEND  ---      ---       LVL=1
      00FF5C2FH   LINE      CODE     ---       #404
      00FF5C43H   LINE      CODE     ---       #405
      00FF5C43H   LINE      CODE     ---       #406
      00FF5C49H   LINE      CODE     ---       #408
      00FF5C69H   LINE      CODE     ---       #409
      00FF5C6CH   LINE      CODE     ---       #411
      00FF5C81H   LINE      CODE     ---       #412
      00FF5C84H   LINE      CODE     ---       #414
      00FF5C9DH   LINE      CODE     ---       #415
      00FF5CB0H   LINE      CODE     ---       #416
      00FF5CC3H   LINE      CODE     ---       #417
      ---         BLOCKEND  ---      ---       LVL=0

      00FF4D63H   BLOCK     CODE     ---       LVL=0
      0000015CH   SYMBOL    EDATA    BYTE      x
      0000015DH   SYMBOL    EDATA    BYTE      y
      DR12        REGSYM    ---      FLOAT     dat
      0000015EH   SYMBOL    EDATA    BYTE      num
      0000015FH   SYMBOL    EDATA    BYTE      pointnum
      00000160H   SYMBOL    EDATA    BYTE      size2
      00FF4D7BH   BLOCK     CODE     NEAR LAB  LVL=1
      00000161H   SYMBOL    EDATA    BYTE      length
      00000162H   SYMBOL    EDATA    ---       buff
      00000184H   SYMBOL    EDATA    CHAR      start
      00000185H   SYMBOL    EDATA    CHAR      end
      R7          REGSYM    ---      CHAR      point
      ---         BLOCKEND  ---      ---       LVL=1
      00FF4D63H   LINE      CODE     ---       #429
      00FF4D7BH   LINE      CODE     ---       #430
      00FF4D7BH   LINE      CODE     ---       #435
      00FF4D8AH   LINE      CODE     ---       #436
      00FF4D99H   LINE      CODE     ---       #438
      00FF4DA2H   LINE      CODE     ---       #439
      00FF4DB9H   LINE      CODE     ---       #440
      00FF4DBBH   LINE      CODE     ---       #441
      00FF4DD4H   LINE      CODE     ---       #442
      00FF4DD9H   LINE      CODE     ---       #443
      00FF4DD9H   LINE      CODE     ---       #445
      00FF4DE3H   LINE      CODE     ---       #446
      00FF4DF3H   LINE      CODE     ---       #447
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 57


      00FF4E03H   LINE      CODE     ---       #449
      00FF4E08H   LINE      CODE     ---       #450
      00FF4E12H   LINE      CODE     ---       #451
      00FF4E1BH   LINE      CODE     ---       #452
      00FF4E24H   LINE      CODE     ---       #453
      00FF4E2DH   LINE      CODE     ---       #455
      00FF4E36H   LINE      CODE     ---       #456
      00FF4E3AH   LINE      CODE     ---       #458
      00FF4E46H   LINE      CODE     ---       #460
      00FF4E51H   LINE      CODE     ---       #462
      00FF4E64H   LINE      CODE     ---       #463
      ---         BLOCKEND  ---      ---       LVL=0

      00FF5B97H   BLOCK     CODE     ---       LVL=0
      R12         REGSYM    ---      BYTE      x
      R13         REGSYM    ---      BYTE      y
      000001AEH   SYMBOL    EDATA    LONG      dat
      R15         REGSYM    ---      BYTE      num
      R14         REGSYM    ---      BYTE      size2
      00FF5BA5H   BLOCK     CODE     NEAR LAB  LVL=1
      000001B2H   SYMBOL    EDATA    ---       buff
      000001D4H   SYMBOL    EDATA    BYTE      length
      ---         BLOCKEND  ---      ---       LVL=1
      00FF5B97H   LINE      CODE     ---       #475
      00FF5BA5H   LINE      CODE     ---       #476
      00FF5BA5H   LINE      CODE     ---       #480
      00FF5BADH   LINE      CODE     ---       #481
      00FF5BAFH   LINE      CODE     ---       #483
      00FF5BB9H   LINE      CODE     ---       #484
      00FF5BD0H   LINE      CODE     ---       #485
      00FF5BD2H   LINE      CODE     ---       #486
      00FF5BD8H   LINE      CODE     ---       #487
      00FF5BF5H   LINE      CODE     ---       #488
      00FF5BFAH   LINE      CODE     ---       #489
      00FF5BFAH   LINE      CODE     ---       #491
      00FF5BFFH   LINE      CODE     ---       #492
      00FF5C09H   LINE      CODE     ---       #493
      00FF5C12H   LINE      CODE     ---       #494
      00FF5C18H   LINE      CODE     ---       #496
      00FF5C1FH   LINE      CODE     ---       #498
      00FF5C2CH   LINE      CODE     ---       #499
      ---         BLOCKEND  ---      ---       LVL=0

      00FF520EH   BLOCK     CODE     ---       LVL=0
      00FF520EH   LINE      CODE     ---       #502
      00FF520EH   LINE      CODE     ---       #505
      00FF5215H   LINE      CODE     ---       #507
      00FF5217H   LINE      CODE     ---       #508
      00FF521EH   LINE      CODE     ---       #509
      00FF5220H   LINE      CODE     ---       #510
      00FF5227H   LINE      CODE     ---       #511
      00FF522DH   LINE      CODE     ---       #512
      00FF5234H   LINE      CODE     ---       #513
      00FF523BH   LINE      CODE     ---       #515
      00FF5242H   LINE      CODE     ---       #516
      00FF5249H   LINE      CODE     ---       #518
      00FF5250H   LINE      CODE     ---       #519
      00FF5257H   LINE      CODE     ---       #521
      00FF525EH   LINE      CODE     ---       #523
      00FF5265H   LINE      CODE     ---       #524
      00FF526CH   LINE      CODE     ---       #526
      00FF5273H   LINE      CODE     ---       #527
      00FF5279H   LINE      CODE     ---       #529
      00FF5280H   LINE      CODE     ---       #530
      00FF5287H   LINE      CODE     ---       #532
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 58


      00FF528EH   LINE      CODE     ---       #533
      00FF5295H   LINE      CODE     ---       #535
      00FF529CH   LINE      CODE     ---       #536
      00FF52A3H   LINE      CODE     ---       #538
      00FF52AAH   LINE      CODE     ---       #539
      00FF52B1H   LINE      CODE     ---       #541
      00FF52B8H   LINE      CODE     ---       #542
      00FF52BFH   LINE      CODE     ---       #544
      00FF52C6H   LINE      CODE     ---       #545
      00FF52CDH   LINE      CODE     ---       #547
      00FF52D4H   LINE      CODE     ---       #548
      00FF52DBH   LINE      CODE     ---       #550
      00FF52DEH   LINE      CODE     ---       #551
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       MKS
      00FF5052H   PUBLIC    CODE     ---       CAN_MKS_CONTROL
      00FF5CC6H   PUBLIC    CODE     ---       CAN_MKS_SPD_CONTROL
      00FF52E5H   PUBLIC    CODE     ---       MKS_HOME
      00FF5136H   PUBLIC    CODE     ---       MKS_PID_SET
      00FF0057H   PUBLIC    CODE     ---       MKS_ALL_RUN
      00000214H   PUBLIC    EDATA    WORD      MOTOR_state
      00000216H   PUBLIC    EDATA    ---       long_uchar1
      0000021AH   PUBLIC    EDATA    WORD      MKS_KD
      0000021CH   PUBLIC    EDATA    WORD      MKS_KI
      0000021EH   PUBLIC    EDATA    WORD      MKS_KP
      00000220H   PUBLIC    EDATA    WORD      MKS_KV
      00000222H   PUBLIC    EDATA    ---       MKS_TX
      00000021H.0 PUBLIC    BIT      BIT       ?CAN_MKS_CONTROL?BIT
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 59


      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF5052H   BLOCK     CODE     ---       LVL=0
      WR2         REGSYM    ---      WORD      Motor
      DR28        REGSYM    ---      LONG      Pos
      WR8         REGSYM    ---      WORD      Spd
      R10         REGSYM    ---      BYTE      Acc
      00000021H.0 SYMBOL    BIT      BIT       Mode
      00FF505CH   BLOCK     CODE     NEAR LAB  LVL=1
      R15         REGSYM    ---      BYTE      crc
      ---         BLOCKEND  ---      ---       LVL=1
      00FF5052H   LINE      CODE     ---       #22
      00FF505CH   LINE      CODE     ---       #23
      00FF505CH   LINE      CODE     ---       #25
      00FF505CH   LINE      CODE     ---       #27
      00FF5060H   LINE      CODE     ---       #29
      00FF5063H   LINE      CODE     ---       #31
      00FF5069H   LINE      CODE     ---       #32
      00FF506FH   LINE      CODE     ---       #33
      00FF5075H   LINE      CODE     ---       #34
      00FF5079H   LINE      CODE     ---       #36
      00FF508AH   LINE      CODE     ---       #37
      00FF508AH   LINE      CODE     ---       #38
      00FF508AH   LINE      CODE     ---       #39
      00FF508AH   LINE      CODE     ---       #41
      00FF508AH   LINE      CODE     ---       #42
      00FF508AH   LINE      CODE     ---       #43
      00FF508CH   LINE      CODE     ---       #46
      00FF5092H   LINE      CODE     ---       #48
      00FF509EH   LINE      CODE     ---       #49
      00FF50A4H   LINE      CODE     ---       #50
      00FF50A8H   LINE      CODE     ---       #52
      00FF50B9H   LINE      CODE     ---       #53
      00FF50C1H   LINE      CODE     ---       #54
      00FF50C9H   LINE      CODE     ---       #55
      00FF50D1H   LINE      CODE     ---       #57
      00FF5105H   LINE      CODE     ---       #58
      00FF5109H   LINE      CODE     ---       #59
      00FF5109H   LINE      CODE     ---       #61
      00FF511EH   LINE      CODE     ---       #62
      00FF5133H   LINE      CODE     ---       #64
      ---         BLOCKEND  ---      ---       LVL=0

      00FF5CC6H   BLOCK     CODE     ---       LVL=0
      WR14        REGSYM    ---      WORD      Motor
      WR2         REGSYM    ---      INT       Spd
      R13         REGSYM    ---      BYTE      Acc
      00FF5CD0H   BLOCK     CODE     NEAR LAB  LVL=1
      R7          REGSYM    ---      BYTE      crc
      ---         BLOCKEND  ---      ---       LVL=1
      00FF5CC6H   LINE      CODE     ---       #65
      00FF5CD0H   LINE      CODE     ---       #66
      00FF5CD0H   LINE      CODE     ---       #68
      00FF5CD0H   LINE      CODE     ---       #69
      00FF5CD6H   LINE      CODE     ---       #70
      00FF5CDBH   LINE      CODE     ---       #71
      00FF5CE9H   LINE      CODE     ---       #72
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 60


      00FF5CF3H   LINE      CODE     ---       #73
      00FF5CFAH   LINE      CODE     ---       #74
      00FF5D14H   LINE      CODE     ---       #75
      00FF5D1FH   LINE      CODE     ---       #76
      00FF5D23H   LINE      CODE     ---       #77
      00FF5D3FH   LINE      CODE     ---       #78
      00FF5D43H   LINE      CODE     ---       #79
      00FF5D58H   LINE      CODE     ---       #81
      ---         BLOCKEND  ---      ---       LVL=0

      00FF52E5H   BLOCK     CODE     ---       LVL=0
      WR14        REGSYM    ---      WORD      Motor
      WR2         REGSYM    ---      INT       Spd
      00FF52EBH   BLOCK     CODE     NEAR LAB  LVL=1
      R7          REGSYM    ---      BYTE      crc
      ---         BLOCKEND  ---      ---       LVL=1
      00FF52E5H   LINE      CODE     ---       #82
      00FF52EBH   LINE      CODE     ---       #83
      00FF52EBH   LINE      CODE     ---       #85
      00FF52F1H   LINE      CODE     ---       #86
      00FF52F6H   LINE      CODE     ---       #87
      00FF5300H   LINE      CODE     ---       #88
      00FF5305H   LINE      CODE     ---       #89
      00FF530CH   LINE      CODE     ---       #90
      00FF5319H   LINE      CODE     ---       #91
      00FF5324H   LINE      CODE     ---       #92
      00FF532AH   LINE      CODE     ---       #93
      00FF5358H   LINE      CODE     ---       #94
      00FF535CH   LINE      CODE     ---       #95
      00FF5371H   LINE      CODE     ---       #96
      00FF5377H   LINE      CODE     ---       #97
      00FF537DH   LINE      CODE     ---       #98
      00FF5381H   LINE      CODE     ---       #99
      00FF5398H   LINE      CODE     ---       #100
      00FF53ABH   LINE      CODE     ---       #101
      ---         BLOCKEND  ---      ---       LVL=0

      00FF5136H   BLOCK     CODE     ---       LVL=0
      WR14        REGSYM    ---      WORD      Motor
      00FF513AH   BLOCK     CODE     NEAR LAB  LVL=1
      R7          REGSYM    ---      BYTE      crc
      ---         BLOCKEND  ---      ---       LVL=1
      00FF5136H   LINE      CODE     ---       #104
      00FF513AH   LINE      CODE     ---       #105
      00FF513AH   LINE      CODE     ---       #107
      00FF513AH   LINE      CODE     ---       #109
      00FF5140H   LINE      CODE     ---       #110
      00FF5145H   LINE      CODE     ---       #111
      00FF514DH   LINE      CODE     ---       #112
      00FF5151H   LINE      CODE     ---       #113
      00FF5159H   LINE      CODE     ---       #114
      00FF515DH   LINE      CODE     ---       #115
      00FF5189H   LINE      CODE     ---       #116
      00FF518DH   LINE      CODE     ---       #117
      00FF51A2H   LINE      CODE     ---       #120
      00FF51A8H   LINE      CODE     ---       #121
      00FF51AEH   LINE      CODE     ---       #122
      00FF51B6H   LINE      CODE     ---       #123
      00FF51BAH   LINE      CODE     ---       #124
      00FF51C2H   LINE      CODE     ---       #125
      00FF51C6H   LINE      CODE     ---       #126
      00FF51F2H   LINE      CODE     ---       #127
      00FF51F6H   LINE      CODE     ---       #128
      00FF520BH   LINE      CODE     ---       #130
      ---         BLOCKEND  ---      ---       LVL=0
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 61



      00FF0057H   BLOCK     CODE     ---       LVL=0
      00FF0057H   LINE      CODE     ---       #132
      00FF0057H   LINE      CODE     ---       #135
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       D2Car
      00FF7188H   PUBLIC    CODE     ---       ESD_M1_SPD_CONTROL
      00FF62F3H   PUBLIC    CODE     ---       ESD_M1_POS_CONTROL
      00FF71EDH   PUBLIC    CODE     ---       ESD_M1_PWM_CONTROL
      00FF5535H   PUBLIC    CODE     ---       CAR_PID_CONTROL
      00FF4B43H   PUBLIC    CODE     ---       CAR_ADD_ANGLE
      000000B5H   PUBLIC    EDATA    INT       ACC_X
      000000B7H   PUBLIC    EDATA    INT       ACC_Y
      000000B9H   PUBLIC    EDATA    INT       ACC_Z
      000000BBH   PUBLIC    EDATA    FLOAT     CAR_ANG
      000000BFH   PUBLIC    EDATA    ---       D2long_uchar
      000000C3H   PUBLIC    EDATA    FLOAT     ANG_ERR
      000000C7H   PUBLIC    EDATA    FLOAT     SET_ANG
      000000CBH   PUBLIC    EDATA    INT       GYRO_Z_OFFSET
      000000CDH   PUBLIC    EDATA    FLOAT     ANG_OUT
      000000D1H   PUBLIC    EDATA    ---       D2_CAN_TX
      000000D9H   PUBLIC    EDATA    ---       D2int_uchar
      000000DBH   PUBLIC    EDATA    INT       GYRO_Z_OFFSET_ADD
      000000DDH   PUBLIC    EDATA    BYTE      GYRO_Z_OFFSET_N
      000000DEH   PUBLIC    EDATA    FLOAT     ANG_ERR_OLD
      000000E2H   PUBLIC    EDATA    ---       MPU_data
      000000F0H   PUBLIC    EDATA    FLOAT     GYRO_X
      000000F4H   PUBLIC    EDATA    FLOAT     GYRO_Y
      000000F8H   PUBLIC    EDATA    FLOAT     GYRO_Z
      00000021H.1 PUBLIC    BIT      BIT       ?ESD_M1_POS_CONTROL?BIT
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 62


      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF5535H   BLOCK     CODE     ---       LVL=0
      00FF5535H   LINE      CODE     ---       #38
      00FF5535H   LINE      CODE     ---       #40
      00FF553EH   LINE      CODE     ---       #42
      00FF5543H   LINE      CODE     ---       #43
      00FF554FH   LINE      CODE     ---       #44
      00FF5550H   LINE      CODE     ---       #47
      00FF556AH   LINE      CODE     ---       #48
      00FF5594H   LINE      CODE     ---       #49
      00FF55A5H   LINE      CODE     ---       #50
      00FF55CDH   LINE      CODE     ---       #52
      00FF55D8H   LINE      CODE     ---       #53
      00FF55EDH   LINE      CODE     ---       #55
      00FF55F5H   LINE      CODE     ---       #56
      00FF55F5H   LINE      CODE     ---       #57
      ---         BLOCKEND  ---      ---       LVL=0

      00FF4B43H   BLOCK     CODE     ---       LVL=0
      DR28        REGSYM    ---      FLOAT     SET_ANGLE
      00FF4B47H   BLOCK     CODE     NEAR LAB  LVL=1
      R15         REGSYM    ---      BYTE      n
      ---         BLOCKEND  ---      ---       LVL=1
      00FF4B43H   LINE      CODE     ---       #60
      00FF4B47H   LINE      CODE     ---       #61
      00FF4B47H   LINE      CODE     ---       #62
      00FF4B4AH   LINE      CODE     ---       #64
      00FF4B57H   LINE      CODE     ---       #65
      00FF4B57H   LINE      CODE     ---       #66
      00FF4B59H   LINE      CODE     ---       #68
      00FF4B6EH   LINE      CODE     ---       #69
      00FF4B83H   LINE      CODE     ---       #70
      00FF4B98H   LINE      CODE     ---       #71
      00FF4BADH   LINE      CODE     ---       #72
      00FF4BB4H   LINE      CODE     ---       #74
      00FF4BC6H   LINE      CODE     ---       #75
      00FF4BC8H   LINE      CODE     ---       #77
      00FF4BDDH   LINE      CODE     ---       #78
      00FF4BF2H   LINE      CODE     ---       #79
      00FF4C07H   LINE      CODE     ---       #80
      00FF4C1CH   LINE      CODE     ---       #81
      00FF4C23H   LINE      CODE     ---       #83
      00FF4C2BH   LINE      CODE     ---       #84
      00FF4C35H   LINE      CODE     ---       #85
      00FF4C3FH   LINE      CODE     ---       #86
      00FF4C49H   LINE      CODE     ---       #87
      00FF4C53H   LINE      CODE     ---       #88
      ---         BLOCKEND  ---      ---       LVL=0

      00FF62F3H   BLOCK     CODE     ---       LVL=0
      WR2         REGSYM    ---      WORD      Motor
      DR28        REGSYM    ---      LONG      Pos
      WR4         REGSYM    ---      WORD      Spd
      R10         REGSYM    ---      BYTE      Acc
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 63


      00000021H.1 SYMBOL    BIT      BIT       Mode
      00FF62F3H   LINE      CODE     ---       #91
      00FF62F9H   LINE      CODE     ---       #93
      00FF6300H   LINE      CODE     ---       #94
      00FF6306H   LINE      CODE     ---       #96
      00FF630AH   LINE      CODE     ---       #97
      00FF6310H   LINE      CODE     ---       #99
      00FF6314H   LINE      CODE     ---       #101
      00FF6318H   LINE      CODE     ---       #103
      00FF6320H   LINE      CODE     ---       #104
      00FF6328H   LINE      CODE     ---       #105
      00FF6330H   LINE      CODE     ---       #106
      00FF6338H   LINE      CODE     ---       #108
      00FF634DH   LINE      CODE     ---       #110
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7188H   BLOCK     CODE     ---       LVL=0
      WR8         REGSYM    ---      WORD      Motor
      WR4         REGSYM    ---      INT       Spd
      R10         REGSYM    ---      BYTE      Acc
      00FF7188H   LINE      CODE     ---       #114
      00FF718AH   LINE      CODE     ---       #116
      00FF7190H   LINE      CODE     ---       #118
      00FF7194H   LINE      CODE     ---       #120
      00FF719CH   LINE      CODE     ---       #121
      00FF71A4H   LINE      CODE     ---       #122
      00FF71A8H   LINE      CODE     ---       #124
      ---         BLOCKEND  ---      ---       LVL=0

      00FF71EDH   BLOCK     CODE     ---       LVL=0
      R10         REGSYM    ---      BYTE      Motor
      WR6         REGSYM    ---      INT       PWM_dat
      00FF71EDH   LINE      CODE     ---       #127
      00FF71EFH   LINE      CODE     ---       #129
      00FF71F5H   LINE      CODE     ---       #131
      00FF71F9H   LINE      CODE     ---       #133
      00FF7201H   LINE      CODE     ---       #134
      00FF7209H   LINE      CODE     ---       #136
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ADC
      00FF6362H   PUBLIC    CODE     ---       ADC_init
      00FF6C96H   PUBLIC    CODE     ---       ADC_get
      00000094H   SFRSYM    DATA     BYTE      P0M0
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000BEH   SFRSYM    DATA     BYTE      ADC_RESL
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000BCH.5 SFRSYM    DATA     BIT       ADC_FLAG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 64


      000000BCH.6 SFRSYM    DATA     BIT       ADC_START
      000000BCH.7 SFRSYM    DATA     BIT       ADC_POWER
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000BDH   SFRSYM    DATA     BYTE      ADC_RES
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0
      00000091H   SFRSYM    DATA     BYTE      P1M1
      00000092H   SFRSYM    DATA     BYTE      P1M0
      00000093H   SFRSYM    DATA     BYTE      P0M1

      00FF6362H   BLOCK     CODE     ---       LVL=0
      WR2         REGSYM    ---      INT       adcn
      WR0         REGSYM    ---      INT       speed
      00FF6362H   LINE      CODE     ---       #6
      00FF6366H   LINE      CODE     ---       #8
      00FF6369H   LINE      CODE     ---       #10
      00FF636CH   LINE      CODE     ---       #11
      00FF6372H   LINE      CODE     ---       #12
      00FF637FH   LINE      CODE     ---       #15
      00FF638DH   LINE      CODE     ---       #18
      00FF63A2H   LINE      CODE     ---       #19
      00FF63A6H   LINE      CODE     ---       #20
      00FF63A8H   LINE      CODE     ---       #21
      00FF63ACH   LINE      CODE     ---       #24
      00FF63C1H   LINE      CODE     ---       #25
      00FF63C5H   LINE      CODE     ---       #26
      00FF63C5H   LINE      CODE     ---       #28
      00FF63CBH   LINE      CODE     ---       #30
      00FF63CEH   LINE      CODE     ---       #31
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6C96H   BLOCK     CODE     ---       LVL=0
      WR6         REGSYM    ---      INT       adcn
      WR6         REGSYM    ---      WORD      adc_value
      00FF6C96H   LINE      CODE     ---       #33
      00FF6C96H   LINE      CODE     ---       #34
      00FF6C96H   LINE      CODE     ---       #37
      00FF6C99H   LINE      CODE     ---       #38
      00FF6C9DH   LINE      CODE     ---       #40
      00FF6CA0H   LINE      CODE     ---       #41
      00FF6CA4H   LINE      CODE     ---       #42
      00FF6CA8H   LINE      CODE     ---       #43
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 65


      00FF6CABH   LINE      CODE     ---       #45
      00FF6CADH   LINE      CODE     ---       #46
      00FF6CB1H   LINE      CODE     ---       #47
      00FF6CB7H   LINE      CODE     ---       #49
      00FF6CBAH   LINE      CODE     ---       #50
      00FF6CBDH   LINE      CODE     ---       #54
      00FF6CDAH   LINE      CODE     ---       #56
      00FF6CDAH   LINE      CODE     ---       #57
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       Delay
      00FF7C04H   PUBLIC    CODE     ---       Delay_X_mS
      00FF7C1BH   PUBLIC    CODE     ---       Delay_X_uS
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF7C04H   BLOCK     CODE     ---       LVL=0
      WR4         REGSYM    ---      WORD      ms
      00FF7C06H   BLOCK     CODE     NEAR LAB  LVL=1
      WR6         REGSYM    ---      WORD      i
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7C04H   LINE      CODE     ---       #3
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 66


      00FF7C06H   LINE      CODE     ---       #4
      00FF7C06H   LINE      CODE     ---       #6
      00FF7C06H   LINE      CODE     ---       #7
      00FF7C0AH   LINE      CODE     ---       #8
      00FF7C12H   LINE      CODE     ---       #9
      00FF7C1AH   LINE      CODE     ---       #10
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7C1BH   BLOCK     CODE     ---       LVL=0
      WR4         REGSYM    ---      WORD      us
      00FF7C1DH   BLOCK     CODE     NEAR LAB  LVL=1
      WR6         REGSYM    ---      WORD      i
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7C1BH   LINE      CODE     ---       #11
      00FF7C1DH   LINE      CODE     ---       #12
      00FF7C1DH   LINE      CODE     ---       #14
      00FF7C1DH   LINE      CODE     ---       #15
      00FF7C21H   LINE      CODE     ---       #16
      00FF7C29H   LINE      CODE     ---       #17
      00FF7C31H   LINE      CODE     ---       #18
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       GPIO
      00FF3718H   PUBLIC    CODE     ---       Get_IO
      00FF434CH   PUBLIC    CODE     ---       GPIO_init_8pin
      00FF65E3H   PUBLIC    CODE     ---       GPIO_init_allpin
      00FF3BC9H   PUBLIC    CODE     ---       Out_IO
      00FF4C56H   PUBLIC    CODE     ---       GPIO_isr_deinit
      00FF340AH   PUBLIC    CODE     ---       GPIO_init_pin
      00FF2739H   PUBLIC    CODE     ---       GPIO_isr_init
      00FF41A5H   PUBLIC    CODE     ---       GPIO_pull_pin
      00000021H.3 PUBLIC    BIT      BIT       ?Out_IO?BIT
      00000094H   SFRSYM    DATA     BYTE      P0M0
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      000000F8H.7 SFRSYM    DATA     BIT       P77
      000000E8H.7 SFRSYM    DATA     BIT       P67
      000000F8H.6 SFRSYM    DATA     BIT       P76
      000000E8H.6 SFRSYM    DATA     BIT       P66
      000000F8H.5 SFRSYM    DATA     BIT       P75
      000000E8H.5 SFRSYM    DATA     BIT       P65
      000000F8H.4 SFRSYM    DATA     BIT       P74
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000C8H.5 SFRSYM    DATA     BIT       P55
      000000E8H.4 SFRSYM    DATA     BIT       P64
      000000F8H.3 SFRSYM    DATA     BIT       P73
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000B0H.6 SFRSYM    DATA     BIT       P36
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 67


      000000C8H.4 SFRSYM    DATA     BIT       P54
      000000E8H.3 SFRSYM    DATA     BIT       P63
      000000F8H.2 SFRSYM    DATA     BIT       P72
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000A0H.6 SFRSYM    DATA     BIT       P26
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000C0H.4 SFRSYM    DATA     BIT       P44
      000000C8H.3 SFRSYM    DATA     BIT       P53
      000000E8H.2 SFRSYM    DATA     BIT       P62
      000000F8H.1 SFRSYM    DATA     BIT       P71
      00000080H.7 SFRSYM    DATA     BIT       P07
      00000090H.6 SFRSYM    DATA     BIT       P16
      000000A0H.5 SFRSYM    DATA     BIT       P25
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000C0H.3 SFRSYM    DATA     BIT       P43
      000000C8H.2 SFRSYM    DATA     BIT       P52
      000000E8H.1 SFRSYM    DATA     BIT       P61
      000000F8H.0 SFRSYM    DATA     BIT       P70
      00000080H.6 SFRSYM    DATA     BIT       P06
      00000090H.5 SFRSYM    DATA     BIT       P15
      000000A0H.4 SFRSYM    DATA     BIT       P24
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000C0H.2 SFRSYM    DATA     BIT       P42
      000000C8H.1 SFRSYM    DATA     BIT       P51
      000000E8H.0 SFRSYM    DATA     BIT       P60
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000090H.4 SFRSYM    DATA     BIT       P14
      000000A0H.3 SFRSYM    DATA     BIT       P23
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000C0H.1 SFRSYM    DATA     BIT       P41
      000000C8H.0 SFRSYM    DATA     BIT       P50
      00000080H.4 SFRSYM    DATA     BIT       P04
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000A0H.2 SFRSYM    DATA     BIT       P22
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000C0H.0 SFRSYM    DATA     BIT       P40
      00000080H.3 SFRSYM    DATA     BIT       P03
      00000090H.2 SFRSYM    DATA     BIT       P12
      000000A0H.1 SFRSYM    DATA     BIT       P21
      000000B0H.0 SFRSYM    DATA     BIT       P30
      00000080H.2 SFRSYM    DATA     BIT       P02
      00000090H.1 SFRSYM    DATA     BIT       P11
      000000A0H.0 SFRSYM    DATA     BIT       P20
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000080H.1 SFRSYM    DATA     BIT       P01
      00000087H   SFRSYM    DATA     BYTE      PCON
      00000090H.0 SFRSYM    DATA     BIT       P10
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      00000080H.0 SFRSYM    DATA     BIT       P00
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 68


      000000E1H   SFRSYM    DATA     BYTE      P7M1
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000CBH   SFRSYM    DATA     BYTE      P6M1
      000000E2H   SFRSYM    DATA     BYTE      P7M0
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000C9H   SFRSYM    DATA     BYTE      P5M1
      000000CCH   SFRSYM    DATA     BYTE      P6M0
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000B3H   SFRSYM    DATA     BYTE      P4M1
      000000CAH   SFRSYM    DATA     BYTE      P5M0
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B1H   SFRSYM    DATA     BYTE      P3M1
      000000B4H   SFRSYM    DATA     BYTE      P4M0
      00000090H   SFRSYM    DATA     BYTE      P1
      00000095H   SFRSYM    DATA     BYTE      P2M1
      000000B2H   SFRSYM    DATA     BYTE      P3M0
      00000080H   SFRSYM    DATA     BYTE      P0
      00000091H   SFRSYM    DATA     BYTE      P1M1
      00000096H   SFRSYM    DATA     BYTE      P2M0
      00000092H   SFRSYM    DATA     BYTE      P1M0
      00000093H   SFRSYM    DATA     BYTE      P0M1
      00FF80B8H   SYMBOL    HCONST   ---       ?tpl?0001
      00FF80C0H   SYMBOL    HCONST   ---       ?tpl?0002
      00FF80C8H   SYMBOL    HCONST   ---       ?tpl?0003

      00FF340AH   BLOCK     CODE     ---       LVL=0
      R14         REGSYM    ---      BYTE      pin
      R15         REGSYM    ---      BYTE      mode
      00FF3410H   BLOCK     CODE     NEAR LAB  LVL=1
      R10         REGSYM    ---      BYTE      a
      R7          REGSYM    ---      BYTE      b
      000002A2H   SYMBOL    EDATA    ---       c
      ---         BLOCKEND  ---      ---       LVL=1
      00FF340AH   LINE      CODE     ---       #2
      00FF3410H   LINE      CODE     ---       #3
      00FF3410H   LINE      CODE     ---       #5
      00FF342FH   LINE      CODE     ---       #6
      00FF3439H   LINE      CODE     ---       #7
      00FF3444H   LINE      CODE     ---       #8
      00FF346EH   LINE      CODE     ---       #10
      00FF346EH   LINE      CODE     ---       #11
      00FF3480H   LINE      CODE     ---       #13
      00FF348CH   LINE      CODE     ---       #14
      00FF349FH   LINE      CODE     ---       #15
      00FF34B0H   LINE      CODE     ---       #16
      00FF34C1H   LINE      CODE     ---       #17
      00FF34C1H   LINE      CODE     ---       #18
      00FF34C4H   LINE      CODE     ---       #19
      00FF34C4H   LINE      CODE     ---       #20
      00FF34D6H   LINE      CODE     ---       #22
      00FF34E2H   LINE      CODE     ---       #23
      00FF34F5H   LINE      CODE     ---       #24
      00FF3506H   LINE      CODE     ---       #25
      00FF3517H   LINE      CODE     ---       #26
      00FF3517H   LINE      CODE     ---       #27
      00FF351AH   LINE      CODE     ---       #28
      00FF351AH   LINE      CODE     ---       #29
      00FF352CH   LINE      CODE     ---       #31
      00FF3538H   LINE      CODE     ---       #32
      00FF354BH   LINE      CODE     ---       #33
      00FF355CH   LINE      CODE     ---       #34
      00FF356DH   LINE      CODE     ---       #35
      00FF356DH   LINE      CODE     ---       #36
      00FF3570H   LINE      CODE     ---       #37
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 69


      00FF3570H   LINE      CODE     ---       #38
      00FF3582H   LINE      CODE     ---       #40
      00FF358EH   LINE      CODE     ---       #41
      00FF35A1H   LINE      CODE     ---       #42
      00FF35B2H   LINE      CODE     ---       #43
      00FF35C3H   LINE      CODE     ---       #44
      00FF35C3H   LINE      CODE     ---       #45
      00FF35C6H   LINE      CODE     ---       #46
      00FF35C6H   LINE      CODE     ---       #47
      00FF35D8H   LINE      CODE     ---       #49
      00FF35E4H   LINE      CODE     ---       #50
      00FF35F7H   LINE      CODE     ---       #51
      00FF3608H   LINE      CODE     ---       #52
      00FF3619H   LINE      CODE     ---       #53
      00FF3619H   LINE      CODE     ---       #54
      00FF361CH   LINE      CODE     ---       #55
      00FF361CH   LINE      CODE     ---       #56
      00FF362EH   LINE      CODE     ---       #58
      00FF363AH   LINE      CODE     ---       #59
      00FF364DH   LINE      CODE     ---       #60
      00FF365EH   LINE      CODE     ---       #61
      00FF366FH   LINE      CODE     ---       #62
      00FF366FH   LINE      CODE     ---       #63
      00FF3672H   LINE      CODE     ---       #64
      00FF3672H   LINE      CODE     ---       #65
      00FF3684H   LINE      CODE     ---       #67
      00FF3690H   LINE      CODE     ---       #68
      00FF36A2H   LINE      CODE     ---       #69
      00FF36B2H   LINE      CODE     ---       #70
      00FF36C3H   LINE      CODE     ---       #71
      00FF36C3H   LINE      CODE     ---       #72
      00FF36C5H   LINE      CODE     ---       #73
      00FF36C5H   LINE      CODE     ---       #74
      00FF36D6H   LINE      CODE     ---       #76
      00FF36E2H   LINE      CODE     ---       #77
      00FF36F4H   LINE      CODE     ---       #78
      00FF3704H   LINE      CODE     ---       #79
      00FF3715H   LINE      CODE     ---       #80
      00FF3715H   LINE      CODE     ---       #81
      00FF3715H   LINE      CODE     ---       #82
      00FF3715H   LINE      CODE     ---       #83
      ---         BLOCKEND  ---      ---       LVL=0

      00FF434CH   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      pin
      R3          REGSYM    ---      BYTE      mode
      00FF434CH   LINE      CODE     ---       #86
      00FF434EH   LINE      CODE     ---       #88
      00FF4381H   LINE      CODE     ---       #90
      00FF4381H   LINE      CODE     ---       #92
      00FF4393H   LINE      CODE     ---       #94
      00FF4398H   LINE      CODE     ---       #95
      00FF439FH   LINE      CODE     ---       #96
      00FF43A4H   LINE      CODE     ---       #97
      00FF43AAH   LINE      CODE     ---       #98
      00FF43AAH   LINE      CODE     ---       #99
      00FF43ABH   LINE      CODE     ---       #100
      00FF43ABH   LINE      CODE     ---       #102
      00FF43BDH   LINE      CODE     ---       #104
      00FF43C2H   LINE      CODE     ---       #105
      00FF43C9H   LINE      CODE     ---       #106
      00FF43CEH   LINE      CODE     ---       #107
      00FF43D4H   LINE      CODE     ---       #108
      00FF43D4H   LINE      CODE     ---       #109
      00FF43D5H   LINE      CODE     ---       #110
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 70


      00FF43D5H   LINE      CODE     ---       #112
      00FF43E7H   LINE      CODE     ---       #114
      00FF43ECH   LINE      CODE     ---       #115
      00FF43F3H   LINE      CODE     ---       #116
      00FF43F8H   LINE      CODE     ---       #117
      00FF43FEH   LINE      CODE     ---       #118
      00FF43FEH   LINE      CODE     ---       #119
      00FF43FFH   LINE      CODE     ---       #120
      00FF43FFH   LINE      CODE     ---       #122
      00FF4411H   LINE      CODE     ---       #124
      00FF4416H   LINE      CODE     ---       #125
      00FF441DH   LINE      CODE     ---       #126
      00FF4422H   LINE      CODE     ---       #127
      00FF4428H   LINE      CODE     ---       #128
      00FF4428H   LINE      CODE     ---       #129
      00FF4429H   LINE      CODE     ---       #130
      00FF4429H   LINE      CODE     ---       #132
      00FF443BH   LINE      CODE     ---       #134
      00FF4440H   LINE      CODE     ---       #135
      00FF4447H   LINE      CODE     ---       #136
      00FF444CH   LINE      CODE     ---       #137
      00FF4452H   LINE      CODE     ---       #138
      00FF4452H   LINE      CODE     ---       #139
      00FF4453H   LINE      CODE     ---       #140
      00FF4453H   LINE      CODE     ---       #142
      00FF4462H   LINE      CODE     ---       #144
      00FF4467H   LINE      CODE     ---       #145
      00FF446EH   LINE      CODE     ---       #146
      00FF4473H   LINE      CODE     ---       #147
      00FF4479H   LINE      CODE     ---       #148
      00FF4479H   LINE      CODE     ---       #149
      00FF447AH   LINE      CODE     ---       #150
      00FF447AH   LINE      CODE     ---       #152
      00FF4489H   LINE      CODE     ---       #154
      00FF448EH   LINE      CODE     ---       #155
      00FF4495H   LINE      CODE     ---       #156
      00FF449AH   LINE      CODE     ---       #157
      00FF44A0H   LINE      CODE     ---       #158
      00FF44A0H   LINE      CODE     ---       #159
      00FF44A1H   LINE      CODE     ---       #160
      00FF44A1H   LINE      CODE     ---       #162
      00FF44B2H   LINE      CODE     ---       #164
      00FF44B7H   LINE      CODE     ---       #165
      00FF44BEH   LINE      CODE     ---       #166
      00FF44C3H   LINE      CODE     ---       #167
      00FF44C9H   LINE      CODE     ---       #168
      00FF44C9H   LINE      CODE     ---       #169
      00FF44C9H   LINE      CODE     ---       #170
      00FF44C9H   LINE      CODE     ---       #171
      ---         BLOCKEND  ---      ---       LVL=0

      00FF65E3H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      mode
      00FF65E9H   BLOCK     CODE     NEAR LAB  LVL=1
      R13         REGSYM    ---      BYTE      a
      R14         REGSYM    ---      BYTE      b
      ---         BLOCKEND  ---      ---       LVL=1
      00FF65E3H   LINE      CODE     ---       #173
      00FF65E9H   LINE      CODE     ---       #174
      00FF65E9H   LINE      CODE     ---       #176
      00FF65ECH   LINE      CODE     ---       #177
      00FF65FDH   LINE      CODE     ---       #179
      00FF65FDH   LINE      CODE     ---       #181
      00FF65FFH   LINE      CODE     ---       #182
      00FF6601H   LINE      CODE     ---       #183
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 71


      00FF6603H   LINE      CODE     ---       #184
      00FF6603H   LINE      CODE     ---       #186
      00FF6606H   LINE      CODE     ---       #187
      00FF6608H   LINE      CODE     ---       #188
      00FF660AH   LINE      CODE     ---       #189
      00FF660AH   LINE      CODE     ---       #191
      00FF660CH   LINE      CODE     ---       #192
      00FF660CH   LINE      CODE     ---       #193
      00FF660EH   LINE      CODE     ---       #194
      00FF660EH   LINE      CODE     ---       #196
      00FF6611H   LINE      CODE     ---       #197
      00FF6614H   LINE      CODE     ---       #198
      00FF6614H   LINE      CODE     ---       #199
      00FF6614H   LINE      CODE     ---       #200
      00FF6617H   LINE      CODE     ---       #201
      00FF661AH   LINE      CODE     ---       #202
      00FF661DH   LINE      CODE     ---       #203
      00FF6620H   LINE      CODE     ---       #204
      00FF6623H   LINE      CODE     ---       #205
      00FF6626H   LINE      CODE     ---       #206
      00FF6629H   LINE      CODE     ---       #207
      00FF662CH   LINE      CODE     ---       #208
      00FF662FH   LINE      CODE     ---       #209
      00FF6632H   LINE      CODE     ---       #210
      00FF6635H   LINE      CODE     ---       #211
      00FF6638H   LINE      CODE     ---       #212
      00FF663BH   LINE      CODE     ---       #213
      00FF663EH   LINE      CODE     ---       #214
      00FF6641H   LINE      CODE     ---       #215
      00FF6644H   LINE      CODE     ---       #217
      ---         BLOCKEND  ---      ---       LVL=0

      00FF41A5H   BLOCK     CODE     ---       LVL=0
      R14         REGSYM    ---      BYTE      pin
      R15         REGSYM    ---      BYTE      mode
      00FF41ABH   BLOCK     CODE     NEAR LAB  LVL=1
      R10         REGSYM    ---      BYTE      a
      R7          REGSYM    ---      BYTE      b
      000002AAH   SYMBOL    EDATA    ---       c
      ---         BLOCKEND  ---      ---       LVL=1
      00FF41A5H   LINE      CODE     ---       #219
      00FF41ABH   LINE      CODE     ---       #220
      00FF41ABH   LINE      CODE     ---       #222
      00FF41CAH   LINE      CODE     ---       #223
      00FF41D4H   LINE      CODE     ---       #224
      00FF41DFH   LINE      CODE     ---       #226
      00FF41E2H   LINE      CODE     ---       #227
      00FF420CH   LINE      CODE     ---       #229
      00FF420CH   LINE      CODE     ---       #230
      00FF4217H   LINE      CODE     ---       #232
      00FF4224H   LINE      CODE     ---       #233
      00FF4230H   LINE      CODE     ---       #234
      00FF4230H   LINE      CODE     ---       #235
      00FF4233H   LINE      CODE     ---       #236
      00FF4233H   LINE      CODE     ---       #237
      00FF423EH   LINE      CODE     ---       #239
      00FF424BH   LINE      CODE     ---       #240
      00FF4257H   LINE      CODE     ---       #241
      00FF4257H   LINE      CODE     ---       #242
      00FF425AH   LINE      CODE     ---       #243
      00FF425AH   LINE      CODE     ---       #244
      00FF4265H   LINE      CODE     ---       #246
      00FF4272H   LINE      CODE     ---       #247
      00FF427EH   LINE      CODE     ---       #248
      00FF427EH   LINE      CODE     ---       #249
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 72


      00FF4281H   LINE      CODE     ---       #250
      00FF4281H   LINE      CODE     ---       #251
      00FF428CH   LINE      CODE     ---       #253
      00FF4299H   LINE      CODE     ---       #254
      00FF42A5H   LINE      CODE     ---       #255
      00FF42A5H   LINE      CODE     ---       #256
      00FF42A8H   LINE      CODE     ---       #257
      00FF42A8H   LINE      CODE     ---       #258
      00FF42B3H   LINE      CODE     ---       #260
      00FF42BFH   LINE      CODE     ---       #261
      00FF42CBH   LINE      CODE     ---       #262
      00FF42CBH   LINE      CODE     ---       #263
      00FF42CDH   LINE      CODE     ---       #264
      00FF42CDH   LINE      CODE     ---       #265
      00FF42D5H   LINE      CODE     ---       #267
      00FF42E1H   LINE      CODE     ---       #268
      00FF42EDH   LINE      CODE     ---       #269
      00FF42EDH   LINE      CODE     ---       #270
      00FF42EFH   LINE      CODE     ---       #271
      00FF42EFH   LINE      CODE     ---       #272
      00FF42F7H   LINE      CODE     ---       #274
      00FF4303H   LINE      CODE     ---       #275
      00FF430FH   LINE      CODE     ---       #276
      00FF430FH   LINE      CODE     ---       #277
      00FF4311H   LINE      CODE     ---       #278
      00FF4311H   LINE      CODE     ---       #279
      00FF4319H   LINE      CODE     ---       #281
      00FF432EH   LINE      CODE     ---       #282
      00FF4346H   LINE      CODE     ---       #283
      00FF4346H   LINE      CODE     ---       #284
      00FF4346H   LINE      CODE     ---       #285
      00FF4346H   LINE      CODE     ---       #286
      00FF4349H   LINE      CODE     ---       #287
      ---         BLOCKEND  ---      ---       LVL=0

      00FF2739H   BLOCK     CODE     ---       LVL=0
      R14         REGSYM    ---      BYTE      pin
      R15         REGSYM    ---      BYTE      mode
      00FF273FH   BLOCK     CODE     NEAR LAB  LVL=1
      R7          REGSYM    ---      BYTE      psw2_old
      R10         REGSYM    ---      BYTE      a
      R6          REGSYM    ---      BYTE      b
      000002B2H   SYMBOL    EDATA    ---       c
      ---         BLOCKEND  ---      ---       LVL=1
      00FF2739H   LINE      CODE     ---       #290
      00FF273FH   LINE      CODE     ---       #291
      00FF273FH   LINE      CODE     ---       #294
      00FF275EH   LINE      CODE     ---       #295
      00FF2768H   LINE      CODE     ---       #296
      00FF2773H   LINE      CODE     ---       #299
      00FF2776H   LINE      CODE     ---       #300
      00FF2779H   LINE      CODE     ---       #302
      00FF27A3H   LINE      CODE     ---       #304
      00FF27A3H   LINE      CODE     ---       #305
      00FF27B5H   LINE      CODE     ---       #307
      00FF27CAH   LINE      CODE     ---       #308
      00FF27F3H   LINE      CODE     ---       #309
      00FF2816H   LINE      CODE     ---       #310
      00FF283AH   LINE      CODE     ---       #311
      00FF283AH   LINE      CODE     ---       #312
      00FF284BH   LINE      CODE     ---       #313
      00FF284EH   LINE      CODE     ---       #314
      00FF284EH   LINE      CODE     ---       #315
      00FF2860H   LINE      CODE     ---       #317
      00FF2875H   LINE      CODE     ---       #318
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 73


      00FF289EH   LINE      CODE     ---       #319
      00FF28C1H   LINE      CODE     ---       #320
      00FF28E5H   LINE      CODE     ---       #321
      00FF28E5H   LINE      CODE     ---       #322
      00FF28F6H   LINE      CODE     ---       #323
      00FF28F9H   LINE      CODE     ---       #324
      00FF28F9H   LINE      CODE     ---       #325
      00FF290BH   LINE      CODE     ---       #327
      00FF2920H   LINE      CODE     ---       #328
      00FF2949H   LINE      CODE     ---       #329
      00FF296CH   LINE      CODE     ---       #330
      00FF2990H   LINE      CODE     ---       #331
      00FF2990H   LINE      CODE     ---       #332
      00FF29A1H   LINE      CODE     ---       #333
      00FF29A4H   LINE      CODE     ---       #334
      00FF29A4H   LINE      CODE     ---       #335
      00FF29B6H   LINE      CODE     ---       #337
      00FF29CBH   LINE      CODE     ---       #338
      00FF29F4H   LINE      CODE     ---       #339
      00FF2A17H   LINE      CODE     ---       #340
      00FF2A3BH   LINE      CODE     ---       #341
      00FF2A3BH   LINE      CODE     ---       #342
      00FF2A4CH   LINE      CODE     ---       #343
      00FF2A4FH   LINE      CODE     ---       #344
      00FF2A4FH   LINE      CODE     ---       #345
      00FF2A61H   LINE      CODE     ---       #347
      00FF2A76H   LINE      CODE     ---       #348
      00FF2A9FH   LINE      CODE     ---       #349
      00FF2AC2H   LINE      CODE     ---       #350
      00FF2AE6H   LINE      CODE     ---       #351
      00FF2AE6H   LINE      CODE     ---       #352
      00FF2AF7H   LINE      CODE     ---       #353
      00FF2AFAH   LINE      CODE     ---       #354
      00FF2AFAH   LINE      CODE     ---       #355
      00FF2B0CH   LINE      CODE     ---       #357
      00FF2B21H   LINE      CODE     ---       #358
      00FF2B4AH   LINE      CODE     ---       #359
      00FF2B6DH   LINE      CODE     ---       #360
      00FF2B91H   LINE      CODE     ---       #361
      00FF2B91H   LINE      CODE     ---       #362
      00FF2BA2H   LINE      CODE     ---       #363
      00FF2BA5H   LINE      CODE     ---       #364
      00FF2BA5H   LINE      CODE     ---       #365
      00FF2BB7H   LINE      CODE     ---       #367
      00FF2BCCH   LINE      CODE     ---       #368
      00FF2BF5H   LINE      CODE     ---       #369
      00FF2C18H   LINE      CODE     ---       #370
      00FF2C3CH   LINE      CODE     ---       #371
      00FF2C3CH   LINE      CODE     ---       #372
      00FF2C4DH   LINE      CODE     ---       #373
      00FF2C50H   LINE      CODE     ---       #374
      00FF2C50H   LINE      CODE     ---       #375
      00FF2C64H   LINE      CODE     ---       #377
      00FF2C79H   LINE      CODE     ---       #378
      00FF2CA2H   LINE      CODE     ---       #379
      00FF2CC5H   LINE      CODE     ---       #380
      00FF2CE9H   LINE      CODE     ---       #381
      00FF2CE9H   LINE      CODE     ---       #382
      00FF2D06H   LINE      CODE     ---       #383
      00FF2D06H   LINE      CODE     ---       #385
      00FF2D06H   LINE      CODE     ---       #387
      00FF2D09H   LINE      CODE     ---       #389
      ---         BLOCKEND  ---      ---       LVL=0

      00FF4C56H   BLOCK     CODE     ---       LVL=0
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 74


      R11         REGSYM    ---      BYTE      pin
      R7          REGSYM    ---      BYTE      psw2_old
      R2          REGSYM    ---      BYTE      a
      R6          REGSYM    ---      BYTE      b
      00FF4C56H   LINE      CODE     ---       #391
      00FF4C56H   LINE      CODE     ---       #392
      00FF4C56H   LINE      CODE     ---       #396
      00FF4C60H   LINE      CODE     ---       #397
      00FF4C6BH   LINE      CODE     ---       #400
      00FF4C6EH   LINE      CODE     ---       #401
      00FF4C71H   LINE      CODE     ---       #403
      00FF4C9BH   LINE      CODE     ---       #405
      00FF4CB3H   LINE      CODE     ---       #406
      00FF4CCBH   LINE      CODE     ---       #407
      00FF4CE2H   LINE      CODE     ---       #408
      00FF4CF9H   LINE      CODE     ---       #409
      00FF4D10H   LINE      CODE     ---       #410
      00FF4D27H   LINE      CODE     ---       #411
      00FF4D3EH   LINE      CODE     ---       #412
      00FF4D5FH   LINE      CODE     ---       #413
      00FF4D5FH   LINE      CODE     ---       #414
      00FF4D62H   LINE      CODE     ---       #415
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3718H   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      IO
      00000021H.2 SYMBOL    BIT      BIT       status
      00FF3718H   LINE      CODE     ---       #417
      00FF3718H   LINE      CODE     ---       #418
      00FF3718H   LINE      CODE     ---       #420
      00FF3812H   LINE      CODE     ---       #422
      00FF3819H   LINE      CODE     ---       #423
      00FF3820H   LINE      CODE     ---       #424
      00FF3827H   LINE      CODE     ---       #425
      00FF382EH   LINE      CODE     ---       #426
      00FF3835H   LINE      CODE     ---       #427
      00FF383CH   LINE      CODE     ---       #428
      00FF3843H   LINE      CODE     ---       #429
      00FF384AH   LINE      CODE     ---       #431
      00FF3851H   LINE      CODE     ---       #432
      00FF3858H   LINE      CODE     ---       #433
      00FF385FH   LINE      CODE     ---       #434
      00FF3866H   LINE      CODE     ---       #435
      00FF386DH   LINE      CODE     ---       #436
      00FF3874H   LINE      CODE     ---       #437
      00FF387BH   LINE      CODE     ---       #438
      00FF3882H   LINE      CODE     ---       #440
      00FF3889H   LINE      CODE     ---       #441
      00FF3890H   LINE      CODE     ---       #442
      00FF3897H   LINE      CODE     ---       #443
      00FF389EH   LINE      CODE     ---       #444
      00FF38A5H   LINE      CODE     ---       #445
      00FF38ACH   LINE      CODE     ---       #446
      00FF38B3H   LINE      CODE     ---       #447
      00FF38BAH   LINE      CODE     ---       #449
      00FF38C1H   LINE      CODE     ---       #450
      00FF38C8H   LINE      CODE     ---       #451
      00FF38CFH   LINE      CODE     ---       #452
      00FF38D6H   LINE      CODE     ---       #453
      00FF38DDH   LINE      CODE     ---       #454
      00FF38E4H   LINE      CODE     ---       #455
      00FF38EBH   LINE      CODE     ---       #456
      00FF38F2H   LINE      CODE     ---       #458
      00FF38F9H   LINE      CODE     ---       #459
      00FF3900H   LINE      CODE     ---       #460
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 75


      00FF3907H   LINE      CODE     ---       #461
      00FF390EH   LINE      CODE     ---       #462
      00FF3915H   LINE      CODE     ---       #464
      00FF391CH   LINE      CODE     ---       #465
      00FF3922H   LINE      CODE     ---       #466
      00FF3928H   LINE      CODE     ---       #467
      00FF392EH   LINE      CODE     ---       #468
      00FF3934H   LINE      CODE     ---       #469
      00FF393AH   LINE      CODE     ---       #471
      00FF3940H   LINE      CODE     ---       #472
      00FF3946H   LINE      CODE     ---       #473
      00FF394CH   LINE      CODE     ---       #474
      00FF3952H   LINE      CODE     ---       #475
      00FF3958H   LINE      CODE     ---       #476
      00FF395EH   LINE      CODE     ---       #477
      00FF3964H   LINE      CODE     ---       #478
      00FF396AH   LINE      CODE     ---       #480
      00FF3970H   LINE      CODE     ---       #481
      00FF3976H   LINE      CODE     ---       #482
      00FF397CH   LINE      CODE     ---       #483
      00FF3982H   LINE      CODE     ---       #484
      00FF3988H   LINE      CODE     ---       #485
      00FF398EH   LINE      CODE     ---       #486
      00FF3994H   LINE      CODE     ---       #487
      00FF399AH   LINE      CODE     ---       #488
      00FF399CH   LINE      CODE     ---       #489
      00FF399CH   LINE      CODE     ---       #490
      00FF399EH   LINE      CODE     ---       #491
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3BC9H   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      IO
      00000021H.3 SYMBOL    BIT      BIT       status
      00FF3BC9H   LINE      CODE     ---       #492
      00FF3BC9H   LINE      CODE     ---       #494
      00FF3CC3H   LINE      CODE     ---       #496
      00FF3CC8H   LINE      CODE     ---       #497
      00FF3CCDH   LINE      CODE     ---       #498
      00FF3CD2H   LINE      CODE     ---       #499
      00FF3CD7H   LINE      CODE     ---       #500
      00FF3CDCH   LINE      CODE     ---       #501
      00FF3CE1H   LINE      CODE     ---       #502
      00FF3CE6H   LINE      CODE     ---       #503
      00FF3CEBH   LINE      CODE     ---       #505
      00FF3CF0H   LINE      CODE     ---       #506
      00FF3CF5H   LINE      CODE     ---       #507
      00FF3CFAH   LINE      CODE     ---       #508
      00FF3CFFH   LINE      CODE     ---       #509
      00FF3D04H   LINE      CODE     ---       #510
      00FF3D09H   LINE      CODE     ---       #511
      00FF3D0EH   LINE      CODE     ---       #512
      00FF3D13H   LINE      CODE     ---       #514
      00FF3D18H   LINE      CODE     ---       #515
      00FF3D1DH   LINE      CODE     ---       #516
      00FF3D22H   LINE      CODE     ---       #517
      00FF3D27H   LINE      CODE     ---       #518
      00FF3D2CH   LINE      CODE     ---       #519
      00FF3D31H   LINE      CODE     ---       #520
      00FF3D36H   LINE      CODE     ---       #521
      00FF3D3BH   LINE      CODE     ---       #523
      00FF3D40H   LINE      CODE     ---       #524
      00FF3D45H   LINE      CODE     ---       #525
      00FF3D4AH   LINE      CODE     ---       #526
      00FF3D4FH   LINE      CODE     ---       #527
      00FF3D54H   LINE      CODE     ---       #528
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 76


      00FF3D59H   LINE      CODE     ---       #529
      00FF3D5EH   LINE      CODE     ---       #530
      00FF3D63H   LINE      CODE     ---       #532
      00FF3D68H   LINE      CODE     ---       #533
      00FF3D6DH   LINE      CODE     ---       #534
      00FF3D72H   LINE      CODE     ---       #535
      00FF3D77H   LINE      CODE     ---       #536
      00FF3D7CH   LINE      CODE     ---       #538
      00FF3D81H   LINE      CODE     ---       #539
      00FF3D86H   LINE      CODE     ---       #540
      00FF3D8BH   LINE      CODE     ---       #541
      00FF3D90H   LINE      CODE     ---       #542
      00FF3D95H   LINE      CODE     ---       #543
      00FF3D9AH   LINE      CODE     ---       #545
      00FF3D9FH   LINE      CODE     ---       #546
      00FF3DA4H   LINE      CODE     ---       #547
      00FF3DA9H   LINE      CODE     ---       #548
      00FF3DAEH   LINE      CODE     ---       #549
      00FF3DB3H   LINE      CODE     ---       #550
      00FF3DB8H   LINE      CODE     ---       #551
      00FF3DBDH   LINE      CODE     ---       #552
      00FF3DC2H   LINE      CODE     ---       #554
      00FF3DC7H   LINE      CODE     ---       #555
      00FF3DCCH   LINE      CODE     ---       #556
      00FF3DD1H   LINE      CODE     ---       #557
      00FF3DD6H   LINE      CODE     ---       #558
      00FF3DDBH   LINE      CODE     ---       #559
      00FF3DE0H   LINE      CODE     ---       #560
      00FF3DE5H   LINE      CODE     ---       #561
      00FF3DE9H   LINE      CODE     ---       #562
      00FF3DE9H   LINE      CODE     ---       #563
      00FF3DE9H   LINE      CODE     ---       #564
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       IIC
      00FF7B5DH   PUBLIC    CODE     ---       ESD_IIC_Send_nack
      00FF6764H   PUBLIC    CODE     ---       ESD_Write_IIC
      00FF7856H   PUBLIC    CODE     ---       ESD_IIC_Recv_data
      00FF7D66H   PUBLIC    CODE     ---       ESD_IIC_Start
      00FF7C32H   PUBLIC    CODE     ---       ESD_IIC_WRITE_START_BYTE
      00FF5754H   PUBLIC    CODE     ---       ESD_Init_IIC
      00FF7C49H   PUBLIC    CODE     ---       ESD_IIC_Wait
      00FF7BBEH   PUBLIC    CODE     ---       ESD_IIC_Send_ack
      00FF7D76H   PUBLIC    CODE     ---       ESD_IIC_Recv_ack
      00FF7D86H   PUBLIC    CODE     ---       ESD_IIC_Stop
      00FF7872H   PUBLIC    CODE     ---       ESD_IIC_READ_NACK_BYTE
      00FF788EH   PUBLIC    CODE     ---       ESD_IIC_READ_ACK_BYTE
      00FF7C60H   PUBLIC    CODE     ---       ESD_IIC_WRITE_ONE_BYTE
      00FF60A3H   PUBLIC    CODE     ---       ESD_Read_IIC
      00FF7C77H   PUBLIC    CODE     ---       ESD_IIC_Send_data
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 77


      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E1H   SFRSYM    DATA     BYTE      P7M1
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000E2H   SFRSYM    DATA     BYTE      P7M0
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B1H   SFRSYM    DATA     BYTE      P3M1
      00000090H   SFRSYM    DATA     BYTE      P1
      00000095H   SFRSYM    DATA     BYTE      P2M1
      000000B2H   SFRSYM    DATA     BYTE      P3M0
      00000080H   SFRSYM    DATA     BYTE      P0
      00000091H   SFRSYM    DATA     BYTE      P1M1
      00000096H   SFRSYM    DATA     BYTE      P2M0
      00000092H   SFRSYM    DATA     BYTE      P1M0

      00FF7C49H   BLOCK     CODE     ---       LVL=0
      00FF7C49H   LINE      CODE     ---       #9
      00FF7C49H   LINE      CODE     ---       #11
      00FF7C57H   LINE      CODE     ---       #12
      00FF7C5FH   LINE      CODE     ---       #13
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7D66H   BLOCK     CODE     ---       LVL=0
      00FF7D66H   LINE      CODE     ---       #18
      00FF7D66H   LINE      CODE     ---       #20
      00FF7D73H   LINE      CODE     ---       #21
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7C77H   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      dat
      00FF7C77H   LINE      CODE     ---       #26
      00FF7C77H   LINE      CODE     ---       #28
      00FF7C82H   LINE      CODE     ---       #29
      00FF7C8BH   LINE      CODE     ---       #30
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7D76H   BLOCK     CODE     ---       LVL=0
      00FF7D76H   LINE      CODE     ---       #35
      00FF7D76H   LINE      CODE     ---       #37
      00FF7D83H   LINE      CODE     ---       #38
      ---         BLOCKEND  ---      ---       LVL=0
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 78



      00FF7856H   BLOCK     CODE     ---       LVL=0
      00FF7856H   LINE      CODE     ---       #43
      00FF7856H   LINE      CODE     ---       #45
      00FF7863H   LINE      CODE     ---       #46
      00FF7866H   LINE      CODE     ---       #47
      00FF7871H   LINE      CODE     ---       #48
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7BBEH   BLOCK     CODE     ---       LVL=0
      00FF7BBEH   LINE      CODE     ---       #53
      00FF7BBEH   LINE      CODE     ---       #55
      00FF7BCAH   LINE      CODE     ---       #56
      00FF7BD3H   LINE      CODE     ---       #57
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7B5DH   BLOCK     CODE     ---       LVL=0
      00FF7B5DH   LINE      CODE     ---       #62
      00FF7B5DH   LINE      CODE     ---       #64
      00FF7B6AH   LINE      CODE     ---       #65
      00FF7B73H   LINE      CODE     ---       #66
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7D86H   BLOCK     CODE     ---       LVL=0
      00FF7D86H   LINE      CODE     ---       #72
      00FF7D86H   LINE      CODE     ---       #74
      00FF7D93H   LINE      CODE     ---       #75
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7C32H   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      dat
      00FF7C32H   LINE      CODE     ---       #81
      00FF7C32H   LINE      CODE     ---       #83
      00FF7C3DH   LINE      CODE     ---       #84
      00FF7C46H   LINE      CODE     ---       #85
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7C60H   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      dat
      00FF7C60H   LINE      CODE     ---       #91
      00FF7C60H   LINE      CODE     ---       #93
      00FF7C6BH   LINE      CODE     ---       #94
      00FF7C74H   LINE      CODE     ---       #95
      ---         BLOCKEND  ---      ---       LVL=0

      00FF788EH   BLOCK     CODE     ---       LVL=0
      00FF788EH   LINE      CODE     ---       #101
      00FF788EH   LINE      CODE     ---       #103
      00FF789BH   LINE      CODE     ---       #104
      00FF789EH   LINE      CODE     ---       #105
      00FF78A9H   LINE      CODE     ---       #106
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7872H   BLOCK     CODE     ---       LVL=0
      00FF7872H   LINE      CODE     ---       #111
      00FF7872H   LINE      CODE     ---       #113
      00FF787FH   LINE      CODE     ---       #114
      00FF7882H   LINE      CODE     ---       #115
      00FF788DH   LINE      CODE     ---       #116
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6764H   BLOCK     CODE     ---       LVL=0
      000002F4H   SYMBOL    EDATA    BYTE      dev
      000002F5H   SYMBOL    EDATA    BYTE      reg
      000002F6H   SYMBOL    EDATA    BYTE      length
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 79


      REG=3       REGSYM    ---      ---       dat
      00FF6774H   BLOCK     CODE     NEAR LAB  LVL=1
      000002F7H   SYMBOL    EDATA    BYTE      count
      ---         BLOCKEND  ---      ---       LVL=1
      00FF6764H   LINE      CODE     ---       #128
      00FF6774H   LINE      CODE     ---       #129
      00FF6774H   LINE      CODE     ---       #130
      00FF6774H   LINE      CODE     ---       #132
      00FF6777H   LINE      CODE     ---       #133
      00FF6780H   LINE      CODE     ---       #134
      00FF6783H   LINE      CODE     ---       #135
      00FF678AH   LINE      CODE     ---       #136
      00FF678DH   LINE      CODE     ---       #137
      00FF6790H   LINE      CODE     ---       #139
      00FF67A0H   LINE      CODE     ---       #140
      00FF67A3H   LINE      CODE     ---       #141
      00FF67B6H   LINE      CODE     ---       #142
      00FF67B9H   LINE      CODE     ---       #143
      ---         BLOCKEND  ---      ---       LVL=0

      00FF60A3H   BLOCK     CODE     ---       LVL=0
      000002F8H   SYMBOL    EDATA    BYTE      dev
      000002F9H   SYMBOL    EDATA    BYTE      reg
      000002FAH   SYMBOL    EDATA    BYTE      length
      REG=3       REGSYM    ---      ---       dat
      00FF60B3H   BLOCK     CODE     NEAR LAB  LVL=1
      000002FBH   SYMBOL    EDATA    BYTE      count
      ---         BLOCKEND  ---      ---       LVL=1
      00FF60A3H   LINE      CODE     ---       #153
      00FF60B3H   LINE      CODE     ---       #154
      00FF60B3H   LINE      CODE     ---       #155
      00FF60B3H   LINE      CODE     ---       #157
      00FF60B6H   LINE      CODE     ---       #158
      00FF60BFH   LINE      CODE     ---       #159
      00FF60C2H   LINE      CODE     ---       #160
      00FF60C9H   LINE      CODE     ---       #161
      00FF60CCH   LINE      CODE     ---       #163
      00FF60CFH   LINE      CODE     ---       #164
      00FF60DCH   LINE      CODE     ---       #165
      00FF60DFH   LINE      CODE     ---       #167
      00FF60E2H   LINE      CODE     ---       #169
      00FF60F2H   LINE      CODE     ---       #170
      00FF6109H   LINE      CODE     ---       #171
      00FF610CH   LINE      CODE     ---       #173
      00FF611FH   LINE      CODE     ---       #174
      00FF6122H   LINE      CODE     ---       #176
      ---         BLOCKEND  ---      ---       LVL=0

      00FF5754H   BLOCK     CODE     ---       LVL=0
      WR6         REGSYM    ---      INT       iic_n
      00FF5754H   LINE      CODE     ---       #180
      00FF5754H   LINE      CODE     ---       #183
      00FF5766H   LINE      CODE     ---       #185
      00FF5766H   LINE      CODE     ---       #188
      00FF5769H   LINE      CODE     ---       #189
      00FF5779H   LINE      CODE     ---       #190
      00FF577CH   LINE      CODE     ---       #191
      00FF577FH   LINE      CODE     ---       #193
      00FF5781H   LINE      CODE     ---       #194
      00FF5781H   LINE      CODE     ---       #197
      00FF5784H   LINE      CODE     ---       #198
      00FF5787H   LINE      CODE     ---       #199
      00FF5797H   LINE      CODE     ---       #200
      00FF579AH   LINE      CODE     ---       #201
      00FF579DH   LINE      CODE     ---       #202
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 80


      00FF579FH   LINE      CODE     ---       #203
      00FF579FH   LINE      CODE     ---       #206
      00FF57A2H   LINE      CODE     ---       #207
      00FF57A5H   LINE      CODE     ---       #208
      00FF57B5H   LINE      CODE     ---       #209
      00FF57B8H   LINE      CODE     ---       #210
      00FF57BBH   LINE      CODE     ---       #211
      00FF57BDH   LINE      CODE     ---       #212
      00FF57BDH   LINE      CODE     ---       #215
      00FF57C0H   LINE      CODE     ---       #216
      00FF57D0H   LINE      CODE     ---       #217
      00FF57D3H   LINE      CODE     ---       #218
      00FF57D6H   LINE      CODE     ---       #219
      00FF57D6H   LINE      CODE     ---       #220
      00FF57D6H   LINE      CODE     ---       #222
      00FF57E3H   LINE      CODE     ---       #223
      00FF57EBH   LINE      CODE     ---       #224
      00FF57F3H   LINE      CODE     ---       #225
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       INT
      00FF71BBH   PUBLIC    CODE     ---       INT_init
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.0 SFRSYM    DATA     BIT       IT0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 81


      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF71BBH   BLOCK     CODE     ---       LVL=0
      R6          REGSYM    ---      BYTE      int_n
      R7          REGSYM    ---      BYTE      mode
      00FF71BBH   LINE      CODE     ---       #2
      00FF71BDH   LINE      CODE     ---       #4
      00FF71C1H   LINE      CODE     ---       #6
      00FF71CAH   LINE      CODE     ---       #7
      00FF71CCH   LINE      CODE     ---       #8
      00FF71CCH   LINE      CODE     ---       #9
      00FF71D0H   LINE      CODE     ---       #11
      00FF71D5H   LINE      CODE     ---       #12
      00FF71D7H   LINE      CODE     ---       #13
      00FF71D7H   LINE      CODE     ---       #14
      00FF71DBH   LINE      CODE     ---       #16
      00FF71DEH   LINE      CODE     ---       #17
      00FF71DEH   LINE      CODE     ---       #18
      00FF71E2H   LINE      CODE     ---       #20
      00FF71E5H   LINE      CODE     ---       #21
      00FF71E5H   LINE      CODE     ---       #22
      00FF71E9H   LINE      CODE     ---       #24
      00FF71ECH   LINE      CODE     ---       #25
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       PIT
      00FF6A49H   PUBLIC    CODE     ---       PIT_count_get
      00FF5E86H   PUBLIC    CODE     ---       PIT_init_ms
      00FF6A96H   PUBLIC    CODE     ---       PIT_init_encoder
      00FF5DF3H   PUBLIC    CODE     ---       PIT_init_us
      00FF68B9H   PUBLIC    CODE     ---       PIT_count_clean
      0000026FH   PUBLIC    EDATA    WORD      T0_cnt
      00000271H   PUBLIC    EDATA    WORD      T1_cnt
      00000273H   PUBLIC    EDATA    WORD      T2_cnt
      00000275H   PUBLIC    EDATA    WORD      T3_cnt
      00000277H   PUBLIC    EDATA    WORD      T4_cnt
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      00000088H.6 SFRSYM    DATA     BIT       TR1
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D3H   SFRSYM    DATA     BYTE      T4L
      000000D5H   SFRSYM    DATA     BYTE      T3L
      000000D7H   SFRSYM    DATA     BYTE      T2L
      0000008BH   SFRSYM    DATA     BYTE      TL1
      0000008AH   SFRSYM    DATA     BYTE      TL0
      000000D2H   SFRSYM    DATA     BYTE      T4H
      000000D4H   SFRSYM    DATA     BYTE      T3H
      000000D6H   SFRSYM    DATA     BYTE      T2H
      0000008DH   SFRSYM    DATA     BYTE      TH1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000DFH   SFRSYM    DATA     BYTE      IP3
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 82


      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF5E86H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      tim_n
      WR2         REGSYM    ---      WORD      time_ms
      00FF5E8CH   BLOCK     CODE     NEAR LAB  LVL=1
      WR6         REGSYM    ---      WORD      temp
      ---         BLOCKEND  ---      ---       LVL=1
      00FF5E86H   LINE      CODE     ---       #10
      00FF5E8CH   LINE      CODE     ---       #11
      00FF5E8CH   LINE      CODE     ---       #13
      00FF5EA5H   LINE      CODE     ---       #14
      00FF5EA9H   LINE      CODE     ---       #16
      00FF5EADH   LINE      CODE     ---       #17
      00FF5EB1H   LINE      CODE     ---       #18
      00FF5EB5H   LINE      CODE     ---       #19
      00FF5EB7H   LINE      CODE     ---       #20
      00FF5EB9H   LINE      CODE     ---       #21
      00FF5EBBH   LINE      CODE     ---       #22
      00FF5EC0H   LINE      CODE     ---       #24
      00FF5EC4H   LINE      CODE     ---       #25
      00FF5EC8H   LINE      CODE     ---       #26
      00FF5ECCH   LINE      CODE     ---       #27
      00FF5ECEH   LINE      CODE     ---       #28
      00FF5ED0H   LINE      CODE     ---       #29
      00FF5ED2H   LINE      CODE     ---       #30
      00FF5ED7H   LINE      CODE     ---       #32
      00FF5EDBH   LINE      CODE     ---       #33
      00FF5EDFH   LINE      CODE     ---       #34
      00FF5EE2H   LINE      CODE     ---       #35
      00FF5EE5H   LINE      CODE     ---       #36
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 83


      00FF5EE7H   LINE      CODE     ---       #37
      00FF5EECH   LINE      CODE     ---       #39
      00FF5EF0H   LINE      CODE     ---       #40
      00FF5EF4H   LINE      CODE     ---       #41
      00FF5EF7H   LINE      CODE     ---       #42
      00FF5EFAH   LINE      CODE     ---       #43
      00FF5EFCH   LINE      CODE     ---       #44
      00FF5F01H   LINE      CODE     ---       #46
      00FF5F05H   LINE      CODE     ---       #47
      00FF5F09H   LINE      CODE     ---       #48
      00FF5F0CH   LINE      CODE     ---       #49
      00FF5F0FH   LINE      CODE     ---       #50
      ---         BLOCKEND  ---      ---       LVL=0

      00FF5DF3H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      tim_n
      WR30        REGSYM    ---      WORD      time_us
      00FF5DF9H   BLOCK     CODE     NEAR LAB  LVL=1
      WR6         REGSYM    ---      WORD      temp
      ---         BLOCKEND  ---      ---       LVL=1
      00FF5DF3H   LINE      CODE     ---       #52
      00FF5DF9H   LINE      CODE     ---       #53
      00FF5DF9H   LINE      CODE     ---       #55
      00FF5E19H   LINE      CODE     ---       #56
      00FF5E1DH   LINE      CODE     ---       #58
      00FF5E21H   LINE      CODE     ---       #59
      00FF5E25H   LINE      CODE     ---       #60
      00FF5E29H   LINE      CODE     ---       #61
      00FF5E2BH   LINE      CODE     ---       #62
      00FF5E2DH   LINE      CODE     ---       #63
      00FF5E2FH   LINE      CODE     ---       #64
      00FF5E34H   LINE      CODE     ---       #66
      00FF5E38H   LINE      CODE     ---       #67
      00FF5E3CH   LINE      CODE     ---       #68
      00FF5E40H   LINE      CODE     ---       #69
      00FF5E42H   LINE      CODE     ---       #70
      00FF5E44H   LINE      CODE     ---       #71
      00FF5E46H   LINE      CODE     ---       #72
      00FF5E4BH   LINE      CODE     ---       #74
      00FF5E4FH   LINE      CODE     ---       #75
      00FF5E53H   LINE      CODE     ---       #76
      00FF5E56H   LINE      CODE     ---       #77
      00FF5E59H   LINE      CODE     ---       #78
      00FF5E5BH   LINE      CODE     ---       #79
      00FF5E60H   LINE      CODE     ---       #81
      00FF5E64H   LINE      CODE     ---       #82
      00FF5E68H   LINE      CODE     ---       #83
      00FF5E6BH   LINE      CODE     ---       #84
      00FF5E6EH   LINE      CODE     ---       #85
      00FF5E70H   LINE      CODE     ---       #86
      00FF5E75H   LINE      CODE     ---       #88
      00FF5E79H   LINE      CODE     ---       #89
      00FF5E7DH   LINE      CODE     ---       #90
      00FF5E80H   LINE      CODE     ---       #91
      00FF5E83H   LINE      CODE     ---       #92
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6A96H   BLOCK     CODE     ---       LVL=0
      R7          REGSYM    ---      BYTE      tim_n
      00FF6A96H   LINE      CODE     ---       #94
      00FF6A98H   LINE      CODE     ---       #96
      00FF6AACH   LINE      CODE     ---       #98
      00FF6AACH   LINE      CODE     ---       #100
      00FF6AAFH   LINE      CODE     ---       #101
      00FF6AB2H   LINE      CODE     ---       #102
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 84


      00FF6AB5H   LINE      CODE     ---       #103
      00FF6AB7H   LINE      CODE     ---       #104
      00FF6AB8H   LINE      CODE     ---       #106
      00FF6AB8H   LINE      CODE     ---       #108
      00FF6ABBH   LINE      CODE     ---       #109
      00FF6ABEH   LINE      CODE     ---       #110
      00FF6AC1H   LINE      CODE     ---       #111
      00FF6AC3H   LINE      CODE     ---       #112
      00FF6AC4H   LINE      CODE     ---       #114
      00FF6AC4H   LINE      CODE     ---       #116
      00FF6AC7H   LINE      CODE     ---       #117
      00FF6ACAH   LINE      CODE     ---       #118
      00FF6ACDH   LINE      CODE     ---       #119
      00FF6ACEH   LINE      CODE     ---       #121
      00FF6ACEH   LINE      CODE     ---       #123
      00FF6AD1H   LINE      CODE     ---       #124
      00FF6AD4H   LINE      CODE     ---       #125
      00FF6AD7H   LINE      CODE     ---       #126
      00FF6AD8H   LINE      CODE     ---       #128
      00FF6AD8H   LINE      CODE     ---       #130
      00FF6ADBH   LINE      CODE     ---       #131
      00FF6ADEH   LINE      CODE     ---       #132
      00FF6AE1H   LINE      CODE     ---       #133
      00FF6AE1H   LINE      CODE     ---       #135
      00FF6AE1H   LINE      CODE     ---       #136
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6A49H   BLOCK     CODE     ---       LVL=0
      R5          REGSYM    ---      BYTE      tim_n
      00FF6A4BH   BLOCK     CODE     NEAR LAB  LVL=1
      WR6         REGSYM    ---      WORD      dat
      ---         BLOCKEND  ---      ---       LVL=1
      00FF6A49H   LINE      CODE     ---       #137
      00FF6A4BH   LINE      CODE     ---       #138
      00FF6A4BH   LINE      CODE     ---       #139
      00FF6A4DH   LINE      CODE     ---       #140
      00FF6A61H   LINE      CODE     ---       #142
      00FF6A61H   LINE      CODE     ---       #144
      00FF6A67H   LINE      CODE     ---       #145
      00FF6A69H   LINE      CODE     ---       #146
      00FF6A6BH   LINE      CODE     ---       #148
      00FF6A6BH   LINE      CODE     ---       #150
      00FF6A71H   LINE      CODE     ---       #151
      00FF6A73H   LINE      CODE     ---       #152
      00FF6A75H   LINE      CODE     ---       #154
      00FF6A75H   LINE      CODE     ---       #156
      00FF6A7BH   LINE      CODE     ---       #157
      00FF6A7DH   LINE      CODE     ---       #158
      00FF6A7FH   LINE      CODE     ---       #160
      00FF6A7FH   LINE      CODE     ---       #162
      00FF6A85H   LINE      CODE     ---       #163
      00FF6A87H   LINE      CODE     ---       #164
      00FF6A89H   LINE      CODE     ---       #166
      00FF6A89H   LINE      CODE     ---       #168
      00FF6A8FH   LINE      CODE     ---       #169
      00FF6A95H   LINE      CODE     ---       #170
      00FF6A95H   LINE      CODE     ---       #172
      00FF6A95H   LINE      CODE     ---       #173
      00FF6A95H   LINE      CODE     ---       #174
      ---         BLOCKEND  ---      ---       LVL=0

      00FF68B9H   BLOCK     CODE     ---       LVL=0
      R7          REGSYM    ---      BYTE      tim_n
      00FF68B9H   LINE      CODE     ---       #175
      00FF68BBH   LINE      CODE     ---       #177
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 85


      00FF68CFH   LINE      CODE     ---       #179
      00FF68CFH   LINE      CODE     ---       #181
      00FF68D1H   LINE      CODE     ---       #182
      00FF68D4H   LINE      CODE     ---       #183
      00FF68D7H   LINE      CODE     ---       #184
      00FF68D9H   LINE      CODE     ---       #185
      00FF68DAH   LINE      CODE     ---       #187
      00FF68DAH   LINE      CODE     ---       #189
      00FF68DCH   LINE      CODE     ---       #190
      00FF68DFH   LINE      CODE     ---       #191
      00FF68E2H   LINE      CODE     ---       #192
      00FF68E4H   LINE      CODE     ---       #193
      00FF68E5H   LINE      CODE     ---       #195
      00FF68E5H   LINE      CODE     ---       #197
      00FF68E8H   LINE      CODE     ---       #198
      00FF68EBH   LINE      CODE     ---       #199
      00FF68EEH   LINE      CODE     ---       #200
      00FF68F1H   LINE      CODE     ---       #201
      00FF68F2H   LINE      CODE     ---       #203
      00FF68F2H   LINE      CODE     ---       #205
      00FF68F5H   LINE      CODE     ---       #206
      00FF68F8H   LINE      CODE     ---       #207
      00FF68FBH   LINE      CODE     ---       #208
      00FF68FEH   LINE      CODE     ---       #209
      00FF68FFH   LINE      CODE     ---       #211
      00FF68FFH   LINE      CODE     ---       #213
      00FF6902H   LINE      CODE     ---       #214
      00FF6905H   LINE      CODE     ---       #215
      00FF6908H   LINE      CODE     ---       #216
      00FF690BH   LINE      CODE     ---       #217
      00FF690BH   LINE      CODE     ---       #219
      00FF690BH   LINE      CODE     ---       #220
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       PWM
      00FF30BEH   PUBLIC    CODE     ---       PWM_init
      00FF399FH   PUBLIC    CODE     ---       PWM_change
      00FF8043H   PUBLIC    HCONST   ---       PWM_CCR_ADDR
      00FF8063H   PUBLIC    HCONST   ---       PWM_ARR_ADDR
      00FF806BH   PUBLIC    HCONST   ---       PWM_CCER_ADDR
      00FF807BH   PUBLIC    HCONST   ---       PWM_CCMR_ADDR
      000000FAH   PUBLIC    EDATA    BYTE      ?PWM_init?BYTE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 86


      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF30BEH   BLOCK     CODE     ---       LVL=0
      WR30        REGSYM    ---      INT       pwmch
      DR24        REGSYM    ---      DWORD     freq
      00000100H   SYMBOL    EDATA    DWORD     duty
      00FF30C0H   BLOCK     CODE     NEAR LAB  LVL=1
      DR20        REGSYM    ---      DWORD     match_temp
      DR24        REGSYM    ---      DWORD     period_temp
      WR28        REGSYM    ---      WORD      freq_div
      ---         BLOCKEND  ---      ---       LVL=1
      00FF30BEH   LINE      CODE     ---       #14
      00FF30C0H   LINE      CODE     ---       #15
      00FF30C0H   LINE      CODE     ---       #19
      00FF30C0H   LINE      CODE     ---       #29
      00FF30CDH   LINE      CODE     ---       #30
      00FF30CDH   LINE      CODE     ---       #31
      00FF30D6H   LINE      CODE     ---       #33
      00FF30E0H   LINE      CODE     ---       #35
      00FF30FFH   LINE      CODE     ---       #36
      00FF3101H   LINE      CODE     ---       #39
      00FF3105H   LINE      CODE     ---       #40
      00FF3105H   LINE      CODE     ---       #43
      00FF310EH   LINE      CODE     ---       #46
      00FF3139H   LINE      CODE     ---       #47
      00FF315AH   LINE      CODE     ---       #50
      00FF31A6H   LINE      CODE     ---       #53
      00FF31B5H   LINE      CODE     ---       #54
      00FF31C0H   LINE      CODE     ---       #56
      00FF31C9H   LINE      CODE     ---       #57
      00FF31CFH   LINE      CODE     ---       #58
      00FF31D2H   LINE      CODE     ---       #61
      00FF3200H   LINE      CODE     ---       #62
      00FF3222H   LINE      CODE     ---       #63
      00FF3245H   LINE      CODE     ---       #66
      00FF3291H   LINE      CODE     ---       #67
      00FF32E7H   LINE      CODE     ---       #71
      00FF32F6H   LINE      CODE     ---       #72
      00FF3301H   LINE      CODE     ---       #74
      00FF330AH   LINE      CODE     ---       #75
      00FF3317H   LINE      CODE     ---       #76
      00FF3317H   LINE      CODE     ---       #79
      00FF3343H   LINE      CODE     ---       #80
      00FF3371H   LINE      CODE     ---       #83
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 87


      00FF3399H   LINE      CODE     ---       #84
      00FF33C7H   LINE      CODE     ---       #87
      00FF33DEH   LINE      CODE     ---       #88
      00FF3409H   LINE      CODE     ---       #91
      ---         BLOCKEND  ---      ---       LVL=0

      00FF399FH   BLOCK     CODE     ---       LVL=0
      WR30        REGSYM    ---      INT       pwmch
      DR24        REGSYM    ---      DWORD     duty
      00FF39A3H   BLOCK     CODE     NEAR LAB  LVL=1
      DR20        REGSYM    ---      DWORD     match_temp
      DR16        REGSYM    ---      DWORD     arr
      ---         BLOCKEND  ---      ---       LVL=1
      00FF399FH   LINE      CODE     ---       #92
      00FF39A3H   LINE      CODE     ---       #93
      00FF39A3H   LINE      CODE     ---       #95
      00FF39E7H   LINE      CODE     ---       #98
      00FF39EDH   LINE      CODE     ---       #101
      00FF3A18H   LINE      CODE     ---       #102
      00FF3A31H   LINE      CODE     ---       #104
      00FF3A34H   LINE      CODE     ---       #107
      00FF3A62H   LINE      CODE     ---       #108
      00FF3A84H   LINE      CODE     ---       #109
      00FF3AA7H   LINE      CODE     ---       #112
      00FF3AF3H   LINE      CODE     ---       #113
      00FF3B49H   LINE      CODE     ---       #115
      00FF3B49H   LINE      CODE     ---       #117
      00FF3B4FH   LINE      CODE     ---       #119
      00FF3B70H   LINE      CODE     ---       #120
      00FF3B72H   LINE      CODE     ---       #123
      00FF3B76H   LINE      CODE     ---       #124
      00FF3B76H   LINE      CODE     ---       #126
      00FF3B9EH   LINE      CODE     ---       #127
      00FF3BC8H   LINE      CODE     ---       #131
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       UART
      00FF1793H   PUBLIC    CODE     ---       UART_Send_float
      00FF7754H   PUBLIC    CODE     ---       UART_Send_string
      00FF690CH   PUBLIC    CODE     ---       UART_Send_byte
      00FF44CAH   PUBLIC    CODE     ---       UART_init
      00FF1FB0H   PUBLIC    CODE     ---       UART_Send_int
      000002FCH   PUBLIC    EDATA    BYTE      UART1_OK
      000002FDH   PUBLIC    EDATA    BYTE      UART2_OK
      000002FEH   PUBLIC    EDATA    BYTE      UART3_OK
      000002FFH   PUBLIC    EDATA    BYTE      UART4_OK
      00000262H   PUBLIC    EDATA    BYTE      ?UART_Send_int?BYTE
      00000252H   PUBLIC    EDATA    BYTE      ?UART_Send_float?BYTE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000FEH   SFRSYM    DATA     BYTE      S4BUF
      000000ADH   SFRSYM    DATA     BYTE      S3BUF
      000000DDH.5 SFRSYM    DATA     BIT       T4x12
      0000009BH   SFRSYM    DATA     BYTE      S2BUF
      000000DDH.1 SFRSYM    DATA     BIT       T3x12
      0000008EH.2 SFRSYM    DATA     BIT       T2x12
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000DDH.7 SFRSYM    DATA     BIT       T4R
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 88


      000000DDH.3 SFRSYM    DATA     BIT       T3R
      0000008EH.4 SFRSYM    DATA     BIT       T2R
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000D3H   SFRSYM    DATA     BYTE      T4L
      000000D5H   SFRSYM    DATA     BYTE      T3L
      000000D7H   SFRSYM    DATA     BYTE      T2L
      0000008BH   SFRSYM    DATA     BYTE      TL1
      000000D2H   SFRSYM    DATA     BYTE      T4H
      000000D4H   SFRSYM    DATA     BYTE      T3H
      000000D6H   SFRSYM    DATA     BYTE      T2H
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000AFH.4 SFRSYM    DATA     BIT       ES4
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000AFH.3 SFRSYM    DATA     BIT       ES3
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000AFH.0 SFRSYM    DATA     BIT       ES2
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      00000099H   SFRSYM    DATA     BYTE      SBUF
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000A8H.4 SFRSYM    DATA     BIT       ES
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF44CAH   BLOCK     CODE     ---       LVL=0
      R13         REGSYM    ---      BYTE      pin
      DR28        REGSYM    ---      DWORD     btl
      R14         REGSYM    ---      BYTE      n
      R15         REGSYM    ---      BYTE      isr
      00FF44CAH   LINE      CODE     ---       #8
      00FF44D6H   LINE      CODE     ---       #10
      00FF44DBH   LINE      CODE     ---       #12
      00FF44DEH   LINE      CODE     ---       #13
      00FF44EEH   LINE      CODE     ---       #15
      00FF44F4H   LINE      CODE     ---       #16
      00FF44F9H   LINE      CODE     ---       #17
      00FF44FEH   LINE      CODE     ---       #18
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 89


      00FF4501H   LINE      CODE     ---       #19
      00FF4501H   LINE      CODE     ---       #20
      00FF4504H   LINE      CODE     ---       #21
      00FF4507H   LINE      CODE     ---       #22
      00FF450BH   LINE      CODE     ---       #23
      00FF4530H   LINE      CODE     ---       #24
      00FF4534H   LINE      CODE     ---       #25
      00FF4536H   LINE      CODE     ---       #26
      00FF4539H   LINE      CODE     ---       #27
      00FF453EH   LINE      CODE     ---       #28
      00FF4540H   LINE      CODE     ---       #30
      00FF4545H   LINE      CODE     ---       #33
      00FF454FH   LINE      CODE     ---       #34
      00FF4552H   LINE      CODE     ---       #35
      00FF4555H   LINE      CODE     ---       #36
      00FF4562H   LINE      CODE     ---       #37
      00FF4587H   LINE      CODE     ---       #38
      00FF458BH   LINE      CODE     ---       #39
      00FF458EH   LINE      CODE     ---       #40
      00FF4591H   LINE      CODE     ---       #41
      00FF4596H   LINE      CODE     ---       #42
      00FF4599H   LINE      CODE     ---       #44
      00FF459EH   LINE      CODE     ---       #46
      00FF45A8H   LINE      CODE     ---       #47
      00FF45ABH   LINE      CODE     ---       #48
      00FF45AEH   LINE      CODE     ---       #49
      00FF45D3H   LINE      CODE     ---       #50
      00FF45D7H   LINE      CODE     ---       #51
      00FF45DAH   LINE      CODE     ---       #52
      00FF45DDH   LINE      CODE     ---       #53
      00FF45E2H   LINE      CODE     ---       #54
      00FF45E5H   LINE      CODE     ---       #56
      00FF45EAH   LINE      CODE     ---       #58
      00FF45F4H   LINE      CODE     ---       #59
      00FF45F7H   LINE      CODE     ---       #60
      00FF45FAH   LINE      CODE     ---       #61
      00FF461FH   LINE      CODE     ---       #62
      00FF4623H   LINE      CODE     ---       #63
      00FF4626H   LINE      CODE     ---       #64
      00FF4629H   LINE      CODE     ---       #65
      00FF462EH   LINE      CODE     ---       #66
      00FF4631H   LINE      CODE     ---       #69
      ---         BLOCKEND  ---      ---       LVL=0

      00FF690CH   BLOCK     CODE     ---       LVL=0
      R7          REGSYM    ---      BYTE      pin
      R10         REGSYM    ---      BYTE      c
      00FF690CH   LINE      CODE     ---       #70
      00FF6910H   LINE      CODE     ---       #72
      00FF6914H   LINE      CODE     ---       #74
      00FF691AH   LINE      CODE     ---       #75
      00FF6920H   LINE      CODE     ---       #76
      00FF6923H   LINE      CODE     ---       #77
      00FF6923H   LINE      CODE     ---       #78
      00FF6927H   LINE      CODE     ---       #80
      00FF692DH   LINE      CODE     ---       #81
      00FF6933H   LINE      CODE     ---       #82
      00FF6936H   LINE      CODE     ---       #83
      00FF6936H   LINE      CODE     ---       #84
      00FF693AH   LINE      CODE     ---       #86
      00FF6940H   LINE      CODE     ---       #87
      00FF6946H   LINE      CODE     ---       #88
      00FF6949H   LINE      CODE     ---       #89
      00FF6949H   LINE      CODE     ---       #90
      00FF694DH   LINE      CODE     ---       #92
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 90


      00FF6953H   LINE      CODE     ---       #93
      00FF6959H   LINE      CODE     ---       #94
      00FF695CH   LINE      CODE     ---       #95
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7754H   BLOCK     CODE     ---       LVL=0
      00000305H   SYMBOL    EDATA    BYTE      pin
      REG=3       REGSYM    ---      ---       pt
      00FF7754H   LINE      CODE     ---       #97
      00FF775CH   LINE      CODE     ---       #99
      00FF775EH   LINE      CODE     ---       #101
      00FF776AH   LINE      CODE     ---       #102
      00FF776FH   LINE      CODE     ---       #103
      ---         BLOCKEND  ---      ---       LVL=0

      00FF1FB0H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      pin
      00000263H   SYMBOL    EDATA    ---       pa
      00000267H   SYMBOL    EDATA    LONG      num
      0000026BH   SYMBOL    EDATA    ---       pb
      00FF1FBCH   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      k
      ---         BLOCKEND  ---      ---       LVL=1
      00FF1FB0H   LINE      CODE     ---       #104
      00FF1FBCH   LINE      CODE     ---       #105
      00FF1FBCH   LINE      CODE     ---       #108
      00FF1FBEH   LINE      CODE     ---       #110
      00FF1FD0H   LINE      CODE     ---       #111
      00FF1FD9H   LINE      CODE     ---       #112
      00FF1FE9H   LINE      CODE     ---       #115
      00FF1FFBH   LINE      CODE     ---       #116
      00FF2014H   LINE      CODE     ---       #117
      00FF2043H   LINE      CODE     ---       #119
      00FF2043H   LINE      CODE     ---       #120
      00FF2059H   LINE      CODE     ---       #121
      00FF205CH   LINE      CODE     ---       #122
      00FF205CH   LINE      CODE     ---       #123
      00FF207CH   LINE      CODE     ---       #124
      00FF2092H   LINE      CODE     ---       #125
      00FF2095H   LINE      CODE     ---       #126
      00FF2095H   LINE      CODE     ---       #127
      00FF20B5H   LINE      CODE     ---       #128
      00FF20D5H   LINE      CODE     ---       #129
      00FF20EBH   LINE      CODE     ---       #130
      00FF20EEH   LINE      CODE     ---       #131
      00FF20EEH   LINE      CODE     ---       #132
      00FF210EH   LINE      CODE     ---       #133
      00FF212EH   LINE      CODE     ---       #134
      00FF214EH   LINE      CODE     ---       #135
      00FF2164H   LINE      CODE     ---       #136
      00FF2167H   LINE      CODE     ---       #137
      00FF2167H   LINE      CODE     ---       #138
      00FF2187H   LINE      CODE     ---       #139
      00FF21A7H   LINE      CODE     ---       #140
      00FF21C7H   LINE      CODE     ---       #141
      00FF21E7H   LINE      CODE     ---       #142
      00FF21FDH   LINE      CODE     ---       #143
      00FF2200H   LINE      CODE     ---       #144
      00FF2200H   LINE      CODE     ---       #145
      00FF2224H   LINE      CODE     ---       #146
      00FF2244H   LINE      CODE     ---       #147
      00FF2264H   LINE      CODE     ---       #148
      00FF2284H   LINE      CODE     ---       #149
      00FF22A4H   LINE      CODE     ---       #150
      00FF22BAH   LINE      CODE     ---       #151
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 91


      00FF22BDH   LINE      CODE     ---       #152
      00FF22BDH   LINE      CODE     ---       #153
      00FF22E1H   LINE      CODE     ---       #154
      00FF2305H   LINE      CODE     ---       #155
      00FF2325H   LINE      CODE     ---       #156
      00FF2345H   LINE      CODE     ---       #157
      00FF2365H   LINE      CODE     ---       #158
      00FF2385H   LINE      CODE     ---       #159
      00FF239BH   LINE      CODE     ---       #160
      00FF239EH   LINE      CODE     ---       #161
      00FF239EH   LINE      CODE     ---       #162
      00FF23C2H   LINE      CODE     ---       #163
      00FF23E6H   LINE      CODE     ---       #164
      00FF240AH   LINE      CODE     ---       #165
      00FF242AH   LINE      CODE     ---       #166
      00FF244AH   LINE      CODE     ---       #167
      00FF246AH   LINE      CODE     ---       #168
      00FF248AH   LINE      CODE     ---       #169
      00FF24A0H   LINE      CODE     ---       #170
      00FF24A3H   LINE      CODE     ---       #171
      00FF24A3H   LINE      CODE     ---       #172
      00FF24C7H   LINE      CODE     ---       #173
      00FF24EBH   LINE      CODE     ---       #174
      00FF250FH   LINE      CODE     ---       #175
      00FF2533H   LINE      CODE     ---       #176
      00FF2553H   LINE      CODE     ---       #177
      00FF2573H   LINE      CODE     ---       #178
      00FF2593H   LINE      CODE     ---       #179
      00FF25B3H   LINE      CODE     ---       #180
      00FF25C9H   LINE      CODE     ---       #181
      00FF25CCH   LINE      CODE     ---       #182
      00FF25CCH   LINE      CODE     ---       #183
      00FF25F0H   LINE      CODE     ---       #184
      00FF2614H   LINE      CODE     ---       #185
      00FF2638H   LINE      CODE     ---       #186
      00FF265CH   LINE      CODE     ---       #187
      00FF2680H   LINE      CODE     ---       #188
      00FF26A0H   LINE      CODE     ---       #189
      00FF26C0H   LINE      CODE     ---       #190
      00FF26E0H   LINE      CODE     ---       #191
      00FF2700H   LINE      CODE     ---       #192
      00FF2719H   LINE      CODE     ---       #193
      00FF2719H   LINE      CODE     ---       #194
      00FF2719H   LINE      CODE     ---       #195
      00FF2719H   LINE      CODE     ---       #197
      00FF271BH   LINE      CODE     ---       #199
      00FF272DH   LINE      CODE     ---       #200
      00FF2736H   LINE      CODE     ---       #201
      ---         BLOCKEND  ---      ---       LVL=0

      00FF1793H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      pin
      00000253H   SYMBOL    EDATA    ---       ps
      00000257H   SYMBOL    EDATA    FLOAT     num
      0000025BH   SYMBOL    EDATA    ---       pe
      00FF179FH   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      k
      0000025FH   SYMBOL    EDATA    DWORD     l
      ---         BLOCKEND  ---      ---       LVL=1
      00FF1793H   LINE      CODE     ---       #202
      00FF179FH   LINE      CODE     ---       #203
      00FF179FH   LINE      CODE     ---       #207
      00FF17B0H   LINE      CODE     ---       #209
      00FF17B2H   LINE      CODE     ---       #211
      00FF17C4H   LINE      CODE     ---       #212
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 92


      00FF17CDH   LINE      CODE     ---       #213
      00FF17E0H   LINE      CODE     ---       #215
      00FF17F0H   LINE      CODE     ---       #217
      00FF180BH   LINE      CODE     ---       #218
      00FF183AH   LINE      CODE     ---       #220
      00FF183AH   LINE      CODE     ---       #221
      00FF183AH   LINE      CODE     ---       #222
      00FF183DH   LINE      CODE     ---       #223
      00FF183DH   LINE      CODE     ---       #224
      00FF1862H   LINE      CODE     ---       #225
      00FF1862H   LINE      CODE     ---       #226
      00FF1865H   LINE      CODE     ---       #227
      00FF1865H   LINE      CODE     ---       #228
      00FF188AH   LINE      CODE     ---       #229
      00FF18AFH   LINE      CODE     ---       #230
      00FF18AFH   LINE      CODE     ---       #231
      00FF18B2H   LINE      CODE     ---       #232
      00FF18B2H   LINE      CODE     ---       #233
      00FF18D7H   LINE      CODE     ---       #234
      00FF18FCH   LINE      CODE     ---       #235
      00FF1921H   LINE      CODE     ---       #236
      00FF1921H   LINE      CODE     ---       #237
      00FF1924H   LINE      CODE     ---       #238
      00FF1924H   LINE      CODE     ---       #239
      00FF194BH   LINE      CODE     ---       #240
      00FF1970H   LINE      CODE     ---       #241
      00FF1995H   LINE      CODE     ---       #242
      00FF19BAH   LINE      CODE     ---       #243
      00FF19BAH   LINE      CODE     ---       #244
      00FF19BDH   LINE      CODE     ---       #245
      00FF19BDH   LINE      CODE     ---       #246
      00FF19E4H   LINE      CODE     ---       #247
      00FF1A0BH   LINE      CODE     ---       #248
      00FF1A30H   LINE      CODE     ---       #249
      00FF1A55H   LINE      CODE     ---       #250
      00FF1A7AH   LINE      CODE     ---       #251
      00FF1A7AH   LINE      CODE     ---       #252
      00FF1A7DH   LINE      CODE     ---       #253
      00FF1A7DH   LINE      CODE     ---       #254
      00FF1AA4H   LINE      CODE     ---       #255
      00FF1ACBH   LINE      CODE     ---       #256
      00FF1AF2H   LINE      CODE     ---       #257
      00FF1B17H   LINE      CODE     ---       #258
      00FF1B3CH   LINE      CODE     ---       #259
      00FF1B61H   LINE      CODE     ---       #260
      00FF1B61H   LINE      CODE     ---       #261
      00FF1B64H   LINE      CODE     ---       #262
      00FF1B64H   LINE      CODE     ---       #263
      00FF1B8BH   LINE      CODE     ---       #264
      00FF1BB2H   LINE      CODE     ---       #265
      00FF1BD9H   LINE      CODE     ---       #266
      00FF1C00H   LINE      CODE     ---       #267
      00FF1C25H   LINE      CODE     ---       #268
      00FF1C4AH   LINE      CODE     ---       #269
      00FF1C6FH   LINE      CODE     ---       #270
      00FF1C6FH   LINE      CODE     ---       #271
      00FF1C72H   LINE      CODE     ---       #272
      00FF1C72H   LINE      CODE     ---       #273
      00FF1C99H   LINE      CODE     ---       #274
      00FF1CC0H   LINE      CODE     ---       #275
      00FF1CE7H   LINE      CODE     ---       #276
      00FF1D0EH   LINE      CODE     ---       #277
      00FF1D35H   LINE      CODE     ---       #278
      00FF1D5AH   LINE      CODE     ---       #279
      00FF1D7FH   LINE      CODE     ---       #280
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 93


      00FF1DA4H   LINE      CODE     ---       #281
      00FF1DA4H   LINE      CODE     ---       #282
      00FF1DA7H   LINE      CODE     ---       #283
      00FF1DA7H   LINE      CODE     ---       #284
      00FF1DCEH   LINE      CODE     ---       #285
      00FF1DF5H   LINE      CODE     ---       #286
      00FF1E1CH   LINE      CODE     ---       #287
      00FF1E43H   LINE      CODE     ---       #288
      00FF1E6AH   LINE      CODE     ---       #289
      00FF1E91H   LINE      CODE     ---       #290
      00FF1EB6H   LINE      CODE     ---       #291
      00FF1EDBH   LINE      CODE     ---       #292
      00FF1F00H   LINE      CODE     ---       #293
      00FF1F1CH   LINE      CODE     ---       #294
      00FF1F1CH   LINE      CODE     ---       #295
      00FF1F1CH   LINE      CODE     ---       #296
      00FF1F24H   LINE      CODE     ---       #298
      00FF1F49H   LINE      CODE     ---       #299
      00FF1F6EH   LINE      CODE     ---       #300
      00FF1F90H   LINE      CODE     ---       #302
      00FF1F92H   LINE      CODE     ---       #304
      00FF1FA4H   LINE      CODE     ---       #305
      00FF1FADH   LINE      CODE     ---       #306
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       EEPROM
      00FF7560H   PUBLIC    CODE     ---       EEPROM_Delete
      00FF7D1EH   PUBLIC    CODE     ---       EEPROM_OFF
      00FF7331H   PUBLIC    CODE     ---       EEPROM_Read
      00FF735DH   PUBLIC    CODE     ---       EEPROM_Change
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000C6H   SFRSYM    DATA     BYTE      IAP_TRIG
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000C2H   SFRSYM    DATA     BYTE      IAP_DATA
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F5H   SFRSYM    DATA     BYTE      IAP_TPS
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000C4H   SFRSYM    DATA     BYTE      IAP_ADDRL
      000000C3H   SFRSYM    DATA     BYTE      IAP_ADDRH
      000000F6H   SFRSYM    DATA     BYTE      IAP_ADDRE
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 94


      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000C5H   SFRSYM    DATA     BYTE      IAP_CMD
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF7D1EH   BLOCK     CODE     ---       LVL=0
      00FF7D1EH   LINE      CODE     ---       #11
      00FF7D1EH   LINE      CODE     ---       #13
      00FF7D21H   LINE      CODE     ---       #14
      00FF7D24H   LINE      CODE     ---       #15
      00FF7D27H   LINE      CODE     ---       #16
      00FF7D2AH   LINE      CODE     ---       #17
      00FF7D2DH   LINE      CODE     ---       #18
      00FF7D30H   LINE      CODE     ---       #19
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7331H   BLOCK     CODE     ---       LVL=0
      DR4         REGSYM    ---      DWORD     addr
      00FF7333H   BLOCK     CODE     NEAR LAB  LVL=1
      R15         REGSYM    ---      BYTE      dat
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7331H   LINE      CODE     ---       #21
      00FF7333H   LINE      CODE     ---       #22
      00FF7333H   LINE      CODE     ---       #25
      00FF7336H   LINE      CODE     ---       #26
      00FF7339H   LINE      CODE     ---       #27
      00FF733CH   LINE      CODE     ---       #28
      00FF7340H   LINE      CODE     ---       #29
      00FF7344H   LINE      CODE     ---       #30
      00FF7348H   LINE      CODE     ---       #31
      00FF734BH   LINE      CODE     ---       #32
      00FF734EH   LINE      CODE     ---       #33
      00FF734FH   LINE      CODE     ---       #34
      00FF7350H   LINE      CODE     ---       #35
      00FF7351H   LINE      CODE     ---       #36
      00FF7352H   LINE      CODE     ---       #37
      00FF7355H   LINE      CODE     ---       #38
      00FF7358H   LINE      CODE     ---       #40
      00FF735AH   LINE      CODE     ---       #41
      ---         BLOCKEND  ---      ---       LVL=0

      00FF735DH   BLOCK     CODE     ---       LVL=0
      DR4         REGSYM    ---      DWORD     addr
      R15         REGSYM    ---      BYTE      dat
      00FF735DH   LINE      CODE     ---       #43
      00FF7361H   LINE      CODE     ---       #45
      00FF7364H   LINE      CODE     ---       #46
      00FF7367H   LINE      CODE     ---       #47
      00FF736AH   LINE      CODE     ---       #48
      00FF736EH   LINE      CODE     ---       #49
      00FF7372H   LINE      CODE     ---       #50
      00FF7376H   LINE      CODE     ---       #51
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 95


      00FF7379H   LINE      CODE     ---       #52
      00FF737CH   LINE      CODE     ---       #53
      00FF737FH   LINE      CODE     ---       #54
      00FF7380H   LINE      CODE     ---       #55
      00FF7381H   LINE      CODE     ---       #56
      00FF7382H   LINE      CODE     ---       #57
      00FF7383H   LINE      CODE     ---       #58
      00FF7386H   LINE      CODE     ---       #59
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7560H   BLOCK     CODE     ---       LVL=0
      DR4         REGSYM    ---      DWORD     addr
      00FF7560H   LINE      CODE     ---       #61
      00FF7560H   LINE      CODE     ---       #63
      00FF7563H   LINE      CODE     ---       #64
      00FF7566H   LINE      CODE     ---       #65
      00FF7569H   LINE      CODE     ---       #66
      00FF756DH   LINE      CODE     ---       #67
      00FF7571H   LINE      CODE     ---       #68
      00FF7575H   LINE      CODE     ---       #69
      00FF7578H   LINE      CODE     ---       #70
      00FF757BH   LINE      CODE     ---       #71
      00FF757CH   LINE      CODE     ---       #72
      00FF757DH   LINE      CODE     ---       #73
      00FF757EH   LINE      CODE     ---       #74
      00FF757FH   LINE      CODE     ---       #75
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       CAN
      00FF5D5DH   PUBLIC    CODE     ---       CanReadFifo
      00FF7D31H   PUBLIC    CODE     ---       CanReadReg
      00FF55F6H   PUBLIC    CODE     ---       CanReadMsg
      00FF7CCEH   PUBLIC    CODE     ---       CanWriteReg
      00FF4636H   PUBLIC    CODE     ---       CanSendMsg
      00FF3DEAH   PUBLIC    CODE     ---       CANInit
      000002DBH   PUBLIC    EDATA    BYTE      TSG1
      000002DCH   PUBLIC    EDATA    BYTE      TSG2
      000002DDH   PUBLIC    EDATA    BYTE      BRP
      000002DEH   PUBLIC    EDATA    WORD      CAN_time
      00000021H.4 PUBLIC    BIT      BIT       CAN_TX_OK
      00000021H.5 PUBLIC    BIT      BIT       ?CANInit?BIT
      00000021H.6 PUBLIC    BIT      BIT       ?CanReadMsg?BIT
      00000274H   PUBLIC    EDATA    BYTE      ?CanSendMsg?BYTE
      00000021H.7 PUBLIC    BIT      BIT       ?CanSendMsg?BIT
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000F1H.1 SFRSYM    DATA     BIT       CANIE
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000097H.3 SFRSYM    DATA     BIT       CANSEL
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 96


      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000F1H.5 SFRSYM    DATA     BIT       CAN2IE
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF7D31H   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      addr
      R11         REGSYM    ---      BYTE      dat
      00FF7D31H   LINE      CODE     ---       #14
      00FF7D31H   LINE      CODE     ---       #15
      00FF7D31H   LINE      CODE     ---       #17
      00FF7D3CH   LINE      CODE     ---       #18
      00FF7D43H   LINE      CODE     ---       #19
      00FF7D43H   LINE      CODE     ---       #20
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7CCEH   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      addr
      R2          REGSYM    ---      BYTE      dat
      00FF7CCEH   LINE      CODE     ---       #22
      00FF7CD0H   LINE      CODE     ---       #24
      00FF7CDBH   LINE      CODE     ---       #25
      00FF7CE2H   LINE      CODE     ---       #26
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3DEAH   BLOCK     CODE     ---       LVL=0
      R5          REGSYM    ---      BYTE      CAN
      WR6         REGSYM    ---      INT       Bbaud
      R4          REGSYM    ---      BYTE      CAN_N
      00000021H.5 SYMBOL    BIT      BIT       en
      00FF3DEAH   LINE      CODE     ---       #83
      00FF3DEEH   LINE      CODE     ---       #85
      00FF3E28H   LINE      CODE     ---       #87
      00FF3E39H   LINE      CODE     ---       #88
      00FF3E4AH   LINE      CODE     ---       #89
      00FF3E5BH   LINE      CODE     ---       #90
      00FF3E6CH   LINE      CODE     ---       #91
      00FF3E7CH   LINE      CODE     ---       #92
      00FF3E8CH   LINE      CODE     ---       #93
      00FF3E9CH   LINE      CODE     ---       #94
      00FF3EAAH   LINE      CODE     ---       #95
      00FF3EBAH   LINE      CODE     ---       #96
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 97


      00FF3EC8H   LINE      CODE     ---       #97
      00FF3ED6H   LINE      CODE     ---       #98
      00FF3EE4H   LINE      CODE     ---       #99
      00FF3EF4H   LINE      CODE     ---       #100
      00FF3EF4H   LINE      CODE     ---       #101
      00FF3EF4H   LINE      CODE     ---       #103
      00FF3EFFH   LINE      CODE     ---       #105
      00FF3F03H   LINE      CODE     ---       #107
      00FF3F12H   LINE      CODE     ---       #109
      00FF3F1BH   LINE      CODE     ---       #110
      00FF3F23H   LINE      CODE     ---       #111
      00FF3F2BH   LINE      CODE     ---       #112
      00FF3F31H   LINE      CODE     ---       #113
      00FF3F31H   LINE      CODE     ---       #114
      00FF3F34H   LINE      CODE     ---       #115
      00FF3F39H   LINE      CODE     ---       #116
      00FF3F39H   LINE      CODE     ---       #117
      00FF3F3DH   LINE      CODE     ---       #119
      00FF3F4EH   LINE      CODE     ---       #121
      00FF3F57H   LINE      CODE     ---       #122
      00FF3F5FH   LINE      CODE     ---       #123
      00FF3F67H   LINE      CODE     ---       #124
      00FF3F6DH   LINE      CODE     ---       #125
      00FF3F6DH   LINE      CODE     ---       #126
      00FF3F70H   LINE      CODE     ---       #127
      00FF3F75H   LINE      CODE     ---       #128
      00FF3F75H   LINE      CODE     ---       #130
      00FF3F7CH   LINE      CODE     ---       #132
      00FF3F8BH   LINE      CODE     ---       #133
      00FF3FA6H   LINE      CODE     ---       #135
      00FF3FADH   LINE      CODE     ---       #136
      00FF3FB4H   LINE      CODE     ---       #137
      00FF3FBBH   LINE      CODE     ---       #138
      00FF3FC2H   LINE      CODE     ---       #139
      00FF3FCAH   LINE      CODE     ---       #140
      00FF3FD2H   LINE      CODE     ---       #141
      00FF3FDAH   LINE      CODE     ---       #142
      00FF3FE2H   LINE      CODE     ---       #144
      00FF3FEAH   LINE      CODE     ---       #145
      00FF3FF2H   LINE      CODE     ---       #146
      ---         BLOCKEND  ---      ---       LVL=0

      00FF5D5DH   BLOCK     CODE     ---       LVL=0
      REG=3       REGSYM    ---      ---       pdat
      00FF5D5DH   LINE      CODE     ---       #149
      00FF5D61H   LINE      CODE     ---       #151
      00FF5D69H   LINE      CODE     ---       #152
      00FF5D72H   LINE      CODE     ---       #153
      00FF5D7BH   LINE      CODE     ---       #154
      00FF5D84H   LINE      CODE     ---       #155
      00FF5D8DH   LINE      CODE     ---       #156
      00FF5D96H   LINE      CODE     ---       #157
      00FF5D9FH   LINE      CODE     ---       #158
      00FF5DA8H   LINE      CODE     ---       #159
      00FF5DB1H   LINE      CODE     ---       #160
      00FF5DBAH   LINE      CODE     ---       #161
      00FF5DC3H   LINE      CODE     ---       #162
      00FF5DCCH   LINE      CODE     ---       #163
      00FF5DD5H   LINE      CODE     ---       #164
      00FF5DDEH   LINE      CODE     ---       #165
      00FF5DE7H   LINE      CODE     ---       #166
      00FF5DF0H   LINE      CODE     ---       #167
      ---         BLOCKEND  ---      ---       LVL=0

      00FF55F6H   BLOCK     CODE     ---       LVL=0
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 98


      REG=3       REGSYM    ---      ---       pdat
      00000021H.6 SYMBOL    BIT      BIT       mode
      00FF55FAH   BLOCK     CODE     NEAR LAB  LVL=1
      0000022AH   SYMBOL    EDATA    BYTE      i
      0000022BH   SYMBOL    EDATA    DWORD     CanID
      0000022FH   SYMBOL    EDATA    ---       buffer
      ---         BLOCKEND  ---      ---       LVL=1
      00FF55F6H   LINE      CODE     ---       #171
      00FF55FAH   LINE      CODE     ---       #172
      00FF55FAH   LINE      CODE     ---       #177
      00FF5601H   LINE      CODE     ---       #178
      00FF5604H   LINE      CODE     ---       #180
      00FF5642H   LINE      CODE     ---       #181
      00FF5647H   LINE      CODE     ---       #183
      00FF5658H   LINE      CODE     ---       #184
      00FF5664H   LINE      CODE     ---       #185
      00FF5666H   LINE      CODE     ---       #188
      00FF5686H   LINE      CODE     ---       #189
      00FF568BH   LINE      CODE     ---       #191
      00FF569CH   LINE      CODE     ---       #192
      00FF56A8H   LINE      CODE     ---       #193
      00FF56A8H   LINE      CODE     ---       #194
      00FF56ACH   LINE      CODE     ---       #195
      ---         BLOCKEND  ---      ---       LVL=0

      00FF4636H   BLOCK     CODE     ---       LVL=0
      R7          REGSYM    ---      BYTE      CAN
      00000279H   SYMBOL    EDATA    DWORD     canid
      REG=3       REGSYM    ---      ---       pdat
      0000027DH   SYMBOL    EDATA    BYTE      len
      00000021H.7 SYMBOL    BIT      BIT       mode
      00FF463EH   BLOCK     CODE     NEAR LAB  LVL=1
      0000027EH   SYMBOL    EDATA    DWORD     CanID
      ---         BLOCKEND  ---      ---       LVL=1
      00FF4636H   LINE      CODE     ---       #196
      00FF463EH   LINE      CODE     ---       #197
      00FF463EH   LINE      CODE     ---       #201
      00FF4649H   LINE      CODE     ---       #203
      00FF464FH   LINE      CODE     ---       #205
      00FF465DH   LINE      CODE     ---       #206
      00FF466CH   LINE      CODE     ---       #207
      00FF4677H   LINE      CODE     ---       #208
      00FF4682H   LINE      CODE     ---       #209
      00FF468DH   LINE      CODE     ---       #211
      00FF4696H   LINE      CODE     ---       #212
      00FF469EH   LINE      CODE     ---       #213
      00FF46A7H   LINE      CODE     ---       #214
      00FF46B0H   LINE      CODE     ---       #216
      00FF46B9H   LINE      CODE     ---       #217
      00FF46C2H   LINE      CODE     ---       #218
      00FF46CBH   LINE      CODE     ---       #219
      00FF46D4H   LINE      CODE     ---       #221
      00FF46DDH   LINE      CODE     ---       #222
      00FF46E4H   LINE      CODE     ---       #223
      00FF46E8H   LINE      CODE     ---       #224
      00FF46E8H   LINE      CODE     ---       #225
      00FF46EAH   LINE      CODE     ---       #228
      00FF46F9H   LINE      CODE     ---       #229
      00FF4702H   LINE      CODE     ---       #230
      00FF470DH   LINE      CODE     ---       #231
      00FF4716H   LINE      CODE     ---       #232
      00FF471EH   LINE      CODE     ---       #234
      00FF4727H   LINE      CODE     ---       #235
      00FF4730H   LINE      CODE     ---       #236
      00FF4739H   LINE      CODE     ---       #237
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 99


      00FF4742H   LINE      CODE     ---       #239
      00FF474BH   LINE      CODE     ---       #240
      00FF4754H   LINE      CODE     ---       #241
      00FF475DH   LINE      CODE     ---       #243
      00FF4764H   LINE      CODE     ---       #244
      00FF4764H   LINE      CODE     ---       #245
      00FF4766H   LINE      CODE     ---       #246
      00FF476EH   LINE      CODE     ---       #247
      00FF4776H   LINE      CODE     ---       #249
      00FF4778H   LINE      CODE     ---       #251
      00FF4782H   LINE      CODE     ---       #252
      00FF4789H   LINE      CODE     ---       #253
      00FF4794H   LINE      CODE     ---       #254
      00FF479BH   LINE      CODE     ---       #255
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       DMA
      00FF479EH   PUBLIC    CODE     ---       DMA_RXD_init
      00FF48E6H   PUBLIC    CODE     ---       DMA_TXD_init
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 100


      00FF48E6H   BLOCK     CODE     ---       LVL=0
      R7          REGSYM    ---      BYTE      pin
      WR0         REGSYM    ---      WORD      length
      REG=7       REGSYM    ---      ---       DAT
      00FF48E6H   LINE      CODE     ---       #7
      00FF48ECH   LINE      CODE     ---       #9
      00FF4906H   LINE      CODE     ---       #11
      00FF4906H   LINE      CODE     ---       #13
      00FF4912H   LINE      CODE     ---       #14
      00FF4919H   LINE      CODE     ---       #15
      00FF4924H   LINE      CODE     ---       #16
      00FF492DH   LINE      CODE     ---       #17
      00FF4938H   LINE      CODE     ---       #18
      00FF4943H   LINE      CODE     ---       #20
      00FF4949H   LINE      CODE     ---       #21
      00FF494CH   LINE      CODE     ---       #22
      00FF494CH   LINE      CODE     ---       #24
      00FF4958H   LINE      CODE     ---       #25
      00FF495FH   LINE      CODE     ---       #26
      00FF496AH   LINE      CODE     ---       #27
      00FF4973H   LINE      CODE     ---       #28
      00FF497EH   LINE      CODE     ---       #29
      00FF4989H   LINE      CODE     ---       #30
      00FF498FH   LINE      CODE     ---       #31
      00FF4992H   LINE      CODE     ---       #32
      00FF4992H   LINE      CODE     ---       #34
      00FF499EH   LINE      CODE     ---       #35
      00FF49A5H   LINE      CODE     ---       #36
      00FF49B0H   LINE      CODE     ---       #37
      00FF49B9H   LINE      CODE     ---       #38
      00FF49C4H   LINE      CODE     ---       #39
      00FF49CFH   LINE      CODE     ---       #40
      00FF49D5H   LINE      CODE     ---       #41
      00FF49D7H   LINE      CODE     ---       #42
      00FF49D7H   LINE      CODE     ---       #44
      00FF49E3H   LINE      CODE     ---       #45
      00FF49EAH   LINE      CODE     ---       #46
      00FF49F9H   LINE      CODE     ---       #47
      00FF4A02H   LINE      CODE     ---       #48
      00FF4A0DH   LINE      CODE     ---       #49
      00FF4A18H   LINE      CODE     ---       #50
      00FF4A25H   LINE      CODE     ---       #51
      00FF4A25H   LINE      CODE     ---       #52
      00FF4A25H   LINE      CODE     ---       #53
      ---         BLOCKEND  ---      ---       LVL=0

      00FF479EH   BLOCK     CODE     ---       LVL=0
      R7          REGSYM    ---      BYTE      pin
      WR0         REGSYM    ---      WORD      length
      REG=7       REGSYM    ---      ---       DAT
      00FF479EH   LINE      CODE     ---       #55
      00FF47A4H   LINE      CODE     ---       #57
      00FF47BEH   LINE      CODE     ---       #59
      00FF47BEH   LINE      CODE     ---       #61
      00FF47CBH   LINE      CODE     ---       #62
      00FF47D3H   LINE      CODE     ---       #63
      00FF47DEH   LINE      CODE     ---       #64
      00FF47E7H   LINE      CODE     ---       #65
      00FF47F2H   LINE      CODE     ---       #66
      00FF47FDH   LINE      CODE     ---       #67
      00FF4803H   LINE      CODE     ---       #68
      00FF4806H   LINE      CODE     ---       #69
      00FF4806H   LINE      CODE     ---       #71
      00FF4813H   LINE      CODE     ---       #72
      00FF481BH   LINE      CODE     ---       #73
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 101


      00FF4826H   LINE      CODE     ---       #74
      00FF482FH   LINE      CODE     ---       #75
      00FF483AH   LINE      CODE     ---       #76
      00FF4845H   LINE      CODE     ---       #77
      00FF484BH   LINE      CODE     ---       #78
      00FF484EH   LINE      CODE     ---       #79
      00FF484EH   LINE      CODE     ---       #81
      00FF485BH   LINE      CODE     ---       #82
      00FF4863H   LINE      CODE     ---       #83
      00FF486EH   LINE      CODE     ---       #84
      00FF4877H   LINE      CODE     ---       #85
      00FF4882H   LINE      CODE     ---       #86
      00FF488DH   LINE      CODE     ---       #87
      00FF4893H   LINE      CODE     ---       #88
      00FF4895H   LINE      CODE     ---       #89
      00FF4895H   LINE      CODE     ---       #91
      00FF48A2H   LINE      CODE     ---       #92
      00FF48AAH   LINE      CODE     ---       #93
      00FF48B9H   LINE      CODE     ---       #94
      00FF48C2H   LINE      CODE     ---       #95
      00FF48CDH   LINE      CODE     ---       #96
      00FF48D8H   LINE      CODE     ---       #97
      00FF48E5H   LINE      CODE     ---       #98
      00FF48E5H   LINE      CODE     ---       #99
      00FF48E5H   LINE      CODE     ---       #100
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       SPI
      00FF7DF0H   PUBLIC    CODE     ---       ESD_SPI_ReadByte
      00FF7389H   PUBLIC    CODE     ---       ESD_SPI_Init
      00FF751BH   PUBLIC    CODE     ---       ESD_SPI_ReadMultiBytes
      00FF7E1AH   PUBLIC    CODE     ---       ESD_SPI_WriteByte
      00FF7626H   PUBLIC    CODE     ---       ESD_SPI_WriteMultiBytes
      00FF7DFEH   PUBLIC    CODE     ---       ESD_SPI_RW
      00FF000EH   PUBLIC    CODE     ---       ESD_SPI_Deinit
      000000CFH   SFRSYM    DATA     BYTE      SPDAT
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 102


      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF7389H   BLOCK     CODE     ---       LVL=0
      WR6         REGSYM    ---      INT       spi_port
      00FF7389H   LINE      CODE     ---       #6
      00FF7389H   LINE      CODE     ---       #9
      00FF738CH   LINE      CODE     ---       #14
      00FF739EH   LINE      CODE     ---       #16
      00FF739EH   LINE      CODE     ---       #17
      00FF73A2H   LINE      CODE     ---       #18
      00FF73A4H   LINE      CODE     ---       #19
      00FF73A4H   LINE      CODE     ---       #20
      00FF73A7H   LINE      CODE     ---       #21
      00FF73A9H   LINE      CODE     ---       #22
      00FF73A9H   LINE      CODE     ---       #23
      00FF73ACH   LINE      CODE     ---       #24
      00FF73AEH   LINE      CODE     ---       #25
      00FF73AEH   LINE      CODE     ---       #26
      00FF73B1H   LINE      CODE     ---       #27
      00FF73B1H   LINE      CODE     ---       #28
      00FF73B1H   LINE      CODE     ---       #29
      00FF73B1H   LINE      CODE     ---       #30
      00FF73B1H   LINE      CODE     ---       #31
      00FF73B4H   LINE      CODE     ---       #32
      ---         BLOCKEND  ---      ---       LVL=0

      00FF000EH   BLOCK     CODE     ---       LVL=0
      00FF000EH   LINE      CODE     ---       #37
      00FF000EH   LINE      CODE     ---       #39
      00FF0011H   LINE      CODE     ---       #40
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7E1AH   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      out
      00FF7E1AH   LINE      CODE     ---       #46
      00FF7E1AH   LINE      CODE     ---       #48
      00FF7E1DH   LINE      CODE     ---       #49
      00FF7E22H   LINE      CODE     ---       #50
      00FF7E25H   LINE      CODE     ---       #51
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7DFEH   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      dat
      00FF7DFEH   LINE      CODE     ---       #58
      00FF7DFEH   LINE      CODE     ---       #60
      00FF7E01H   LINE      CODE     ---       #61
      00FF7E06H   LINE      CODE     ---       #62
      00FF7E09H   LINE      CODE     ---       #63
      00FF7E0BH   LINE      CODE     ---       #64
      ---         BLOCKEND  ---      ---       LVL=0
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 103



      00FF7DF0H   BLOCK     CODE     ---       LVL=0
      00FF7DF0H   LINE      CODE     ---       #70
      00FF7DF0H   LINE      CODE     ---       #72
      00FF7DF3H   LINE      CODE     ---       #73
      00FF7DF8H   LINE      CODE     ---       #74
      00FF7DFBH   LINE      CODE     ---       #75
      00FF7DFDH   LINE      CODE     ---       #76
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7626H   BLOCK     CODE     ---       LVL=0
      REG=7       REGSYM    ---      ---       tx_ptr
      WR8         REGSYM    ---      WORD      n
      00FF762AH   BLOCK     CODE     NEAR LAB  LVL=1
      WR6         REGSYM    ---      WORD      i
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7626H   LINE      CODE     ---       #83
      00FF762AH   LINE      CODE     ---       #84
      00FF762AH   LINE      CODE     ---       #86
      00FF762EH   LINE      CODE     ---       #88
      00FF7637H   LINE      CODE     ---       #89
      00FF763CH   LINE      CODE     ---       #90
      00FF763FH   LINE      CODE     ---       #91
      00FF7645H   LINE      CODE     ---       #92
      ---         BLOCKEND  ---      ---       LVL=0

      00FF751BH   BLOCK     CODE     ---       LVL=0
      REG=7       REGSYM    ---      ---       rx_ptr
      WR8         REGSYM    ---      WORD      n
      00FF751FH   BLOCK     CODE     NEAR LAB  LVL=1
      WR6         REGSYM    ---      WORD      i
      ---         BLOCKEND  ---      ---       LVL=1
      00FF751BH   LINE      CODE     ---       #99
      00FF751FH   LINE      CODE     ---       #100
      00FF751FH   LINE      CODE     ---       #102
      00FF7523H   LINE      CODE     ---       #104
      00FF7526H   LINE      CODE     ---       #105
      00FF752BH   LINE      CODE     ---       #106
      00FF752EH   LINE      CODE     ---       #107
      00FF7537H   LINE      CODE     ---       #108
      00FF753DH   LINE      CODE     ---       #109
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ?C?FPADD
      00FF01D4H   PUBLIC    CODE     ---       ?C?FPADD
      00FF01D1H   PUBLIC    CODE     ---       ?C?FPSUB

      ---         MODULE    ---      ---       ?C?FPMUL
      00FF028CH   PUBLIC    CODE     ---       ?C?FPMUL

      ---         MODULE    ---      ---       ?C?FPDIV
      00FF032CH   PUBLIC    CODE     ---       ?C?FPDIV

      ---         MODULE    ---      ---       ?C?FPCMP
      00FF03AFH   PUBLIC    CODE     ---       ?C?FPCMP
      00FF03ADH   PUBLIC    CODE     ---       ?C?FPCMP3

      ---         MODULE    ---      ---       ?C?FPNEG
      00FF03F3H   PUBLIC    CODE     ---       ?C?FPNEG

      ---         MODULE    ---      ---       ?C?FCAST
      00FF040BH   PUBLIC    CODE     NEAR LAB  ?C?FCASTC
      00FF0406H   PUBLIC    CODE     NEAR LAB  ?C?FCASTI
      00FF0401H   PUBLIC    CODE     NEAR LAB  ?C?FCASTL

L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 104


      ---         MODULE    ---      ---       ?C?CASTF
      00FF043EH   PUBLIC    CODE     NEAR LAB  ?C?CASTF

      ---         MODULE    ---      ---       SPRINTF
      000001AEH   PUBLIC    EDATA    ---       ?SPRINTF?BYTE
      00FF047BH   PUBLIC    CODE     NEAR LAB  SPRINTF

      ---         MODULE    ---      ---       VSPRINTF
      00000124H   PUBLIC    EDATA    ---       ?VSPRINTF?BYTE
      00FF049BH   PUBLIC    CODE     NEAR LAB  VSPRINTF

      ---         MODULE    ---      ---       FABS
      00FF04B7H   PUBLIC    CODE     NEAR LAB  FABS?_

      ---         MODULE    ---      ---       LOG10?_
      00FF04C6H   PUBLIC    CODE     NEAR LAB  LOG10?_

      ---         MODULE    ---      ---       SQRT?_
      00FF04D4H   PUBLIC    CODE     ---       SQRT?_

      ---         MODULE    ---      ---       ASIN?_
      00FF0564H   PUBLIC    CODE     ---       ASIN?_

      ---         MODULE    ---      ---       floor
      00FF6511H   PUBLIC    CODE     ---       floor?_

      ---         MODULE    ---      ---       ?C?FPGETOPN
      00FF0584H   PUBLIC    CODE     ---       ?C?FPGETOPN2
      00FF05C1H   PUBLIC    CODE     ---       ?C?FPNANRESULT
      00FF05C9H   PUBLIC    CODE     ---       ?C?FPOVERFLOW
      00FF059DH   PUBLIC    CODE     ---       ?C?FPRESULT
      00FF05B3H   PUBLIC    CODE     ---       ?C?FPRESULT2
      00FF05C6H   PUBLIC    CODE     ---       ?C?FPUNDERFLOW

      ---         MODULE    ---      ---       ?C?PRNFMT
      00FF0602H   PUBLIC    CODE     ---       ?C?PRNFMT

      ---         MODULE    ---      ---       LOG?_
      00FF0A3DH   PUBLIC    CODE     NEAR LAB  LOG?_

      ---         MODULE    ---      ---       ATAN?_
      00FF0B22H   PUBLIC    CODE     ---       ATAN?_

      ---         MODULE    ---      ---       ?C?FPCONVERT
      00FF0C02H   PUBLIC    CODE     ---       ?C?FPCONVERT
      00FF0CBAH   PUBLIC    CODE     ---       ?C?FPROUND

      ---         MODULE    ---      ---       ?C?FPSERIES
      00FF0CFDH   PUBLIC    CODE     ---       ?C?FP2SERIES
      00FF0D06H   PUBLIC    CODE     ---       ?C?FPSERIES

      ---         MODULE    ---      ---       ?C?FTNPWR
      00FF0D5AH   PUBLIC    CODE     ---       ?C?FTNPWR

      ---         MODULE    ---      ---       ?C_START
      00FF0000H   PUBLIC    CODE     ---       ?C?STARTUP
      00FF0000H   PUBLIC    CODE     ---       ?C_STARTUP

      ---         MODULE    ---      ---       ?C?INITEDATA
      00FF6B01H   PUBLIC    CODE     ---       ?C?INITEDATA

      ---         MODULE    ---      ---       ?C?SIDIV
      00FF0D91H   PUBLIC    CODE     ---       ?C?SIDIV

      ---         MODULE    ---      ---       ?C?LMUL
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:45:41  PAGE 105


      00FF0DC3H   PUBLIC    CODE     ---       ?C?LMUL

      ---         MODULE    ---      ---       ?C?ULDIV
      00FF0DD6H   PUBLIC    CODE     NEAR LAB  ?C?ULDIV
      00FF0DD4H   PUBLIC    CODE     NEAR LAB  ?C?ULIDIV

      ---         MODULE    ---      ---       ?C?SLDIV
      00FF0E27H   PUBLIC    CODE     NEAR LAB  ?C?SLDIV

      ---         MODULE    ---      ---       ABS
      00FF7D96H   PUBLIC    CODE     ---       abs?_

      ---         MODULE    ---      ---       LABS
      00FF7DA6H   PUBLIC    CODE     ---       labs?_

      ---         MODULE    ---      ---       STRLEN
      00FF7E0CH   PUBLIC    CODE     ---       strlen?_

      ---         MODULE    ---      ---       memcpy
      00FF74F6H   PUBLIC    CODE     ---       memcpy?_

      ---         MODULE    ---      ---       ?C?INITEDATA_END
      00FF8041H   PUBLIC    HCONST   WORD      ?C?INITEDATA_END



*** ERROR L127: UNRESOLVED EXTERNAL SYMBOL
    SYMBOL:  TM0_isr
    MODULE:  .\Objects\isr.obj (isr)

*** ERROR L127: UNRESOLVED EXTERNAL SYMBOL
    SYMBOL:  INT2_isr
    MODULE:  .\Objects\isr.obj (isr)

*** ERROR L128: REFERENCE MADE TO UNRESOLVED EXTERNAL
    SYMBOL:  TM0_isr
    MODULE:  .\Objects\isr.obj (isr)
    ADDRESS: FF709CH

*** ERROR L128: REFERENCE MADE TO UNRESOLVED EXTERNAL
    SYMBOL:  INT2_isr
    MODULE:  .\Objects\isr.obj (isr)
    ADDRESS: FF7235H

Program Size: data=10.0 edata+hdata=1020 xdata=192 const=681 code=32172
L251 RUN COMPLETE.  0 WARNING(S),  4 ERROR(S)
