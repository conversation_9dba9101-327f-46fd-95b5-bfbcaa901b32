L251 LINKER/LOCATER V4.66.93.0                                                          07/26/2025  14:58:12  PAGE 1


L251 LINKER/LOCATER V4.66.93.0, INVOKED BY:
D:\KEIL_V5\C251\BIN\L251.EXE .\Objects\main.obj, .\Objects\isr.obj, .\Objects\config.obj, ..\Driver\stc_usb_cdc_32g.lib,
>>  .\Objects\MPU6050.obj, .\Objects\OLED_SPI.obj, .\Objects\MKS.obj, .\Objects\D2Car.obj, .\Objects\ADC.obj, .\Objects\
>> Delay.obj, .\Objects\GPIO.obj, .\Objects\IIC.obj, .\Objects\INT.obj, .\Objects\PIT.obj, .\Objects\PWM.obj, .\Objects\
>> UART.obj, .\Objects\EEPROM.obj, .\Objects\CAN.obj, .\Objects\DMA.obj, .\Objects\SPI.obj TO .\Objects\mode PRINT (.\Li
>> stings\mode.map) CASE DISABLEWARNING (57) CLASSES (EDATA (0X0-0XFFF), HDATA (0X0-0XFFF))


CPU MODE:     251 SOURCE MODE
INTR FRAME:   2 BYTES SAVED ON INTERRUPT
MEMORY MODEL: XSMALL WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\main.obj (main)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\isr.obj (isr)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\config.obj (config)
         COMMENT TYPE 0: C251 V5.60.0
  ..\Driver\stc_usb_cdc_32g.lib (usb)
         COMMENT TYPE 0: C251 V5.60.0
  ..\Driver\stc_usb_cdc_32g.lib (usb_req_class)
         COMMENT TYPE 0: C251 V5.60.0
  ..\Driver\stc_usb_cdc_32g.lib (usb_req_std)
         COMMENT TYPE 0: C251 V5.60.0
  ..\Driver\stc_usb_cdc_32g.lib (usb_req_vendor)
         COMMENT TYPE 0: C251 V5.60.0
  ..\Driver\stc_usb_cdc_32g.lib (util)
         COMMENT TYPE 0: C251 V5.60.0
  ..\Driver\stc_usb_cdc_32g.lib (usb_desc)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\MPU6050.obj (MPU6050)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\OLED_SPI.obj (OLED_SPI)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\MKS.obj (MKS)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\D2Car.obj (D2Car)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\ADC.obj (ADC)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\Delay.obj (Delay)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\GPIO.obj (GPIO)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\IIC.obj (IIC)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\INT.obj (INT)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\PIT.obj (PIT)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\PWM.obj (PWM)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\UART.obj (UART)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\EEPROM.obj (EEPROM)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\CAN.obj (CAN)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\DMA.obj (DMA)
         COMMENT TYPE 0: C251 V5.60.0
  .\Objects\SPI.obj (SPI)
         COMMENT TYPE 0: C251 V5.60.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FPADD)
         COMMENT TYPE 0: A251 V4.69.6.0
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 2


  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FPMUL)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FPDIV)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FPCMP)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FPNEG)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FCAST)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?CASTF)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (SPRINTF)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (VSPRINTF)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (FABS)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (LOG10?_)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (SQRT?_)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (ASIN?_)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (floor)
         COMMENT TYPE 0: C251 V5.58.7
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FPGETOPN)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?PRNFMT)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (LOG?_)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (ATAN?_)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FPCONVERT)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FPSERIES)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SFPXS.LIB (?C?FTNPWR)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (?C_START)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (?C?INITEDATA)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (?C?SIDIV)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (?C?LMUL)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (?C?ULDIV)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (?C?SLDIV)
         COMMENT TYPE 0: A251 V4.69.6.0
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (ABS)
         COMMENT TYPE 0: C251 V5.58.7
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (LABS)
         COMMENT TYPE 0: C251 V5.58.7
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (STRLEN)
         COMMENT TYPE 0: C251 V5.58.7
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (memcpy)
         COMMENT TYPE 0: C251 V5.58.7
  D:\KEIL_V5\C251\LIB\C2SXS.LIB (?C?INITEDATA_END)
         COMMENT TYPE 0: A251 V4.69.6.0


ACTIVE MEMORY CLASSES OF MODULE:  .\Objects\mode (main)
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 3



BASE        START       END         USED      MEMORY CLASS
==========================================================
000000H     000000H     000FFFH     0003F8H   EDATA
000000H     000000H     000FFFH               HDATA
FF0000H     FF0000H     FFFFFFH     007F64H   CODE
000020H.0   000020H.0   00002FH.7   000002H.4 BIT
000000H     FF0000H     FFFFFFH     000294H   HCONST
010000H     010000H     01FFFFH     0000C0H   XDATA
FF0000H     FF0000H     FFFFFFH     000004H   CONST
000000H     000000H     00007FH     000008H   DATA


MEMORY MAP OF MODULE:  .\Objects\mode (main)


START     STOP      LENGTH    ALIGN  RELOC    MEMORY CLASS   SEGMENT NAME
=========================================================================
000000H   000007H   000008H   ---    AT..     DATA           "REG BANK 0"
000008H   00001FH   000018H   BYTE   UNIT     EDATA          ?ED?IMUUPDATE?MPU6050
000020H.0 000020H.5 000000H.6 BIT    UNIT     BIT            ?BI?MAIN
000020H.6 000021H.0 000000H.3 BIT    UNIT     BIT            ?BI?USB
000021H.1 000021H.2 000000H.2 BIT    UNIT     BIT            ?BI?CONFIG
000021H.3 000021H.3 000000H.1 BIT    UNIT     BIT            ?BI?CAN_MKS_CONTROL?MKS
000021H.4 000021H.4 000000H.1 BIT    UNIT     BIT            ?BI?ESD_M1_POS_CONTROL?D2CAR
000021H.5 000021H.5 000000H.1 BIT    UNIT     BIT            ?BI?GET_IO?GPIO
000021H.6 000021H.6 000000H.1 BIT    UNIT     BIT            ?BI?OUT_IO?GPIO
000021H.7 000021H.7 000000H.1 BIT    UNIT     BIT            ?BI?CAN
000022H.0 000022H.0 000000H.1 BIT    UNIT     BIT            ?BI?CANINIT?CAN
000022H.1 000022H.1 000000H.1 BIT    UNIT     BIT            ?BI?CANREADMSG?CAN
000022H.2 000022H.2 000000H.1 BIT    UNIT     BIT            ?BI?CANSENDMSG?CAN
000022H.3 000022H.3 000000H.1 BIT    UNIT     BIT            _BIT_GROUP_
000022H.4 000022H   000000H.4 ---    ---      **GAP**
000023H   0000B5H   000093H   BYTE   UNIT     EDATA          ?ED?MPU6050
0000B6H   0000FCH   000047H   BYTE   UNIT     EDATA          ?ED?D2CAR
0000FDH   000128H   00002CH   BYTE   UNIT     EDATA          ?ED?SEG7_SHOWSTRING?UTIL
000129H   000154H   00002CH   BYTE   UNIT     EDATA          ?ED?PRINTF_HID?UTIL
000155H   00017EH   00002AH   BYTE   UNIT     EDATA          ?ED?OLED_SHOWFLOAT?OLED_SPI
00017FH   0001A6H   000028H   BYTE   UNIT     EDATA          ?ED?SPRINTF
0001A7H   0001CDH   000027H   BYTE   UNIT     EDATA          ?ED?OLED_SHOWINT?OLED_SPI
0001CEH   0001F3H   000026H   BYTE   UNIT     EDATA          ?ED?MAIN
0001F4H   000210H   00001DH   BYTE   UNIT     EDATA          ?ED?CONFIG
000211H   000226H   000016H   BYTE   UNIT     EDATA          ?ED?MKS
000227H   00023BH   000015H   BYTE   UNIT     EDATA          ?ED?CANREADMSG?CAN
00023CH   00024FH   000014H   BYTE   UNIT     EDATA          ?ED?USB
000250H   00025FH   000010H   BYTE   UNIT     EDATA          ?ED?UART_SEND_FLOAT?UART
000260H   00026BH   00000CH   BYTE   UNIT     EDATA          ?ED?UART_SEND_INT?UART
00026CH   000277H   00000CH   BYTE   UNIT     EDATA          _EDATA_GROUP_
000278H   000281H   00000AH   BYTE   UNIT     EDATA          ?ED?PIT
000282H   00028AH   000009H   BYTE   UNIT     EDATA          ?ED?CANSENDMSG?CAN
00028BH   000292H   000008H   BYTE   UNIT     EDATA          ?ED?REVERSE4?UTIL
000293H   00029AH   000008H   BYTE   UNIT     EDATA          ?ED?MPU6050_GET_GYRO?MPU6050
00029BH   0002A2H   000008H   BYTE   UNIT     EDATA          ?ED?OLED_SHOWNUM?OLED_SPI
0002A3H   0002AAH   000008H   BYTE   UNIT     EDATA          ?ED?OLED_DRAWBMP?OLED_SPI
0002ABH   0002B2H   000008H   BYTE   UNIT     EDATA          ?ED?GPIO_ISR_INIT?GPIO
0002B3H   0002B9H   000007H   BYTE   UNIT     EDATA          ?ED?USB_REQ_CLASS
0002BAH   0002BFH   000006H   BYTE   UNIT     EDATA          ?ED?MPU6050_GET_ACC?MPU6050
0002C0H   0002C4H   000005H   BYTE   UNIT     EDATA          ?ED?USB_SENDDATA?USB
0002C5H   0002C9H   000005H   BYTE   UNIT     EDATA          ?ED?LCD12864_SETBUFFER?UTIL
0002CAH   0002CEH   000005H   BYTE   UNIT     EDATA          ?ED?OLED12864_SETBUFFER?UTIL
0002CFH   0002D3H   000005H   BYTE   UNIT     EDATA          ?ED?OLED_SHOWCHAR?OLED_SPI
0002D4H   0002D8H   000005H   BYTE   UNIT     EDATA          ?ED?CAN
0002D9H   0002DCH   000004H   BYTE   UNIT     EDATA          ?ED?LIMIT_INT?CONFIG
0002DDH   0002E0H   000004H   BYTE   UNIT     EDATA          ?ED?LIMIT_FLOAT?CONFIG
0002E1H   0002E4H   000004H   BYTE   UNIT     EDATA          ?ED?SEG7_SHOWLONG?UTIL
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 4


0002E5H   0002E8H   000004H   BYTE   UNIT     EDATA          ?ED?OLED_SHOWSTRING?OLED_SPI
0002E9H   0002ECH   000004H   BYTE   UNIT     EDATA          ?ED?OLED_SHOWCHINESE?OLED_SPI
0002EDH   0002F0H   000004H   BYTE   UNIT     EDATA          ?ED?ESD_WRITE_IIC?IIC
0002F1H   0002F4H   000004H   BYTE   UNIT     EDATA          ?ED?ESD_READ_IIC?IIC
0002F5H   0002F8H   000004H   BYTE   UNIT     EDATA          ?ED?UART
0002F9H   0002FCH   000004H   BYTE   UNIT     EDATA          ?ED?VSPRINTF
0002FDH   0002FFH   000003H   BYTE   UNIT     EDATA          ?ED?MPU6050_SIMIIC_READ_REGS?MPU6050
000300H   000301H   000002H   BYTE   UNIT     EDATA          ?ED?LCD12864_SHOWSTRING?UTIL
000302H   000302H   000001H   BYTE   UNIT     EDATA          ?ED?UART_SEND_STRING?UART
000303H   000402H   000100H   BYTE   UNIT     EDATA          ?STACK
000403H   00FFFFH   00FBFDH   ---    ---      **GAP**
010000H   0100BFH   0000C0H   BYTE   INSEG    XDATA          ?XD?USB
0100C0H   FEFFFFH   FDFF40H   ---    ---      **GAP**
FF0000H   FF0002H   000003H   ---    OFFS..   CODE           ?CO?start251?4
FF0003H   FF0005H   000003H   ---    OFFS..   CODE           ?PR?IV?0
FF0006H   FF000AH   000005H   BYTE   INSEG    CODE           ?PR?SET_MOTOR_SPEED?MAIN
FF000BH   FF000DH   000003H   ---    OFFS..   CODE           ?PR?IV?1
FF000EH   FF0012H   000005H   BYTE   INSEG    CODE           ?PR?SQ?CONFIG
FF0013H   FF0015H   000003H   ---    OFFS..   CODE           ?PR?IV?2
FF0016H   FF0019H   000004H   BYTE   INSEG    CODE           ?PR?ESD_SPI_DEINIT?SPI
FF001AH   FF001AH   000001H   BYTE   INSEG    CODE           ?PR?IIC_I?ISR
FF001BH   FF001DH   000003H   ---    OFFS..   CODE           ?PR?IV?3
FF001EH   FF0020H   000003H   BYTE   INSEG    CODE           ?PR?HID_ISR?CONFIG
FF0021H   FF0021H   000001H   BYTE   INSEG    CODE           ?PR?CMP_I?ISR
FF0022H   FF0022H   000001H   BYTE   INSEG    CODE           ?PR?LVD_I?ISR
FF0023H   FF0025H   000003H   ---    OFFS..   CODE           ?PR?IV?4
FF0026H   FF0028H   000003H   BYTE   INSEG    CODE           ?PR?USB_SET_DESCRIPTOR?USB_REQ_STD
FF0029H   FF0029H   000001H   BYTE   INSEG    CODE           ?PR?SPI_I?ISR
FF002AH   FF002AH   000001H   BYTE   INSEG    CODE           ?PR?DMA_ADC_ISR?ISR
FF002BH   FF002DH   000003H   ---    OFFS..   CODE           ?PR?IV?5
FF002EH   FF0030H   000003H   BYTE   INSEG    CODE           ?PR?USB_SYNCH_FRAME?USB_REQ_STD
FF0031H   FF0031H   000001H   BYTE   INSEG    CODE           ?PR?DMA_M2M_ISR?ISR
FF0032H   FF0032H   000001H   BYTE   INSEG    CODE           ?PR?INT0_I?ISR
FF0033H   FF0035H   000003H   ---    OFFS..   CODE           ?PR?IV?6
FF0036H   FF0042H   00000DH   BYTE   INSEG    CODE           ?PR?USB_SETUP_STATUS?USB
FF0043H   FF0045H   000003H   ---    OFFS..   CODE           ?PR?IV?8
FF0046H   FF0048H   000003H   BYTE   INSEG    CODE           ?PR?USB_REQ_VENDOR?USB_REQ_VENDOR
FF0049H   FF0049H   000001H   BYTE   INSEG    CODE           ?PR?INT1_I?ISR
FF004AH   FF004AH   000001H   BYTE   INSEG    CODE           ?PR?INT3_I?ISR
FF004BH   FF004DH   000003H   ---    OFFS..   CODE           ?PR?IV?9
FF004EH   FF004EH   000001H   BYTE   INSEG    CODE           ?PR?INT4_I?ISR
FF004FH   FF004FH   000001H   BYTE   INSEG    CODE           ?PR?DMA_SPI_ISR?ISR
FF0050H   FF0050H   000001H   BYTE   INSEG    CODE           ?PR?TIMER1_I?ISR
FF0051H   FF0051H   000001H   BYTE   INSEG    CODE           ?PR?TIMER2_I?ISR
FF0052H   FF0052H   000001H   BYTE   INSEG    CODE           ?PR?PWMA_I?ISR
FF0053H   FF0055H   000003H   ---    OFFS..   CODE           ?PR?IV?10
FF0056H   FF0056H   000001H   BYTE   INSEG    CODE           ?PR?TIMER3_I?ISR
FF0057H   FF0057H   000001H   BYTE   INSEG    CODE           ?PR?PWMB_I?ISR
FF0058H   FF0058H   000001H   BYTE   INSEG    CODE           ?PR?TIMER4_I?ISR
FF0059H   FF0059H   000001H   BYTE   INSEG    CODE           ?PR?ADC_I?ISR
FF005AH   FF005AH   000001H   BYTE   INSEG    CODE           ?PR?USB_SUSPEND?USB
FF005BH   FF005DH   000003H   ---    OFFS..   CODE           ?PR?IV?11
FF005EH   FF005EH   000001H   BYTE   INSEG    CODE           ?PR?USB_RESUME?USB
FF005FH   FF005FH   000001H   BYTE   INSEG    CODE           ?PR?MKS_ALL_RUN?MKS
FF0060H   FF0062H   000003H   ---    ---      **GAP**
FF0063H   FF0065H   000003H   ---    OFFS..   CODE           ?PR?IV?12
FF0066H   FF0082H   00001DH   BYTE   INSEG    CODE           ?PR?DMA_UART1_TXD_ISR?ISR
FF0083H   FF0085H   000003H   ---    OFFS..   CODE           ?PR?IV?16
FF0086H   FF0089H   000004H   BYTE   UNIT     CONST          ?CO?PRINTF
FF008AH   FF008AH   000001H   ---    ---      **GAP**
FF008BH   FF008DH   000003H   ---    OFFS..   CODE           ?PR?IV?17
FF008EH   FF0092H   000005H   ---    ---      **GAP**
FF0093H   FF0095H   000003H   ---    OFFS..   CODE           ?PR?IV?18
FF0096H   FF009AH   000005H   ---    ---      **GAP**
FF009BH   FF009DH   000003H   ---    OFFS..   CODE           ?PR?IV?19
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 5


FF009EH   FF00A2H   000005H   ---    ---      **GAP**
FF00A3H   FF00A5H   000003H   ---    OFFS..   CODE           ?PR?IV?20
FF00A6H   FF00AAH   000005H   ---    ---      **GAP**
FF00ABH   FF00ADH   000003H   ---    OFFS..   CODE           ?PR?IV?21
FF00AEH   FF00C2H   000015H   BYTE   INSEG    CODE           ?PR?USB_BULK_INTR_IN?USB
FF00C3H   FF00C5H   000003H   ---    OFFS..   CODE           ?PR?IV?24
FF00C6H   FF00CAH   000005H   ---    ---      **GAP**
FF00CBH   FF00CDH   000003H   ---    OFFS..   CODE           ?PR?IV?25
FF00CEH   FF00D2H   000005H   ---    ---      **GAP**
FF00D3H   FF00D5H   000003H   ---    OFFS..   CODE           ?PR?IV?26
FF00D6H   FF00DAH   000005H   ---    ---      **GAP**
FF00DBH   FF00DDH   000003H   ---    OFFS..   CODE           ?PR?IV?27
FF00DEH   FF00E2H   000005H   ---    ---      **GAP**
FF00E3H   FF00E5H   000003H   ---    OFFS..   CODE           ?PR?IV?28
FF00E6H   FF00EAH   000005H   ---    ---      **GAP**
FF00EBH   FF00EDH   000003H   ---    OFFS..   CODE           ?PR?IV?29
FF00EEH   FF012AH   00003DH   BYTE   INSEG    CODE           ?PR?SEG7_SHOWLONG?UTIL
FF012BH   FF012DH   000003H   ---    OFFS..   CODE           ?PR?IV?37
FF012EH   FF0132H   000005H   ---    ---      **GAP**
FF0133H   FF0135H   000003H   ---    OFFS..   CODE           ?PR?IV?38
FF0136H   FF013AH   000005H   ---    ---      **GAP**
FF013BH   FF013DH   000003H   ---    OFFS..   CODE           ?PR?IV?39
FF013EH   FF0142H   000005H   ---    ---      **GAP**
FF0143H   FF0145H   000003H   ---    OFFS..   CODE           ?PR?IV?40
FF0146H   FF014AH   000005H   ---    ---      **GAP**
FF014BH   FF014DH   000003H   ---    OFFS..   CODE           ?PR?IV?41
FF014EH   FF0152H   000005H   ---    ---      **GAP**
FF0153H   FF0155H   000003H   ---    OFFS..   CODE           ?PR?IV?42
FF0156H   FF015AH   000005H   ---    ---      **GAP**
FF015BH   FF015DH   000003H   ---    OFFS..   CODE           ?PR?IV?43
FF015EH   FF0162H   000005H   ---    ---      **GAP**
FF0163H   FF0165H   000003H   ---    OFFS..   CODE           ?PR?IV?44
FF0166H   FF017AH   000015H   BYTE   INSEG    CODE           ?PR?USB_READ_REG?USB
FF017BH   FF017DH   000003H   ---    OFFS..   CODE           ?PR?IV?47
FF017EH   FF0182H   000005H   ---    ---      **GAP**
FF0183H   FF0185H   000003H   ---    OFFS..   CODE           ?PR?IV?48
FF0186H   FF018AH   000005H   ---    ---      **GAP**
FF018BH   FF018DH   000003H   ---    OFFS..   CODE           ?PR?IV?49
FF018EH   FF0192H   000005H   ---    ---      **GAP**
FF0193H   FF0195H   000003H   ---    OFFS..   CODE           ?PR?IV?50
FF0196H   FF019AH   000005H   ---    ---      **GAP**
FF019BH   FF019DH   000003H   ---    OFFS..   CODE           ?PR?IV?51
FF019EH   FF01A2H   000005H   ---    ---      **GAP**
FF01A3H   FF01A5H   000003H   ---    OFFS..   CODE           ?PR?IV?52
FF01A6H   FF01AAH   000005H   ---    ---      **GAP**
FF01ABH   FF01ADH   000003H   ---    OFFS..   CODE           ?PR?IV?53
FF01AEH   FF01B2H   000005H   ---    ---      **GAP**
FF01B3H   FF01B5H   000003H   ---    OFFS..   CODE           ?PR?IV?54
FF01B6H   FF01BAH   000005H   ---    ---      **GAP**
FF01BBH   FF01BDH   000003H   ---    OFFS..   CODE           ?PR?IV?55
FF01BEH   FF01C2H   000005H   ---    ---      **GAP**
FF01C3H   FF01C5H   000003H   ---    OFFS..   CODE           ?PR?IV?56
FF01C6H   FF01CAH   000005H   ---    ---      **GAP**
FF01CBH   FF01CDH   000003H   ---    OFFS..   CODE           ?PR?IV?57
FF01CEH   FF0E5AH   000C8DH   BYTE   UNIT     CODE           ?C?LIB_CODE
FF0E5BH   FF1792H   000938H   BYTE   UNIT     CODE           ?CO?OLED_SPI
FF1793H   FF1FAFH   00081DH   BYTE   INSEG    CODE           ?PR?UART_SEND_FLOAT?UART
FF1FB0H   FF2738H   000789H   BYTE   INSEG    CODE           ?PR?UART_SEND_INT?UART
FF2739H   FF2D0BH   0005D3H   BYTE   INSEG    CODE           ?PR?GPIO_ISR_INIT?GPIO
FF2D0CH   FF30BDH   0003B2H   BYTE   INSEG    CODE           ?PR?IMUUPDATE?MPU6050
FF30BEH   FF3409H   00034CH   BYTE   INSEG    CODE           ?PR?PWM_INIT?PWM
FF340AH   FF3717H   00030EH   BYTE   INSEG    CODE           ?PR?GPIO_INIT_PIN?GPIO
FF3718H   FF399EH   000287H   BYTE   INSEG    CODE           ?PR?GET_IO?GPIO
FF399FH   FF3BC8H   00022AH   BYTE   INSEG    CODE           ?PR?PWM_CHANGE?PWM
FF3BC9H   FF3DE9H   000221H   BYTE   INSEG    CODE           ?PR?OUT_IO?GPIO
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 6


FF3DEAH   FF3FF7H   00020EH   BYTE   INSEG    CODE           ?PR?CANINIT?CAN
FF3FF8H   FF41A4H   0001ADH   BYTE   INSEG    CODE           ?PR?KALMAN_FILTER?MPU6050
FF41A5H   FF434BH   0001A7H   BYTE   INSEG    CODE           ?PR?GPIO_PULL_PIN?GPIO
FF434CH   FF44C9H   00017EH   BYTE   INSEG    CODE           ?PR?GPIO_INIT_8PIN?GPIO
FF44CAH   FF4635H   00016CH   BYTE   INSEG    CODE           ?PR?UART_INIT?UART
FF4636H   FF479DH   000168H   BYTE   INSEG    CODE           ?PR?CANSENDMSG?CAN
FF479EH   FF48E5H   000148H   BYTE   INSEG    CODE           ?PR?DMA_RXD_INIT?DMA
FF48E6H   FF4A25H   000140H   BYTE   INSEG    CODE           ?PR?DMA_TXD_INIT?DMA
FF4A26H   FF4B42H   00011DH   BYTE   INSEG    CODE           ?PR?SET_CLK?CONFIG
FF4B43H   FF4C5AH   000118H   BYTE   INSEG    CODE           ?PR?MAIN?MAIN
FF4C5BH   FF4D6DH   000113H   BYTE   INSEG    CODE           ?PR?CAR_ADD_ANGLE?D2CAR
FF4D6EH   FF4E7AH   00010DH   BYTE   INSEG    CODE           ?PR?GPIO_ISR_DEINIT?GPIO
FF4E7BH   FF4F7EH   000104H   BYTE   INSEG    CODE           ?PR?OLED_SHOWFLOAT?OLED_SPI
FF4F7FH   FF5081H   000103H   BYTE   INSEG    CODE           ?PR?USB_GET_STATUS?USB_REQ_STD
FF5082H   FF5183H   000102H   BYTE   INSEG    CODE           ?PR?GRIPPER_CONTROL?MAIN
FF5184H   FF526BH   0000E8H   BYTE   INSEG    CODE           ?PR?OLED_SHOWNUM?OLED_SPI
FF526CH   FF534FH   0000E4H   BYTE   INSEG    CODE           ?PR?CAN_MKS_CONTROL?MKS
FF5350H   FF5427H   0000D8H   BYTE   INSEG    CODE           ?PR?MKS_PID_SET?MKS
FF5428H   FF54FEH   0000D7H   BYTE   INSEG    CODE           ?PR?OLED_INIT?OLED_SPI
FF54FFH   FF55C7H   0000C9H   BYTE   INSEG    CODE           ?PR?MKS_HOME?MKS
FF55C8H   FF568BH   0000C4H   BYTE   INSEG    CODE           ?PR?USB_OUT_EP1?USB
FF568CH   FF574EH   0000C3H   BYTE   INSEG    CODE           ?PR?OLED_SHOWCHAR?OLED_SPI
FF574FH   FF580FH   0000C1H   BYTE   INSEG    CODE           ?PR?CAR_PID_CONTROL?D2CAR
FF5810H   FF58C8H   0000B9H   BYTE   INSEG    CODE           ?PR?CANREADMSG?CAN
FF58C9H   FF596DH   0000A5H   BYTE   INSEG    CODE           ?PR?USB_SET_CONFIGURATION?USB_REQ_STD
FF596EH   FF5A0DH   0000A0H   BYTE   INSEG    CODE           ?PR?ESD_INIT_IIC?IIC
FF5A0EH   FF5AACH   00009FH   BYTE   INSEG    CODE           ?PR?USB_CLEAR_FEATURE?USB_REQ_STD
FF5AADH   FF5B4BH   00009FH   BYTE   INSEG    CODE           ?PR?USB_GET_DESCRIPTOR?USB_REQ_STD
FF5B4CH   FF5BE4H   000099H   BYTE   INSEG    CODE           ?PR?USB_SET_FEATURE?USB_REQ_STD
FF5BE5H   FF5C7CH   000098H   BYTE   INSEG    CODE           ?PR?OLED12864_SETBUFFER?UTIL
FF5C7DH   FF5D14H   000098H   BYTE   INSEG    CODE           ?PR?LCD12864_SETBUFFER?UTIL
FF5D15H   FF5DACH   000098H   BYTE   INSEG    CODE           ?PR?OLED_SHOWINT?OLED_SPI
FF5DADH   FF5E43H   000097H   BYTE   INSEG    CODE           ?PR?OLED_DRAWBMP?OLED_SPI
FF5E44H   FF5EDAH   000097H   BYTE   INSEG    CODE           ?PR?CAN_MKS_SPD_CONTROL?MKS
FF5EDBH   FF5F70H   000096H   BYTE   INSEG    CODE           ?PR?CANREADFIFO?CAN
FF5F71H   FF6003H   000093H   BYTE   INSEG    CODE           ?PR?PIT_INIT_US?PIT
FF6004H   FF608FH   00008CH   BYTE   INSEG    CODE           ?PR?PIT_INIT_MS?PIT
FF6090H   FF6119H   00008AH   BYTE   INSEG    CODE           ?PR?USB_SETUP?USB
FF611AH   FF619DH   000084H   BYTE   INSEG    CODE           ?PR?USB_ISR?USB
FF619EH   FF6220H   000083H   BYTE   UNIT     CODE           ?CO?USB_DESC
FF6221H   FF62A2H   000082H   BYTE   INSEG    CODE           ?PR?ESD_READ_IIC?IIC
FF62A3H   FF631EH   00007CH   BYTE   INSEG    CODE           ?PR?OLED_SHOWCHINESE?OLED_SPI
FF631FH   FF6391H   000073H   BYTE   INSEG    CODE           ?PR?OLED_SHOWSTRING?OLED_SPI
FF6392H   FF6401H   000070H   BYTE   INSEG    CODE           ?PR?SYSTEM_INIT?CONFIG
FF6402H   FF6470H   00006FH   BYTE   INSEG    CODE           ?PR?USB_SENDDATA?USB
FF6471H   FF64DFH   00006FH   BYTE   INSEG    CODE           ?PR?ESD_M1_POS_CONTROL?D2CAR
FF64E0H   FF654CH   00006DH   BYTE   INSEG    CODE           ?PR?ADC_INIT?ADC
FF654DH   FF65B8H   00006CH   BYTE   INSEG    CODE           ?PR?MPU6050_IIC_INIT?MPU6050
FF65B9H   FF6623H   00006BH   BYTE   INSEG    CODE           ?PR?CAN1_I?ISR
FF6624H   FF668EH   00006BH   BYTE   INSEG    CODE           ?PR?CAN2_I?ISR
FF668FH   FF66F9H   00006BH   BYTE   INSEG    CODE           ?PR?FLOOR?_?FLOOR
FF66FAH   FF6760H   000067H   BYTE   INSEG    CODE           ?PR?USB_CTRL_IN?USB
FF6761H   FF67C6H   000066H   BYTE   INSEG    CODE           ?PR?GPIO_INIT_ALLPIN?GPIO
FF67C7H   FF6827H   000061H   BYTE   INSEG    CODE           ?PR?REVERSE4?UTIL
FF6828H   FF6885H   00005EH   BYTE   INSEG    CODE           ?PR?USB_INIT?USB
FF6886H   FF68E1H   00005CH   BYTE   INSEG    CODE           ?PR?USB_REQ_STD?USB_REQ_STD
FF68E2H   FF6939H   000058H   BYTE   INSEG    CODE           ?PR?ESD_WRITE_IIC?IIC
FF693AH   FF698EH   000055H   BYTE   INSEG    CODE           ?PR?MPU6050_SIMIIC_READ_REGS?MPU6050
FF698FH   FF69E2H   000054H   BYTE   INSEG    CODE           ?PR?USB_GET_CONFIGURATION?USB_REQ_STD
FF69E3H   FF6A36H   000054H   BYTE   INSEG    CODE           ?PR?MPU6050_INIT?MPU6050
FF6A37H   FF6A89H   000053H   BYTE   INSEG    CODE           ?PR?PIT_COUNT_CLEAN?PIT
FF6A8AH   FF6ADAH   000051H   BYTE   INSEG    CODE           ?PR?UART_SEND_BYTE?UART
FF6ADBH   FF6AEDH   000013H   BYTE   UNIT     CODE           ?C_C51STARTUP
FF6AEEH   FF6AFFH   000012H   BYTE   UNIT     CODE           ?C_C51STARTUP?1
FF6B00H   FF6B28H   000029H   BYTE   UNIT     CODE           ?C_C51STARTUP?2
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 7


FF6B29H   FF6B2BH   000003H   BYTE   UNIT     CODE           ?C_C51STARTUP?3
FF6B2CH   FF6B7BH   000050H   BYTE   INSEG    CODE           ?PR?MPU6050_GET_GYRO?MPU6050
FF6B7CH   FF6BCAH   00004FH   BYTE   INSEG    CODE           ?PR?LCD12864_SHOWPICTURE?UTIL
FF6BCBH   FF6C17H   00004DH   BYTE   INSEG    CODE           ?PR?USB_CTRL_OUT?USB
FF6C18H   FF6C64H   00004DH   BYTE   INSEG    CODE           ?PR?PIT_COUNT_GET?PIT
FF6C65H   FF6CB0H   00004CH   BYTE   INSEG    CODE           ?PR?PIT_INIT_ENCODER?PIT
FF6CB1H   FF6CFAH   00004AH   BYTE   INSEG    CODE           ?PR?USB_SET_ADDRESS?USB_REQ_STD
FF6CFBH   FF6D43H   000049H   BYTE   INSEG    CODE           ?PR?SEG7_SHOWSTRING?UTIL
FF6D44H   FF6D8BH   000048H   BYTE   INSEG    CODE           ?PR?USB_RESET?USB
FF6D8CH   FF6DD2H   000047H   BYTE   INSEG    CODE           ?PR?LED40_SENDDATA?UTIL
FF6DD3H   FF6E19H   000047H   BYTE   INSEG    CODE           ?PR?LED64_SENDDATA?UTIL
FF6E1AH   FF6E5DH   000044H   BYTE   INSEG    CODE           ?PR?ADC_GET?ADC
FF6E5EH   FF6E9FH   000042H   BYTE   INSEG    CODE           ?PR?DMA_UART1_RXD_ISR?ISR
FF6EA0H   FF6EE1H   000042H   BYTE   INSEG    CODE           ?PR?DMA_UART2_RXD_ISR?ISR
FF6EE2H   FF6F23H   000042H   BYTE   INSEG    CODE           ?PR?DMA_UART3_RXD_ISR?ISR
FF6F24H   FF6F65H   000042H   BYTE   INSEG    CODE           ?PR?DMA_UART4_RXD_ISR?ISR
FF6F66H   FF6FA6H   000041H   BYTE   INSEG    CODE           ?PR?YIJIELVBO?MPU6050
FF6FA7H   FF6FE6H   000040H   BYTE   INSEG    CODE           ?PR?OLED12864_SHOWPICTURE?UTIL
FF6FE7H   FF7026H   000040H   BYTE   INSEG    CODE           ?PR?MPU6050_GET_ACC?MPU6050
FF7027H   FF7065H   00003FH   BYTE   INSEG    CODE           ?PR?KEY_RST?CONFIG
FF7066H   FF70A3H   00003EH   BYTE   INSEG    CODE           ?PR?USB_GET_INTERFACE?USB_REQ_STD
FF70A4H   FF70E0H   00003DH   BYTE   INSEG    CODE           ?PR?LCD12864_SHOWSTRING?UTIL
FF70E1H   FF711BH   00003BH   BYTE   INSEG    CODE           ?PR?MPU6050_READ_CH?MPU6050
FF711CH   FF7155H   00003AH   BYTE   INSEG    CODE           ?PR?USB_READ_FIFO?_?USB
FF7156H   FF718FH   00003AH   BYTE   INSEG    CODE           ?PR?OLED12864_SCROLLRIGHT?UTIL
FF7190H   FF71C9H   00003AH   BYTE   INSEG    CODE           ?PR?OLED12864_SCROLLUP?UTIL
FF71CAH   FF7202H   000039H   BYTE   INSEG    CODE           ?PR?OLED12864_SCROLLLEFT?UTIL
FF7203H   FF7238H   000036H   BYTE   INSEG    CODE           ?PR?TIMER0_I?ISR
FF7239H   FF726EH   000036H   BYTE   INSEG    CODE           ?PR?OLED_CLEAR?OLED_SPI
FF726FH   FF72A3H   000035H   BYTE   INSEG    CODE           ?PR?SEG7_SHOWCODE?UTIL
FF72A4H   FF72D8H   000035H   BYTE   INSEG    CODE           ?PR?REVERSE2?UTIL
FF72D9H   FF730BH   000033H   BYTE   INSEG    CODE           ?PR?SEG7_SHOWFLOAT?UTIL
FF730CH   FF733EH   000033H   BYTE   INSEG    CODE           ?PR?ESD_M1_SPD_CONTROL?D2CAR
FF733FH   FF7370H   000032H   BYTE   INSEG    CODE           ?PR?INT_INIT?INT
FF7371H   FF73A1H   000031H   BYTE   INSEG    CODE           ?PR?ESD_M1_PWM_CONTROL?D2CAR
FF73A2H   FF73D1H   000030H   BYTE   INSEG    CODE           ?PR?INT2_I?ISR
FF73D2H   FF7400H   00002FH   BYTE   INSEG    CODE           ?PR?IR_DETECTION?MAIN
FF7401H   FF742FH   00002FH   BYTE   INSEG    CODE           ?PR?USB_IN?USB
FF7430H   FF745DH   00002EH   BYTE   INSEG    CODE           ?PR?MPU6050_SEND_CH?MPU6050
FF745EH   FF748AH   00002DH   BYTE   INSEG    CODE           ?PR?USB_IN_EP1?USB
FF748BH   FF74B7H   00002DH   BYTE   INSEG    CODE           ?PR?PRINTF_HID?UTIL
FF74B8H   FF74E3H   00002CH   BYTE   INSEG    CODE           ?PR?OLED_DISPLAYTURN?OLED_SPI
FF74E4H   FF750FH   00002CH   BYTE   INSEG    CODE           ?PR?EEPROM_READ?EEPROM
FF7510H   FF753BH   00002CH   BYTE   INSEG    CODE           ?PR?EEPROM_CHANGE?EEPROM
FF753CH   FF7567H   00002CH   BYTE   INSEG    CODE           ?PR?ESD_SPI_INIT?SPI
FF7568H   FF7592H   00002BH   BYTE   INSEG    CODE           ?PR?USB_IN_EP2?USB
FF7593H   FF75BBH   000029H   BYTE   INSEG    CODE           ?PR?USB_WRITE_FIFO?_?USB
FF75BCH   FF75E4H   000029H   BYTE   INSEG    CODE           ?PR?MPU6050_SIMIIC_READ_REG?MPU6050
FF75E5H   FF760DH   000029H   BYTE   INSEG    CODE           ?PR?OLED_SET_POS?OLED_SPI
FF760EH   FF7635H   000028H   BYTE   INSEG    CODE           ?PR?USB_SET_INTERFACE?USB_REQ_STD
FF7636H   FF765CH   000027H   BYTE   INSEG    CODE           ?PR?LCD12864_SCROLLUP?UTIL
FF765DH   FF7682H   000026H   BYTE   INSEG    CODE           ?PR?USB_GET_LINE_CODING?USB_REQ_CLASS
FF7683H   FF76A8H   000026H   BYTE   INSEG    CODE           ?PR?USB_SET_LINE_CODING?USB_REQ_CLASS
FF76A9H   FF76CDH   000025H   BYTE   INSEG    CODE           ?PR?MEMCPY?_?MEMCPY
FF76CEH   FF76F0H   000023H   BYTE   INSEG    CODE           ?PR?ESD_SPI_READMULTIBYTES?SPI
FF76F1H   FF7712H   000022H   BYTE   INSEG    CODE           ?PR?MPU6050_SIMIIC_WRITE_REG?MPU6050
FF7713H   FF7734H   000022H   BYTE   INSEG    CODE           ?PR?EEPROM_DELETE?EEPROM
FF7735H   FF7755H   000021H   BYTE   INSEG    CODE           ?PR?LIMIT_FLOAT?CONFIG
FF7756H   FF7776H   000021H   BYTE   INSEG    CODE           ?PR?OLED12864_SETADDRESSMODE?UTIL
FF7777H   FF7797H   000021H   BYTE   INSEG    CODE           ?PR?OLED12864_SETCONTRAST?UTIL
FF7798H   FF77B8H   000021H   BYTE   INSEG    CODE           ?PR?LCD12864_REVERSELINE?UTIL
FF77B9H   FF77D8H   000020H   BYTE   INSEG    CODE           ?PR?MPU6050_SIMIIC_SENDACK?MPU6050
FF77D9H   FF77F8H   000020H   BYTE   INSEG    CODE           ?PR?ESD_SPI_WRITEMULTIBYTES?SPI
FF77F9H   FF7816H   00001EH   BYTE   INSEG    CODE           ?PR?PIN0_I?ISR
FF7817H   FF7834H   00001EH   BYTE   INSEG    CODE           ?PR?PIN1_I?ISR
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 8


FF7835H   FF7852H   00001EH   BYTE   INSEG    CODE           ?PR?PIN2_I?ISR
FF7853H   FF7870H   00001EH   BYTE   INSEG    CODE           ?PR?PIN3_I?ISR
FF7871H   FF788EH   00001EH   BYTE   INSEG    CODE           ?PR?PIN4_I?ISR
FF788FH   FF78ACH   00001EH   BYTE   INSEG    CODE           ?PR?PIN5_I?ISR
FF78ADH   FF78CAH   00001EH   BYTE   INSEG    CODE           ?PR?PIN6_I?ISR
FF78CBH   FF78E8H   00001EH   BYTE   INSEG    CODE           ?PR?PIN7_I?ISR
FF78E9H   FF7906H   00001EH   BYTE   INSEG    CODE           ?PR?OLED_COLORTURN?OLED_SPI
FF7907H   FF7924H   00001EH   BYTE   INSEG    CODE           ?PR?UART_SEND_STRING?UART
FF7925H   FF7941H   00001DH   BYTE   INSEG    CODE           ?PR?DMA_UART2_TXD_ISR?ISR
FF7942H   FF795EH   00001DH   BYTE   INSEG    CODE           ?PR?DMA_UART3_TXD_ISR?ISR
FF795FH   FF797BH   00001DH   BYTE   INSEG    CODE           ?PR?DMA_UART4_TXD_ISR?ISR
FF797CH   FF7998H   00001DH   BYTE   INSEG    CODE           ?PR?SLEEP_US?UTIL
FF7999H   FF79B4H   00001CH   BYTE   INSEG    CODE           ?PR?UART2_I?ISR
FF79B5H   FF79D0H   00001CH   BYTE   INSEG    CODE           ?PR?UART3_I?ISR
FF79D1H   FF79ECH   00001CH   BYTE   INSEG    CODE           ?PR?UART4_I?ISR
FF79EDH   FF7A08H   00001CH   BYTE   INSEG    CODE           ?PR?MPU6050_SCCB_WAITACK?MPU6050
FF7A09H   FF7A24H   00001CH   BYTE   INSEG    CODE           ?PR?ESD_IIC_RECV_DATA?IIC
FF7A25H   FF7A40H   00001CH   BYTE   INSEG    CODE           ?PR?ESD_IIC_READ_NACK_BYTE?IIC
FF7A41H   FF7A5CH   00001CH   BYTE   INSEG    CODE           ?PR?ESD_IIC_READ_ACK_BYTE?IIC
FF7A5DH   FF7A77H   00001BH   BYTE   INSEG    CODE           ?PR?BCD2HEX?CONFIG
FF7A78H   FF7A92H   00001BH   BYTE   INSEG    CODE           ?PR?USB_REQ_CLASS?USB_REQ_CLASS
FF7A93H   FF7AACH   00001AH   BYTE   INSEG    CODE           ?PR?OLED12864_SCROLLSTART?UTIL
FF7AADH   FF7AC6H   00001AH   BYTE   INSEG    CODE           ?PR?OLED12864_DISPLAYON?UTIL
FF7AC7H   FF7AE0H   00001AH   BYTE   INSEG    CODE           ?PR?LCD12864_SCROLLRIGHT?UTIL
FF7AE1H   FF7AFAH   00001AH   BYTE   INSEG    CODE           ?PR?LCD12864_AUTOWRAPON?UTIL
FF7AFBH   FF7B14H   00001AH   BYTE   INSEG    CODE           ?PR?OLED12864_VERTICALMIRROR?UTIL
FF7B15H   FF7B2EH   00001AH   BYTE   INSEG    CODE           ?PR?LCD12864_CURSORRETURNHOME?UTIL
FF7B2FH   FF7B48H   00001AH   BYTE   INSEG    CODE           ?PR?OLED12864_DISPLAYENTIRE?UTIL
FF7B49H   FF7B62H   00001AH   BYTE   INSEG    CODE           ?PR?LCD12864_DISPLAYON?UTIL
FF7B63H   FF7B7CH   00001AH   BYTE   INSEG    CODE           ?PR?LCD12864_CURSORMOVERIGHT?UTIL
FF7B7DH   FF7B96H   00001AH   BYTE   INSEG    CODE           ?PR?LCD12864_CURSORON?UTIL
FF7B97H   FF7BB0H   00001AH   BYTE   INSEG    CODE           ?PR?OLED_WR_BYTE?OLED_SPI
FF7BB1H   FF7BCAH   00001AH   BYTE   INSEG    CODE           ?PR?OLED_POW?OLED_SPI
FF7BCBH   FF7BE3H   000019H   BYTE   INSEG    CODE           ?PR?USB_BULK_INTR_OUT?USB
FF7BE4H   FF7BFCH   000019H   BYTE   INSEG    CODE           ?PR?OLED12864_DISPLAYOFF?UTIL
FF7BFDH   FF7C15H   000019H   BYTE   INSEG    CODE           ?PR?OLED12864_SETHEADER?UTIL
FF7C16H   FF7C2EH   000019H   BYTE   INSEG    CODE           ?PR?LCD12864_AUTOWRAPOFF?UTIL
FF7C2FH   FF7C47H   000019H   BYTE   INSEG    CODE           ?PR?OLED12864_SCROLLSTOP?UTIL
FF7C48H   FF7C60H   000019H   BYTE   INSEG    CODE           ?PR?LCD12864_DISPLAYOFF?UTIL
FF7C61H   FF7C79H   000019H   BYTE   INSEG    CODE           ?PR?LCD12864_SETHEADER?UTIL
FF7C7AH   FF7C92H   000019H   BYTE   INSEG    CODE           ?PR?LCD12864_SCROLLLEFT?UTIL
FF7C93H   FF7CABH   000019H   BYTE   INSEG    CODE           ?PR?OLED12864_HORIZONTALMIRROR?UTIL
FF7CACH   FF7CC4H   000019H   BYTE   INSEG    CODE           ?PR?OLED12864_DISPLAYCONTENT?UTIL
FF7CC5H   FF7CDDH   000019H   BYTE   INSEG    CODE           ?PR?LCD12864_CURSOROFF?UTIL
FF7CDEH   FF7CF6H   000019H   BYTE   INSEG    CODE           ?PR?LCD12864_CURSORMOVELEFT?UTIL
FF7CF7H   FF7D0FH   000019H   BYTE   INSEG    CODE           ?PR?SEG7_SETHEADER?UTIL
FF7D10H   FF7D28H   000019H   BYTE   INSEG    CODE           ?PR?ESD_IIC_SEND_NACK?IIC
FF7D29H   FF7D40H   000018H   BYTE   INSEG    CODE           ?PR?UART1_I?ISR
FF7D41H   FF7D58H   000018H   BYTE   INSEG    CODE           ?PR?USB_OUT_DONE?USB
FF7D59H   FF7D70H   000018H   BYTE   INSEG    CODE           ?PR?SLEEP_MS?UTIL
FF7D71H   FF7D88H   000018H   BYTE   INSEG    CODE           ?PR?ESD_IIC_SEND_ACK?IIC
FF7D89H   FF7D9FH   000017H   BYTE   INSEG    CODE           ?PR?LIMIT_INT?CONFIG
FF7DA0H   FF7DB6H   000017H   BYTE   INSEG    CODE           ?PR?HEX2BCD?CONFIG
FF7DB7H   FF7DCDH   000017H   BYTE   INSEG    CODE           ?PR?DELAY_X_MS?DELAY
FF7DCEH   FF7DE4H   000017H   BYTE   INSEG    CODE           ?PR?DELAY_X_US?DELAY
FF7DE5H   FF7DFBH   000017H   BYTE   INSEG    CODE           ?PR?ESD_IIC_WRITE_START_BYTE?IIC
FF7DFCH   FF7E12H   000017H   BYTE   INSEG    CODE           ?PR?ESD_IIC_WAIT?IIC
FF7E13H   FF7E29H   000017H   BYTE   INSEG    CODE           ?PR?ESD_IIC_WRITE_ONE_BYTE?IIC
FF7E2AH   FF7E40H   000017H   BYTE   INSEG    CODE           ?PR?ESD_IIC_SEND_DATA?IIC
FF7E41H   FF7E56H   000016H   BYTE   INSEG    CODE           ?PR?USB_SET_CTRL_LINE_STATE?USB_REQ_CLASS
FF7E57H   FF7E6BH   000015H   BYTE   INSEG    CODE           ?PR?OLED_DISPLAY_ON?OLED_SPI
FF7E6CH   FF7E80H   000015H   BYTE   INSEG    CODE           ?PR?OLED_DISPLAY_OFF?OLED_SPI
FF7E81H   FF7E95H   000015H   BYTE   INSEG    CODE           ?PR?CANWRITEREG?CAN
FF7E96H   FF7EA9H   000014H   BYTE   INSEG    CODE           ?PR?LCD12864_DISPLAYCLEAR?UTIL
FF7EAAH   FF7EBDH   000014H   BYTE   INSEG    CODE           ?PR?OLED12864_DISPLAYREVERSE?UTIL
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 9


FF7EBEH   FF7ED0H   000013H   BYTE   INSEG    CODE           ?PR?USB_WRITE_REG?USB
FF7ED1H   FF7EE3H   000013H   BYTE   INSEG    CODE           ?PR?EEPROM_OFF?EEPROM
FF7EE4H   FF7EF6H   000013H   BYTE   INSEG    CODE           ?PR?CANREADREG?CAN
FF7EF7H   FF7F07H   000011H   BYTE   INSEG    CODE           ?PR?USB_SETUP_IN?USB
FF7F08H   FF7F18H   000011H   BYTE   INSEG    CODE           ?PR?MPU6050_SIMIIC_STOP?MPU6050
FF7F19H   FF7F28H   000010H   BYTE   INSEG    CODE           ?PR?ESD_IIC_START?IIC
FF7F29H   FF7F38H   000010H   BYTE   INSEG    CODE           ?PR?ESD_IIC_RECV_ACK?IIC
FF7F39H   FF7F48H   000010H   BYTE   INSEG    CODE           ?PR?ESD_IIC_STOP?IIC
FF7F49H   FF7F58H   000010H   BYTE   INSEG    CODE           ?PR?ABS?_?ABS
FF7F59H   FF7F68H   000010H   BYTE   INSEG    CODE           ?PR?LABS?_?LABS
FF7F69H   FF7F77H   00000FH   BYTE   INSEG    CODE           ?PR?FMAX?CONFIG
FF7F78H   FF7F86H   00000FH   BYTE   INSEG    CODE           ?PR?MPU6050_SIMIIC_START?MPU6050
FF7F87H   FF7F94H   00000EH   BYTE   INSEG    CODE           ?PR?USB_SETUP_OUT?USB
FF7F95H   FF7FA2H   00000EH   BYTE   INSEG    CODE           ?PR?USB_SETUP_STALL?USB
FF7FA3H   FF7FB0H   00000EH   BYTE   INSEG    CODE           ?PR?ESD_SPI_READBYTE?SPI
FF7FB1H   FF7FBEH   00000EH   BYTE   INSEG    CODE           ?PR?ESD_SPI_RW?SPI
FF7FBFH   FF7FCCH   00000EH   BYTE   INSEG    CODE           ?PR?STRLEN?_?STRLEN
FF7FCDH   FF7FD8H   00000CH   BYTE   INSEG    CODE           ?PR?ESD_SPI_WRITEBYTE?SPI
FF7FD9H   FF7FE3H   00000BH   BYTE   INSEG    CODE           ?PR?MPU6050_SIMIIC_DELAY?MPU6050
FF7FE4H   FF7FEDH   00000AH   BYTE   INSEG    CODE           ?PR?USB_RST?CONFIG
FF7FEEH   FF8201H   000214H   BYTE   UNIT     HCONST         ?C_INITEDATA
FF8202H   FF8259H   000058H   BYTE   UNIT     HCONST         ?HC?PWM
FF825AH   FF8271H   000018H   BYTE   UNIT     HCONST         ?HC?GPIO
FF8272H   FF827AH   000009H   BYTE   UNIT     HCONST         ?HC?CONFIG
FF827BH   FF8281H   000007H   BYTE   UNIT     HCONST         ?HC?OLED_SPI



OVERLAY MAP OF MODULE:   .\Objects\mode (main)


FUNCTION/MODULE                             BIT_GROUP   EDATA_GROUP
--> CALLED FUNCTION/MODULE                 START  STOP  START  STOP
===================================================================
?C_C51STARTUP?1                            ----- -----  ----- -----

*** NEW ROOT *****************************

IIC_I/isr                                  ----- -----  ----- -----

*** NEW ROOT *****************************

CMP_I/isr                                  ----- -----  ----- -----

*** NEW ROOT *****************************

LVD_I/isr                                  ----- -----  ----- -----

*** NEW ROOT *****************************

SPI_I/isr                                  ----- -----  ----- -----

*** NEW ROOT *****************************

UART1_I/isr                                ----- -----  ----- -----

*** NEW ROOT *****************************

UART2_I/isr                                ----- -----  ----- -----

*** NEW ROOT *****************************

UART3_I/isr                                ----- -----  ----- -----

*** NEW ROOT *****************************
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 10



UART4_I/isr                                ----- -----  ----- -----

*** NEW ROOT *****************************

DMA_UART1_RXD_isr/isr                      ----- -----  ----- -----

*** NEW ROOT *****************************

DMA_UART2_RXD_isr/isr                      ----- -----  ----- -----

*** NEW ROOT *****************************

DMA_UART3_RXD_isr/isr                      ----- -----  ----- -----

*** NEW ROOT *****************************

DMA_UART1_TXD_isr/isr                      ----- -----  ----- -----

*** NEW ROOT *****************************

DMA_UART4_RXD_isr/isr                      ----- -----  ----- -----

*** NEW ROOT *****************************

DMA_UART2_TXD_isr/isr                      ----- -----  ----- -----

*** NEW ROOT *****************************

DMA_UART3_TXD_isr/isr                      ----- -----  ----- -----

*** NEW ROOT *****************************

DMA_UART4_TXD_isr/isr                      ----- -----  ----- -----

*** NEW ROOT *****************************

DMA_ADC_isr/isr                            ----- -----  ----- -----

*** NEW ROOT *****************************

CAN1_I/isr                                 ----- -----  ----- -----
  +--> CanReadReg/CAN

CanReadReg/CAN                             ----- -----  ----- -----

*** NEW ROOT *****************************

CAN2_I/isr                                 ----- -----  ----- -----
  +--> CanReadReg/CAN

*** NEW ROOT *****************************

DMA_M2M_isr/isr                            ----- -----  ----- -----

*** NEW ROOT *****************************

PIN0_I/isr                                 ----- -----  ----- -----

*** NEW ROOT *****************************

PIN1_I/isr                                 ----- -----  ----- -----

*** NEW ROOT *****************************

L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 11


PIN2_I/isr                                 ----- -----  ----- -----

*** NEW ROOT *****************************

PIN3_I/isr                                 ----- -----  ----- -----

*** NEW ROOT *****************************

PIN4_I/isr                                 ----- -----  ----- -----

*** NEW ROOT *****************************

INT0_I/isr                                 ----- -----  ----- -----

*** NEW ROOT *****************************

PIN5_I/isr                                 ----- -----  ----- -----

*** NEW ROOT *****************************

INT1_I/isr                                 ----- -----  ----- -----

*** NEW ROOT *****************************

PIN6_I/isr                                 ----- -----  ----- -----

*** NEW ROOT *****************************

INT2_I/isr                                 ----- -----  ----- -----

*** NEW ROOT *****************************

PIN7_I/isr                                 ----- -----  ----- -----

*** NEW ROOT *****************************

INT3_I/isr                                 ----- -----  ----- -----

*** NEW ROOT *****************************

INT4_I/isr                                 ----- -----  ----- -----

*** NEW ROOT *****************************

DMA_SPI_isr/isr                            ----- -----  ----- -----

*** NEW ROOT *****************************

TIMER0_I/isr                               ----- -----  ----- -----
  +--> Key_Rst/config
  +--> Usb_Rst/config

Key_Rst/config                             ----- -----  ----- -----
  +--> Delay_X_mS/Delay

Delay_X_mS/Delay                           ----- -----  ----- -----

Usb_Rst/config                             ----- -----  ----- -----
  +--> HID_isr/config
  +--> usb_OUT_done/usb

HID_isr/config                             ----- -----  ----- -----

usb_OUT_done/usb                           ----- -----  ----- -----
  +--> usb_write_reg/usb
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 12



usb_write_reg/usb                          ----- -----  ----- -----

*** NEW ROOT *****************************

TIMER1_I/isr                               ----- -----  ----- -----

*** NEW ROOT *****************************

TIMER2_I/isr                               ----- -----  ----- -----

*** NEW ROOT *****************************

PWMA_I/isr                                 ----- -----  ----- -----

*** NEW ROOT *****************************

TIMER3_I/isr                               ----- -----  ----- -----

*** NEW ROOT *****************************

PWMB_I/isr                                 ----- -----  ----- -----

*** NEW ROOT *****************************

TIMER4_I/isr                               ----- -----  ----- -----

*** NEW ROOT *****************************

ADC_I/isr                                  ----- -----  ----- -----

*** NEW ROOT *****************************

usb_isr/usb                                ----- -----  ----- -----
  +--> usb_read_reg/usb
  +--> usb_resume/usb
  +--> usb_reset/usb
  +--> usb_setup/usb
  +--> usb_in_ep1/usb
  +--> usb_in_ep2/usb
  +--> usb_out_ep1/usb
  +--> usb_suspend/usb

usb_read_reg/usb                           ----- -----  ----- -----

usb_resume/usb                             ----- -----  ----- -----

usb_reset/usb                              ----- -----  ----- -----
  +--> usb_write_reg/usb

usb_setup/usb                              ----- -----  ----- -----
  +--> usb_write_reg/usb
  +--> usb_read_reg/usb
  +--> usb_read_fifo?_/usb
  +--> reverse2/util
  +--> usb_req_std/usb_req_std
  +--> usb_req_class/usb_req_class
  +--> usb_req_vendor/usb_req_vendor
  +--> usb_setup_stall/usb
  +--> usb_ctrl_in/usb
  +--> usb_ctrl_out/usb

usb_read_fifo?_/usb                        ----- -----  ----- -----
  +--> usb_read_reg/usb

L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 13


reverse2/util                              ----- -----  026CH 026FH

usb_req_std/usb_req_std                    ----- -----  ----- -----
  +--> usb_get_status/usb_req_std
  +--> usb_clear_feature/usb_req_std
  +--> usb_set_feature/usb_req_std
  +--> usb_set_address/usb_req_std
  +--> usb_get_descriptor/usb_req_std
  +--> usb_set_descriptor/usb_req_std
  +--> usb_get_configuration/usb_req_std
  +--> usb_set_configuration/usb_req_std
  +--> usb_get_interface/usb_req_std
  +--> usb_set_interface/usb_req_std
  +--> usb_synch_frame/usb_req_std
  +--> usb_setup_stall/usb

usb_get_status/usb_req_std                 ----- -----  ----- -----
  +--> usb_setup_stall/usb
  +--> usb_setup_in/usb

usb_setup_stall/usb                        ----- -----  ----- -----
  +--> usb_write_reg/usb

usb_setup_in/usb                           ----- -----  ----- -----
  +--> usb_write_reg/usb
  +--> usb_ctrl_in/usb

usb_ctrl_in/usb                            ----- -----  ----- -----
  +--> usb_write_reg/usb
  +--> usb_read_reg/usb
  +--> usb_write_fifo?_/usb

usb_write_fifo?_/usb                       ----- -----  ----- -----
  +--> usb_write_reg/usb

usb_clear_feature/usb_req_std              ----- -----  ----- -----
  +--> usb_write_reg/usb
  +--> usb_setup_stall/usb
  +--> usb_setup_status/usb

usb_setup_status/usb                       ----- -----  ----- -----
  +--> usb_write_reg/usb

usb_set_feature/usb_req_std                ----- -----  ----- -----
  +--> usb_write_reg/usb
  +--> usb_setup_stall/usb
  +--> usb_setup_status/usb

usb_set_address/usb_req_std                ----- -----  ----- -----
  +--> usb_setup_stall/usb
  +--> usb_write_reg/usb
  +--> usb_setup_status/usb

usb_get_descriptor/usb_req_std             ----- -----  ----- -----
  +--> usb_setup_stall/usb
  +--> usb_setup_in/usb

usb_set_descriptor/usb_req_std             ----- -----  ----- -----
  +--> usb_setup_stall/usb

usb_get_configuration/usb_req_std          ----- -----  ----- -----
  +--> usb_setup_stall/usb
  +--> usb_setup_in/usb

usb_set_configuration/usb_req_std          ----- -----  ----- -----
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 14


  +--> usb_setup_stall/usb
  +--> usb_write_reg/usb
  +--> usb_setup_status/usb

usb_get_interface/usb_req_std              ----- -----  ----- -----
  +--> usb_setup_stall/usb
  +--> usb_setup_in/usb

usb_set_interface/usb_req_std              ----- -----  ----- -----
  +--> usb_setup_stall/usb
  +--> usb_setup_status/usb

usb_synch_frame/usb_req_std                ----- -----  ----- -----
  +--> usb_setup_stall/usb

usb_req_class/usb_req_class                ----- -----  ----- -----
  +--> usb_set_line_coding/usb_req_class
  +--> usb_get_line_coding/usb_req_class
  +--> usb_set_ctrl_line_state/usb_req_class
  +--> usb_setup_stall/usb

usb_set_line_coding/usb_req_class          ----- -----  ----- -----
  +--> usb_setup_stall/usb
  +--> usb_setup_out/usb

usb_setup_out/usb                          ----- -----  ----- -----
  +--> usb_write_reg/usb

usb_get_line_coding/usb_req_class          ----- -----  ----- -----
  +--> usb_setup_stall/usb
  +--> usb_setup_in/usb

usb_set_ctrl_line_state/usb_req_class      ----- -----  ----- -----
  +--> usb_setup_stall/usb
  +--> usb_setup_status/usb

usb_req_vendor/usb_req_vendor              ----- -----  ----- -----
  +--> usb_setup_stall/usb

usb_ctrl_out/usb                           ----- -----  ----- -----
  +--> usb_write_reg/usb
  +--> usb_read_reg/usb
  +--> usb_read_fifo?_/usb

usb_in_ep1/usb                             ----- -----  ----- -----
  +--> usb_write_reg/usb
  +--> usb_read_reg/usb

usb_in_ep2/usb                             ----- -----  ----- -----
  +--> usb_write_reg/usb
  +--> usb_read_reg/usb

usb_out_ep1/usb                            ----- -----  ----- -----
  +--> usb_write_reg/usb
  +--> usb_read_reg/usb
  +--> usb_read_fifo?_/usb
  +--> sleep_ms/util

sleep_ms/util                              ----- -----  ----- -----
  +--> sleep_us/util

sleep_us/util                              ----- -----  ----- -----

usb_suspend/usb                            ----- -----  ----- -----

L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 15


*** NEW ROOT *****************************

?C_C51STARTUP                              ----- -----  ----- -----

*** NEW ROOT *****************************

?C_C51STARTUP?3                            ----- -----  ----- -----
  +--> main/main

main/main                                  ----- -----  ----- -----
  +--> System_init/config
  +--> PIT_init_ms/PIT
  +--> INT_init/INT
  +--> PWM_init/PWM
  +--> GPIO_init_pin/GPIO
  +--> GPIO_pull_pin/GPIO
  +--> IR_Detection/main
  +--> Gripper_Control/main

System_init/config                         ----- -----  ----- -----
  +--> usb_init/usb

usb_init/usb                               ----- -----  ----- -----
  +--> usb_write_reg/usb

PIT_init_ms/PIT                            ----- -----  ----- -----
  +--> ?C?ULIDIV/?C?ULDIV

?C?ULIDIV/?C?ULDIV                         ----- -----  ----- -----

INT_init/INT                               ----- -----  ----- -----

PWM_init/PWM                               ----- -----  0270H 0273H
  +--> ?C?ULIDIV/?C?ULDIV

GPIO_init_pin/GPIO                         ----- -----  0270H 0277H

GPIO_pull_pin/GPIO                         ----- -----  0270H 0277H

IR_Detection/main                          22H.3 22H.3  ----- -----

Gripper_Control/main                       ----- -----  ----- -----
  +--> Set_Motor_Speed/main

Set_Motor_Speed/main                       ----- -----  ----- -----

*** NEW ROOT *****************************

?C_C51STARTUP?2                            ----- -----  ----- -----



PUBLIC SYMBOLS OF MODULE:  .\Objects\mode (main)


      VALUE       CLASS    TYPE      PUBLIC SYMBOL NAME
      =================================================
      00FF043EH   CODE     NEAR LAB  ?C?CASTF
      000000FFH   NUMBER   ---       ?C?CODESEG
      00FF040BH   CODE     NEAR LAB  ?C?FCASTC
      00FF0406H   CODE     NEAR LAB  ?C?FCASTI
      00FF0401H   CODE     NEAR LAB  ?C?FCASTL
      00FF0CFDH   CODE     ---       ?C?FP2SERIES
      00FF01D4H   CODE     ---       ?C?FPADD
      00FF03AFH   CODE     ---       ?C?FPCMP
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 16


      00FF03ADH   CODE     ---       ?C?FPCMP3
      00FF0C02H   CODE     ---       ?C?FPCONVERT
      00FF032CH   CODE     ---       ?C?FPDIV
      00FF0584H   CODE     ---       ?C?FPGETOPN2
      00FF028CH   CODE     ---       ?C?FPMUL
      00FF05C1H   CODE     ---       ?C?FPNANRESULT
      00FF03F3H   CODE     ---       ?C?FPNEG
      00FF05C9H   CODE     ---       ?C?FPOVERFLOW
      00FF059DH   CODE     ---       ?C?FPRESULT
      00FF05B3H   CODE     ---       ?C?FPRESULT2
      00FF0CBAH   CODE     ---       ?C?FPROUND
      00FF0D06H   CODE     ---       ?C?FPSERIES
      00FF01D1H   CODE     ---       ?C?FPSUB
      00FF05C6H   CODE     ---       ?C?FPUNDERFLOW
      00FF0D5AH   CODE     ---       ?C?FTNPWR
      00FF6B00H   CODE     ---       ?C?INITEDATA
      00FF8200H   HCONST   WORD      ?C?INITEDATA_END
      00FF0DC3H   CODE     ---       ?C?LMUL
      00FF0602H   CODE     ---       ?C?PRNFMT
      00FF0D91H   CODE     ---       ?C?SIDIV
      00FF0E27H   CODE     NEAR LAB  ?C?SLDIV
      00FF0000H   CODE     ---       ?C?STARTUP
      00FF0DD6H   CODE     NEAR LAB  ?C?ULDIV
      00FF0DD4H   CODE     NEAR LAB  ?C?ULIDIV
      00000001H   NUMBER   ---       ?C?XDATASEG
      00FF0000H   CODE     ---       ?C_STARTUP
      00000021H.3 BIT      BIT       ?CAN_MKS_CONTROL?BIT
      00000022H.0 BIT      BIT       ?CANInit?BIT
      00000022H.1 BIT      BIT       ?CanReadMsg?BIT
      00000022H.2 BIT      BIT       ?CanSendMsg?BIT
      0000027DH   EDATA    BYTE      ?CanSendMsg?BYTE
      00000021H.4 BIT      BIT       ?ESD_M1_POS_CONTROL?BIT
      00000004H   EDATA    BYTE      ?IMUupdate?BYTE
      000002D5H   EDATA    BYTE      ?Limit_float?BYTE
      000002D1H   EDATA    BYTE      ?Limit_int?BYTE
      00000021H.6 BIT      BIT       ?Out_IO?BIT
      00000151H   EDATA    BYTE      ?printf_hid?BYTE
      0000026AH   EDATA    BYTE      ?PWM_init?BYTE
      00000125H   EDATA    BYTE      ?SEG7_ShowString?BYTE
      000001A7H   EDATA    ---       ?sprintf?BYTE
      0000024FH   EDATA    BYTE      ?UART_Send_float?BYTE
      0000025FH   EDATA    BYTE      ?UART_Send_int?BYTE
      000002F1H   EDATA    ---       ?vsprintf?BYTE
      00FF7F49H   CODE     ---       abs?_
      000000B6H   EDATA    INT       ACC_X
      000000B8H   EDATA    INT       ACC_Y
      000000BAH   EDATA    INT       ACC_Z
*SFR* 000000BCH   DATA     BYTE      ADC_CONTR
*SFR* 000000BCH.5 DATA     BIT       ADC_FLAG
      00FF6E1AH   CODE     ---       ADC_get
      00FF0059H   CODE     ---       ADC_I
      00FF64E0H   CODE     ---       ADC_init
*SFR* 000000BCH.7 DATA     BIT       ADC_POWER
*SFR* 000000BDH   DATA     BYTE      ADC_RES
*SFR* 000000BEH   DATA     BYTE      ADC_RESL
*SFR* 000000BCH.6 DATA     BIT       ADC_START
*SFR* 000000DEH   DATA     BYTE      ADCCFG
      000000C4H   EDATA    FLOAT     ANG_ERR
      000000DFH   EDATA    FLOAT     ANG_ERR_OLD
      000000CEH   EDATA    FLOAT     ANG_OUT
      00000031H   EDATA    FLOAT     angle
      00000083H   EDATA    FLOAT     angle_dot
      00000041H   EDATA    FLOAT     Angle_err
      00000093H   EDATA    FLOAT     AngleX
      00000097H   EDATA    FLOAT     AngleY
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 17


      00FF0E5BH   CODE     ---       asc2_0806
      00FF1083H   CODE     ---       asc2_1608
      00FF0564H   CODE     ---       asin?_
      00FF0B22H   CODE     ---       ATAN?_
      00000020H.3 BIT      BIT       auto_mode
*SFR* 000000EFH   DATA     BYTE      AUXINTIF
*SFR* 0000008EH   DATA     BYTE      AUXR
*SFR* 00000097H   DATA     BYTE      AUXR2
      00FF7A5DH   CODE     ---       BCD2HEX
      000002D6H   EDATA    BYTE      BRP
      00000020H.7 BIT      BIT       bUsbFeatureReady
      00000021H.0 BIT      BIT       bUsbInBusy
      00000020H.6 BIT      BIT       bUsbOutReady
      000000A3H   EDATA    CHAR      C_0
      00FF65B9H   CODE     ---       CAN1_I
      00FF6624H   CODE     ---       CAN2_I
*SFR* 000000F1H.5 DATA     BIT       CAN2IE
      00FF526CH   CODE     ---       CAN_MKS_CONTROL
      00FF5E44H   CODE     ---       CAN_MKS_SPD_CONTROL
      000002D7H   EDATA    WORD      CAN_time
      00000021H.7 BIT      BIT       CAN_TX_OK
*SFR* 000000F1H   DATA     BYTE      CANICR
*SFR* 000000F1H.1 DATA     BIT       CANIE
      00FF3DEAH   CODE     ---       CANInit
      00FF5EDBH   CODE     ---       CanReadFifo
      00FF5810H   CODE     ---       CanReadMsg
      00FF7EE4H   CODE     ---       CanReadReg
*SFR* 00000097H.3 DATA     BIT       CANSEL
      00FF4636H   CODE     ---       CanSendMsg
      00FF7E81H   CODE     ---       CanWriteReg
      00FF4C5BH   CODE     ---       CAR_ADD_ANGLE
      000000BCH   EDATA    FLOAT     CAR_ANG
      00FF574FH   CODE     ---       CAR_PID_CONTROL
      000001F8H   EDATA    ---       char_uchar
*SFR* 000000EAH   DATA     BYTE      CKCON
      00FF0021H   CODE     ---       CMP_I
*SFR* 000000E6H   DATA     BYTE      CMPCR1
*SFR* 000000E7H   DATA     BYTE      CMPCR2
      000001DEH   EDATA    INT       collision_counter
      000001D0H   EDATA    LONG      collision_pos
      00FF61D2H   CODE     ---       CONFIGDESC
      000000D2H   EDATA    ---       D2_CAN_TX
      000000DAH   EDATA    ---       D2int_uchar
      000000C0H   EDATA    ---       D2long_uchar
      00000087H   EDATA    FLOAT     d_t
      00FF7DB7H   CODE     ---       Delay_X_mS
      00FF7DCEH   CODE     ---       Delay_X_uS
      00FF61C0H   CODE     ---       DEVICEDESC
      00000246H   EDATA    BYTE      DeviceState
      00FF002AH   CODE     ---       DMA_ADC_isr
      00FF0031H   CODE     ---       DMA_M2M_isr
      00FF479EH   CODE     ---       DMA_RXD_init
      00FF004FH   CODE     ---       DMA_SPI_isr
      00FF48E6H   CODE     ---       DMA_TXD_init
      00FF6E5EH   CODE     ---       DMA_UART1_RXD_isr
      00FF0066H   CODE     ---       DMA_UART1_TXD_isr
      00FF6EA0H   CODE     ---       DMA_UART2_RXD_isr
      00FF7925H   CODE     ---       DMA_UART2_TXD_isr
      00FF6EE2H   CODE     ---       DMA_UART3_RXD_isr
      00FF7942H   CODE     ---       DMA_UART3_TXD_isr
      00FF6F24H   CODE     ---       DMA_UART4_RXD_isr
      00FF795FH   CODE     ---       DMA_UART4_TXD_isr
      0000009BH   EDATA    FLOAT     E
*SFR* 000000A8H.7 DATA     BIT       EA
*SFR* 000000BAH.7 DATA     BIT       EAXFR
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 18


      00FF7510H   CODE     ---       EEPROM_Change
      00FF7713H   CODE     ---       EEPROM_Delete
      00FF7ED1H   CODE     ---       EEPROM_OFF
      00FF74E4H   CODE     ---       EEPROM_Read
      00000247H   EDATA    ---       Ep0State
      000001ECH   EDATA    ---       err_spd
*SFR* 000000A8H.4 DATA     BIT       ES
*SFR* 000000AFH.0 DATA     BIT       ES2
*SFR* 000000AFH.3 DATA     BIT       ES3
*SFR* 000000AFH.4 DATA     BIT       ES4
      00FF7A41H   CODE     ---       ESD_IIC_READ_ACK_BYTE
      00FF7A25H   CODE     ---       ESD_IIC_READ_NACK_BYTE
      00FF7F29H   CODE     ---       ESD_IIC_Recv_ack
      00FF7A09H   CODE     ---       ESD_IIC_Recv_data
      00FF7D71H   CODE     ---       ESD_IIC_Send_ack
      00FF7E2AH   CODE     ---       ESD_IIC_Send_data
      00FF7D10H   CODE     ---       ESD_IIC_Send_nack
      00FF7F19H   CODE     ---       ESD_IIC_Start
      00FF7F39H   CODE     ---       ESD_IIC_Stop
      00FF7DFCH   CODE     ---       ESD_IIC_Wait
      00FF7E13H   CODE     ---       ESD_IIC_WRITE_ONE_BYTE
      00FF7DE5H   CODE     ---       ESD_IIC_WRITE_START_BYTE
      00FF596EH   CODE     ---       ESD_Init_IIC
      00FF6471H   CODE     ---       ESD_M1_POS_CONTROL
      00FF7371H   CODE     ---       ESD_M1_PWM_CONTROL
      00FF730CH   CODE     ---       ESD_M1_SPD_CONTROL
      00FF6221H   CODE     ---       ESD_Read_IIC
      00FF0016H   CODE     ---       ESD_SPI_Deinit
      00FF753CH   CODE     ---       ESD_SPI_Init
      00FF7FA3H   CODE     ---       ESD_SPI_ReadByte
      00FF76CEH   CODE     ---       ESD_SPI_ReadMultiBytes
      00FF7FB1H   CODE     ---       ESD_SPI_RW
      00FF7FCDH   CODE     ---       ESD_SPI_WriteByte
      00FF77D9H   CODE     ---       ESD_SPI_WriteMultiBytes
      00FF68E2H   CODE     ---       ESD_Write_IIC
*SFR* 000000A8H.1 DATA     BIT       ET0
*SFR* 000000A8H.3 DATA     BIT       ET1
*SFR* 000000AFH.7 DATA     BIT       EUSB
*SFR* 000000A8H.0 DATA     BIT       EX0
*SFR* 000000A8H.2 DATA     BIT       EX1
      00000035H   EDATA    FLOAT     exInt
      00000039H   EDATA    FLOAT     eyInt
      0000003DH   EDATA    FLOAT     ezInt
      00FF04B7H   CODE     NEAR LAB  fabs?_
      000001F4H   EDATA    ---       float_uchar
      00FF668FH   CODE     ---       floor?_
      00FF7F69H   CODE     ---       fmax
      00FF3718H   CODE     ---       Get_IO
      00FF434CH   CODE     ---       GPIO_init_8pin
      00FF6761H   CODE     ---       GPIO_init_allpin
      00FF340AH   CODE     ---       GPIO_init_pin
      00FF4D6EH   CODE     ---       GPIO_isr_deinit
      00FF2739H   CODE     ---       GPIO_isr_init
      00FF41A5H   CODE     ---       GPIO_pull_pin
      00FF5082H   CODE     ---       Gripper_Control
      00000020H.1 BIT      BIT       gripper_mode
      000001CEH   EDATA    INT       gripper_state
      000001EAH   EDATA    INT       gripper_timeout
      000000F1H   EDATA    FLOAT     GYRO_X
      000000F5H   EDATA    FLOAT     GYRO_Y
      000000F9H   EDATA    FLOAT     GYRO_Z
      000000CCH   EDATA    INT       GYRO_Z_OFFSET
      000000DCH   EDATA    INT       GYRO_Z_OFFSET_ADD
      000000DEH   EDATA    BYTE      GYRO_Z_OFFSET_N
      00FF7DA0H   CODE     ---       HEX2BCD
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 19


      00FF001EH   CODE     ---       HID_isr
      00FF1673H   CODE     ---       Hzk
*SFR* 000000F6H   DATA     BYTE      IAP_ADDRE
*SFR* 000000C3H   DATA     BYTE      IAP_ADDRH
*SFR* 000000C4H   DATA     BYTE      IAP_ADDRL
*SFR* 000000C5H   DATA     BYTE      IAP_CMD
*SFR* 000000C7H   DATA     BYTE      IAP_CONTR
*SFR* 000000C2H   DATA     BYTE      IAP_DATA
*SFR* 000000F5H   DATA     BYTE      IAP_TPS
*SFR* 000000C6H   DATA     BYTE      IAP_TRIG
*SFR* 000000A8H   DATA     BYTE      IE
*SFR* 000000AFH   DATA     BYTE      IE2
      00FF001AH   CODE     ---       IIC_I
      00FF2D0CH   CODE     ---       IMUupdate
      0000024EH   EDATA    BYTE      InEpState
      00FF0032H   CODE     ---       INT0_I
      00FF0049H   CODE     ---       INT1_I
      00FF73A2H   CODE     ---       INT2_I
      *EXTERN*    CODE     ---       INT2_isr
      00FF004AH   CODE     ---       INT3_I
      00FF004EH   CODE     ---       INT4_I
      00FF733FH   CODE     ---       INT_init
      000001FDH   EDATA    ---       int_uchar
*SFR* 0000008FH   DATA     BYTE      INTCLKO
*SFR* 000000B8H   DATA     BYTE      IP
*SFR* 000000B5H   DATA     BYTE      IP2
*SFR* 000000B6H   DATA     BYTE      IP2H
*SFR* 000000DFH   DATA     BYTE      IP3
*SFR* 000000EEH   DATA     BYTE      IP3H
*SFR* 000000B7H   DATA     BYTE      IPH
      00000020H.0 BIT      BIT       ir_detected
      00FF73D2H   CODE     ---       IR_Detection
      00000020H.5 BIT      BIT       ir_old
*SFR* 000000B0H.3 DATA     BIT       IR_SENSOR
      000001DCH   EDATA    INT       ir_stable_counter
*SFR* 0000009DH   DATA     BYTE      IRCBAND
*SFR* 0000009FH   DATA     BYTE      IRTRIM
*SFR* 00000088H.0 DATA     BIT       IT0
*SFR* 00000088H.2 DATA     BIT       IT1
      000000B2H   EDATA    FLOAT     K1
      000000A4H   EDATA    FLOAT     K_0
      000000A8H   EDATA    FLOAT     K_1
      00FF3FF8H   CODE     ---       Kalman_Filter
*SFR* 000000B0H.2 DATA     BIT       KEY1
      00000020H.4 BIT      BIT       key1_flag
      00000020H.2 BIT      BIT       key1_old
      00000203H   EDATA    WORD      Key_cnt
      00000021H.1 BIT      BIT       Key_Flag
      00FF7027H   CODE     ---       Key_Rst
      00FF7F59H   CODE     ---       labs?_
      00FF61BCH   CODE     ---       LANGIDDESC
      00FF7C16H   CODE     ---       LCD12864_AutoWrapOff
      00FF7AE1H   CODE     ---       LCD12864_AutoWrapOn
      00FF7CDEH   CODE     ---       LCD12864_CursorMoveLeft
      00FF7B63H   CODE     ---       LCD12864_CursorMoveRight
      00FF7CC5H   CODE     ---       LCD12864_CursorOff
      00FF7B7DH   CODE     ---       LCD12864_CursorOn
      00FF7B15H   CODE     ---       LCD12864_CursorReturnHome
      00FF7E96H   CODE     ---       LCD12864_DisplayClear
      00FF7C48H   CODE     ---       LCD12864_DisplayOff
      00FF7B49H   CODE     ---       LCD12864_DisplayOn
      00FF7798H   CODE     ---       LCD12864_ReverseLine
      00FF7C7AH   CODE     ---       LCD12864_ScrollLeft
      00FF7AC7H   CODE     ---       LCD12864_ScrollRight
      00FF7636H   CODE     ---       LCD12864_ScrollUp
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 20


      00FF6B7CH   CODE     ---       LCD12864_ShowPicture
      00FF70A4H   CODE     ---       LCD12864_ShowString
      00FF6D8CH   CODE     ---       LED40_SendData
      00FF6DD3H   CODE     ---       LED64_SendData
      00FF7735H   CODE     ---       Limit_float
      00FF7D89H   CODE     ---       Limit_int
      000002B3H   EDATA    ---       LineCoding
*SFR* 000000F9H   DATA     BYTE      LINICR
      00FF04C6H   CODE     NEAR LAB  log10?_
      00FF0A3DH   CODE     NEAR LAB  LOG?_
      000001F9H   EDATA    ---       long_uchar
      00000213H   EDATA    ---       long_uchar1
      00FF0022H   CODE     ---       LVD_I
      00FF4B43H   CODE     ---       main
      00FF6219H   CODE     ---       MANUFACTDESC
      00FF76A9H   CODE     ---       memcpy?_
      00FF005FH   CODE     ---       MKS_ALL_RUN
      00FF54FFH   CODE     ---       MKS_HOME
      00000217H   EDATA    WORD      MKS_KD
      00000219H   EDATA    WORD      MKS_KI
      0000021BH   EDATA    WORD      MKS_KP
      0000021DH   EDATA    WORD      MKS_KV
      00FF5350H   CODE     ---       MKS_PID_SET
      0000021FH   EDATA    ---       MKS_TX
      *EXTERN*    CODE     ---       MOTOR_control
      000001E6H   EDATA    LONG      motor_pos
      000001D4H   EDATA    LONG      motor_pos_old
      000001E4H   EDATA    INT       motor_spd
      00000211H   EDATA    WORD      MOTOR_state
      00000023H   EDATA    INT       mpu6050_acc_x
      00000025H   EDATA    INT       mpu6050_acc_y
      0000002BH   EDATA    INT       mpu6050_acc_z
      00FF6FE7H   CODE     ---       mpu6050_get_acc
      00FF6B2CH   CODE     ---       mpu6050_get_gyro
      0000005DH   EDATA    INT       mpu6050_gyro_x
      0000005FH   EDATA    INT       mpu6050_gyro_y
      00000061H   EDATA    INT       mpu6050_gyro_z
      00FF654DH   CODE     ---       mpu6050_iic_init
      00FF69E3H   CODE     ---       mpu6050_init
      00FF75BCH   CODE     ---       mpu6050_simiic_read_reg
      00FF693AH   CODE     ---       mpu6050_simiic_read_regs
      000000E3H   EDATA    ---       MPU_data
      000000ACH   EDATA    INT       mpu_temp
      00FF7CACH   CODE     ---       OLED12864_DisplayContent
      00FF7B2FH   CODE     ---       OLED12864_DisplayEntire
      00FF7BE4H   CODE     ---       OLED12864_DisplayOff
      00FF7AADH   CODE     ---       OLED12864_DisplayOn
      00FF7EAAH   CODE     ---       OLED12864_DisplayReverse
      00FF7C93H   CODE     ---       OLED12864_HorizontalMirror
      00FF71CAH   CODE     ---       OLED12864_ScrollLeft
      00FF7156H   CODE     ---       OLED12864_ScrollRight
      00FF7A93H   CODE     ---       OLED12864_ScrollStart
      00FF7C2FH   CODE     ---       OLED12864_ScrollStop
      00FF7190H   CODE     ---       OLED12864_ScrollUp
      00FF7756H   CODE     ---       OLED12864_SetAddressMode
      00FF7777H   CODE     ---       OLED12864_SetContrast
      00FF6FA7H   CODE     ---       OLED12864_ShowPicture
      00FF7AFBH   CODE     ---       OLED12864_VerticalMirror
      00FF7239H   CODE     ---       OLED_Clear
      00FF78E9H   CODE     ---       OLED_ColorTurn
*SFR* 000000A0H.2 DATA     BIT       OLED_CS
*SFR* 000000A0H.4 DATA     BIT       OLED_DC
      00FF7E6CH   CODE     ---       OLED_Display_Off
      00FF7E57H   CODE     ---       OLED_Display_On
      00FF74B8H   CODE     ---       OLED_DisplayTurn
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 21


      00FF5DADH   CODE     ---       OLED_DrawBMP
      00FF5428H   CODE     ---       OLED_Init
      00FF7BB1H   CODE     ---       oled_pow
*SFR* 000000A0H.6 DATA     BIT       OLED_RES
      00FF75E5H   CODE     ---       OLED_Set_Pos
      00FF568CH   CODE     ---       OLED_ShowChar
      00FF62A3H   CODE     ---       OLED_ShowChinese
      00FF4E7BH   CODE     ---       OLED_ShowFloat
      00FF5D15H   CODE     ---       OLED_ShowInt
      00FF5184H   CODE     ---       OLED_ShowNum
      00FF631FH   CODE     ---       OLED_ShowString
      00FF7B97H   CODE     ---       OLED_WR_Byte
      00FF3BC9H   CODE     ---       Out_IO
      0000024FH   EDATA    BYTE      OutEpState
      00000244H   EDATA    BYTE      OutNumber
*SFR* 00000080H   DATA     BYTE      P0
*SFR* 00000080H.0 DATA     BIT       P00
*SFR* 00000080H.1 DATA     BIT       P01
*SFR* 00000080H.2 DATA     BIT       P02
*SFR* 00000080H.3 DATA     BIT       P03
*SFR* 00000080H.4 DATA     BIT       P04
*SFR* 00000080H.5 DATA     BIT       P05
*SFR* 00000080H.6 DATA     BIT       P06
*SFR* 00000080H.7 DATA     BIT       P07
*SFR* 00000094H   DATA     BYTE      P0M0
*SFR* 00000093H   DATA     BYTE      P0M1
*SFR* 00000090H   DATA     BYTE      P1
*SFR* 00000090H.0 DATA     BIT       P10
*SFR* 00000090H.1 DATA     BIT       P11
*SFR* 00000090H.2 DATA     BIT       P12
*SFR* 00000090H.3 DATA     BIT       P13
*SFR* 00000090H.4 DATA     BIT       P14
*SFR* 00000090H.5 DATA     BIT       P15
*SFR* 00000090H.6 DATA     BIT       P16
*SFR* 00000090H.7 DATA     BIT       P17
*SFR* 00000092H   DATA     BYTE      P1M0
*SFR* 00000091H   DATA     BYTE      P1M1
*SFR* 000000A0H   DATA     BYTE      P2
*SFR* 000000A0H.0 DATA     BIT       P20
*SFR* 000000A0H.1 DATA     BIT       P21
*SFR* 000000A0H.2 DATA     BIT       P22
*SFR* 000000A0H.3 DATA     BIT       P23
*SFR* 000000A0H.4 DATA     BIT       P24
*SFR* 000000A0H.5 DATA     BIT       P25
*SFR* 000000A0H.6 DATA     BIT       P26
*SFR* 000000A0H.7 DATA     BIT       P27
*SFR* 00000096H   DATA     BYTE      P2M0
*SFR* 00000095H   DATA     BYTE      P2M1
*SFR* 000000B0H   DATA     BYTE      P3
*SFR* 000000B0H.0 DATA     BIT       P30
*SFR* 000000B0H.1 DATA     BIT       P31
*SFR* 000000B0H.2 DATA     BIT       P32
*SFR* 000000B0H.3 DATA     BIT       P33
*SFR* 000000B0H.4 DATA     BIT       P34
*SFR* 000000B0H.5 DATA     BIT       P35
*SFR* 000000B0H.6 DATA     BIT       P36
*SFR* 000000B0H.7 DATA     BIT       P37
*SFR* 000000B2H   DATA     BYTE      P3M0
*SFR* 000000B1H   DATA     BYTE      P3M1
*SFR* 000000C0H   DATA     BYTE      P4
*SFR* 000000C0H.0 DATA     BIT       P40
*SFR* 000000C0H.1 DATA     BIT       P41
*SFR* 000000C0H.2 DATA     BIT       P42
*SFR* 000000C0H.3 DATA     BIT       P43
*SFR* 000000C0H.4 DATA     BIT       P44
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 22


*SFR* 000000B4H   DATA     BYTE      P4M0
*SFR* 000000B3H   DATA     BYTE      P4M1
*SFR* 000000C8H   DATA     BYTE      P5
*SFR* 000000C8H.0 DATA     BIT       P50
*SFR* 000000C8H.1 DATA     BIT       P51
*SFR* 000000C8H.2 DATA     BIT       P52
*SFR* 000000C8H.3 DATA     BIT       P53
*SFR* 000000C8H.4 DATA     BIT       P54
*SFR* 000000C8H.5 DATA     BIT       P55
*SFR* 000000CAH   DATA     BYTE      P5M0
*SFR* 000000C9H   DATA     BYTE      P5M1
*SFR* 000000E8H   DATA     BYTE      P6
*SFR* 000000E8H.0 DATA     BIT       P60
*SFR* 000000E8H.1 DATA     BIT       P61
*SFR* 000000E8H.2 DATA     BIT       P62
*SFR* 000000E8H.3 DATA     BIT       P63
*SFR* 000000E8H.4 DATA     BIT       P64
*SFR* 000000E8H.5 DATA     BIT       P65
*SFR* 000000E8H.6 DATA     BIT       P66
*SFR* 000000E8H.7 DATA     BIT       P67
*SFR* 000000CCH   DATA     BYTE      P6M0
*SFR* 000000CBH   DATA     BYTE      P6M1
*SFR* 000000F8H   DATA     BYTE      P7
*SFR* 000000F8H.0 DATA     BIT       P70
*SFR* 000000F8H.1 DATA     BIT       P71
*SFR* 000000F8H.2 DATA     BIT       P72
*SFR* 000000F8H.3 DATA     BIT       P73
*SFR* 000000F8H.4 DATA     BIT       P74
*SFR* 000000F8H.5 DATA     BIT       P75
*SFR* 000000F8H.6 DATA     BIT       P76
*SFR* 000000F8H.7 DATA     BIT       P77
*SFR* 000000E2H   DATA     BYTE      P7M0
*SFR* 000000E1H   DATA     BYTE      P7M1
*SFR* 000000A2H   DATA     BYTE      P_SW1
*SFR* 000000BAH   DATA     BYTE      P_SW2
*SFR* 000000BBH   DATA     BYTE      P_SW3
      00FF6215H   CODE     ---       PACKET0
      00FF6217H   CODE     ---       PACKET1
*SFR* 00000087H   DATA     BYTE      PCON
      00000045H   EDATA    FLOAT     PCt_0
      00000059H   EDATA    FLOAT     PCt_1
      00000049H   EDATA    ---       Pdot
      00FF77F9H   CODE     ---       PIN0_I
      00FF7817H   CODE     ---       PIN1_I
      00FF7835H   CODE     ---       PIN2_I
      00FF7853H   CODE     ---       PIN3_I
      00FF7871H   CODE     ---       PIN4_I
      00FF788FH   CODE     ---       PIN5_I
      00FF78ADH   CODE     ---       PIN6_I
      00FF78CBH   CODE     ---       PIN7_I
      00FF6A37H   CODE     ---       PIT_count_clean
      00FF6C18H   CODE     ---       PIT_count_get
      00FF6C65H   CODE     ---       PIT_init_encoder
      00FF6004H   CODE     ---       PIT_init_ms
      00FF5F71H   CODE     ---       PIT_init_us
      00000063H   EDATA    ---       PP
      00FF748BH   CODE     ---       printf_hid
      00FF619EH   CODE     ---       PRODUCTDESC
*SFR* 000000D0H   DATA     BYTE      PSW
*SFR* 000000B5H.7 DATA     BIT       PUSB
*SFR* 000000B6H.7 DATA     BIT       PUSBH
      00FF8222H   HCONST   ---       PWM_ARR_ADDR
      00FF822AH   HCONST   ---       PWM_CCER_ADDR
      00FF823AH   HCONST   ---       PWM_CCMR_ADDR
      00FF8202H   HCONST   ---       PWM_CCR_ADDR
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 23


      00FF399FH   CODE     ---       PWM_change
      00FF30BEH   CODE     ---       PWM_init
      000001D8H   EDATA    LONG      pwm_out
      00FF0052H   CODE     ---       PWMA_I
      00FF0057H   CODE     ---       PWMB_I
      00000073H   EDATA    FLOAT     q0
      00000077H   EDATA    FLOAT     q1
      0000007BH   EDATA    FLOAT     q2
      0000007FH   EDATA    FLOAT     q3
      0000008BH   EDATA    FLOAT     Q_angle
      0000009FH   EDATA    FLOAT     Q_bias
      000000AEH   EDATA    FLOAT     Q_gyro
      0000008FH   EDATA    FLOAT     R_angle
*SFR* 000000B0H.2 DATA     BIT       Reset_PIN
      00FF72A4H   CODE     ---       reverse2
      00FF67C7H   CODE     ---       reverse4
*SFR* 00000098H.0 DATA     BIT       RI
*SFR* 000000FFH   DATA     BYTE      RSTCFG
*SFR* 0000009BH   DATA     BYTE      S2BUF
*SFR* 0000009AH   DATA     BYTE      S2CON
*SFR* 0000009AH.0 DATA     BIT       S2RI
*SFR* 0000009AH.1 DATA     BIT       S2TI
*SFR* 000000ADH   DATA     BYTE      S3BUF
*SFR* 000000ACH   DATA     BYTE      S3CON
*SFR* 000000ACH.0 DATA     BIT       S3RI
*SFR* 000000ACH.1 DATA     BIT       S3TI
*SFR* 000000FEH   DATA     BYTE      S4BUF
*SFR* 000000FDH   DATA     BYTE      S4CON
*SFR* 000000FDH.0 DATA     BIT       S4RI
*SFR* 000000FDH.1 DATA     BIT       S4TI
*SFR* 00000099H   DATA     BYTE      SBUF
*SFR* 00000098H   DATA     BYTE      SCON
      00FF726FH   CODE     ---       SEG7_ShowCode
      00FF72D9H   CODE     ---       SEG7_ShowFloat
      00FF00EEH   CODE     ---       SEG7_ShowLong
      00FF6CFBH   CODE     ---       SEG7_ShowString
      000000C8H   EDATA    FLOAT     SET_ANG
      00FF4A26H   CODE     ---       set_clk
      00FF0006H   CODE     ---       Set_Motor_Speed
      000001F2H   EDATA    INT       set_spd
      0000023CH   EDATA    ---       Setup
      00FF7D59H   CODE     ---       sleep_ms
      00FF797CH   CODE     ---       sleep_us
*SFR* 000000CEH   DATA     BYTE      SPCTL
*SFR* 000000CFH   DATA     BYTE      SPDAT
      00FF0029H   CODE     ---       SPI_I
      00FF047BH   CODE     NEAR LAB  sprintf
*SFR* 000000CDH   DATA     BYTE      SPSTAT
      00FF000EH   CODE     ---       sq
      00FF04D4H   CODE     ---       sqrt?_
      00FF7FBFH   CODE     ---       strlen?_
      0000020DH   EDATA    DWORD     sys_clk
      00FF6392H   CODE     ---       System_init
      00000278H   EDATA    WORD      T0_cnt
      0000027AH   EDATA    WORD      T1_cnt
      0000027CH   EDATA    WORD      T2_cnt
*SFR* 000000D6H   DATA     BYTE      T2H
*SFR* 000000D7H   DATA     BYTE      T2L
*SFR* 0000008EH.4 DATA     BIT       T2R
*SFR* 0000008EH.2 DATA     BIT       T2x12
      0000027EH   EDATA    WORD      T3_cnt
*SFR* 000000D4H   DATA     BYTE      T3H
*SFR* 000000D5H   DATA     BYTE      T3L
*SFR* 000000DDH.3 DATA     BIT       T3R
*SFR* 000000DDH.1 DATA     BIT       T3x12
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 24


      00000280H   EDATA    WORD      T4_cnt
*SFR* 000000D2H   DATA     BYTE      T4H
*SFR* 000000D3H   DATA     BYTE      T4L
*SFR* 000000DDH.7 DATA     BIT       T4R
*SFR* 000000DDH   DATA     BYTE      T4T3M
*SFR* 000000DDH.5 DATA     BIT       T4x12
      00000027H   EDATA    FLOAT     t_0
      0000002DH   EDATA    FLOAT     t_1
      000001E0H   EDATA    LONG      target_pos
*SFR* 00000088H   DATA     BYTE      TCON
*SFR* 0000008CH   DATA     BYTE      TH0
*SFR* 0000008DH   DATA     BYTE      TH1
*SFR* 00000098H.1 DATA     BIT       TI
      00FF7203H   CODE     ---       TIMER0_I
      00FF0050H   CODE     ---       TIMER1_I
      00FF0051H   CODE     ---       TIMER2_I
      00FF0056H   CODE     ---       TIMER3_I
      00FF0058H   CODE     ---       TIMER4_I
*SFR* 0000008AH   DATA     BYTE      TL0
*SFR* 0000008BH   DATA     BYTE      TL1
      *EXTERN*    CODE     ---       TM0_isr
*SFR* 00000089H   DATA     BYTE      TMOD
*SFR* 00000088H.4 DATA     BIT       TR0
*SFR* 00000088H.6 DATA     BIT       TR1
      000002D4H   EDATA    BYTE      TSG1
      000002D5H   EDATA    BYTE      TSG2
      00FF7D29H   CODE     ---       UART1_I
      000002F5H   EDATA    BYTE      UART1_OK
      00FF7999H   CODE     ---       UART2_I
      000002F6H   EDATA    BYTE      UART2_OK
      00FF79B5H   CODE     ---       UART3_I
      000002F7H   EDATA    BYTE      UART3_OK
      00FF79D1H   CODE     ---       UART4_I
      000002F8H   EDATA    BYTE      UART4_OK
      00FF44CAH   CODE     ---       UART_init
      00FF6A8AH   CODE     ---       UART_Send_byte
      00FF1793H   CODE     ---       UART_Send_float
      00FF1FB0H   CODE     ---       UART_Send_int
      00FF7907H   CODE     ---       UART_Send_string
      00FF00AEH   CODE     ---       usb_bulk_intr_in
      00FF7BCBH   CODE     ---       usb_bulk_intr_out
      00FF5A0EH   CODE     ---       usb_clear_feature
      00FF66FAH   CODE     ---       usb_ctrl_in
      00FF6BCBH   CODE     ---       usb_ctrl_out
      00000021H.2 BIT      BIT       USB_flag
      00FF698FH   CODE     ---       usb_get_configuration
      00FF5AADH   CODE     ---       usb_get_descriptor
      00FF7066H   CODE     ---       usb_get_interface
      00FF765DH   CODE     ---       usb_get_line_coding
      00FF4F7FH   CODE     ---       usb_get_status
      00FF7401H   CODE     ---       usb_IN
      00FF745EH   CODE     ---       usb_in_ep1
      00FF7568H   CODE     ---       usb_in_ep2
      00FF6828H   CODE     ---       usb_init
      00FF611AH   CODE     ---       usb_isr
      00FF7D41H   CODE     ---       usb_OUT_done
      00FF55C8H   CODE     ---       usb_out_ep1
      00FF711CH   CODE     ---       usb_read_fifo?_
      00FF0166H   CODE     ---       usb_read_reg
      00FF7A78H   CODE     ---       usb_req_class
      00FF6886H   CODE     ---       usb_req_std
      00FF0046H   CODE     ---       usb_req_vendor
      00FF6D44H   CODE     ---       usb_reset
      00FF005EH   CODE     ---       usb_resume
      00FF7FE4H   CODE     ---       Usb_Rst
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 25


      00FF6402H   CODE     ---       USB_SendData
      00FF6CB1H   CODE     ---       usb_set_address
      00FF58C9H   CODE     ---       usb_set_configuration
      00FF7E41H   CODE     ---       usb_set_ctrl_line_state
      00FF0026H   CODE     ---       usb_set_descriptor
      00FF5B4CH   CODE     ---       usb_set_feature
      00FF760EH   CODE     ---       usb_set_interface
      00FF7683H   CODE     ---       usb_set_line_coding
      00FF6090H   CODE     ---       usb_setup
      00FF7EF7H   CODE     ---       usb_setup_in
      00FF7F87H   CODE     ---       usb_setup_out
      00FF7F95H   CODE     ---       usb_setup_stall
      00FF0036H   CODE     ---       usb_setup_status
      00FF005AH   CODE     ---       usb_suspend
      00FF002EH   CODE     ---       usb_synch_frame
      00FF7593H   CODE     ---       usb_write_fifo?_
      00FF7EBEH   CODE     ---       usb_write_reg
*SFR* 000000FCH   DATA     BYTE      USBADR
*SFR* 000000DCH   DATA     BYTE      USBCLK
*SFR* 000000F4H   DATA     BYTE      USBCON
*SFR* 000000ECH   DATA     BYTE      USBDAT
      00010080H   XDATA    ---       UsbFeatureBuffer
      00010000H   XDATA    ---       UsbInBuffer
      00010040H   XDATA    ---       UsbOutBuffer
      00000209H   EDATA    ---       USER_DEVICEDESC
      00000205H   EDATA    ---       USER_PRODUCTDESC
      000001FFH   EDATA    ---       USER_STCISPCMD
*SFR* 000000A6H   DATA     BYTE      VRTRIM
      00FF049BH   CODE     NEAR LAB  vsprintf
*SFR* 000000C1H   DATA     BYTE      WDT_CONTR
*SFR* 000000E9H   DATA     BYTE      WTST
      00FF6F66H   CODE     ---       Yijielvbo



UNRESOLVED EXTERNAL SYMBOLS:
   INT2_isr
   MOTOR_control
   TM0_isr



SYMBOL TABLE OF MODULE:  .\Objects\mode (main)

      VALUE       REP       CLASS    TYPE      SYMBOL NAME
      ====================================================
      ---         MODULE    ---      ---       main
      00FF73D2H   PUBLIC    CODE     ---       IR_Detection
      00FF5082H   PUBLIC    CODE     ---       Gripper_Control
      00FF4B43H   PUBLIC    CODE     ---       main
      00FF0006H   PUBLIC    CODE     ---       Set_Motor_Speed
      000001CEH   PUBLIC    EDATA    INT       gripper_state
      000001D0H   PUBLIC    EDATA    LONG      collision_pos
      000001D4H   PUBLIC    EDATA    LONG      motor_pos_old
      000001D8H   PUBLIC    EDATA    LONG      pwm_out
      000001DCH   PUBLIC    EDATA    INT       ir_stable_counter
      000001DEH   PUBLIC    EDATA    INT       collision_counter
      000001E0H   PUBLIC    EDATA    LONG      target_pos
      000001E4H   PUBLIC    EDATA    INT       motor_spd
      000001E6H   PUBLIC    EDATA    LONG      motor_pos
      000001EAH   PUBLIC    EDATA    INT       gripper_timeout
      000001ECH   PUBLIC    EDATA    ---       err_spd
      000001F2H   PUBLIC    EDATA    INT       set_spd
      00000020H.0 PUBLIC    BIT      BIT       ir_detected
      00000020H.1 PUBLIC    BIT      BIT       gripper_mode
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 26


      00000020H.2 PUBLIC    BIT      BIT       key1_old
      00000020H.3 PUBLIC    BIT      BIT       auto_mode
      00000020H.4 PUBLIC    BIT      BIT       key1_flag
      00000020H.5 PUBLIC    BIT      BIT       ir_old
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000B0H.3 SFRSYM    DATA     BIT       IR_SENSOR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      00000090H.1 SFRSYM    DATA     BIT       P11
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000AFH.7 SFRSYM    DATA     BIT       EUSB
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B0H.2 SFRSYM    DATA     BIT       KEY1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF4B43H   BLOCK     CODE     ---       LVL=0
      00FF4B43H   LINE      CODE     ---       #69
      00FF4B43H   LINE      CODE     ---       #71
      00FF4B46H   LINE      CODE     ---       #72
      00FF4B4EH   LINE      CODE     ---       #73
      00FF4B56H   LINE      CODE     ---       #75
      00FF4B63H   LINE      CODE     ---       #76
      00FF4B65H   LINE      CODE     ---       #79
      00FF4B6DH   LINE      CODE     ---       #80
      00FF4B75H   LINE      CODE     ---       #82
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 27


      00FF4B78H   LINE      CODE     ---       #83
      00FF4B7AH   LINE      CODE     ---       #85
      00FF4B7AH   LINE      CODE     ---       #88
      00FF4B7DH   LINE      CODE     ---       #91
      00FF4B85H   LINE      CODE     ---       #92
      00FF4B89H   LINE      CODE     ---       #93
      00FF4B8FH   LINE      CODE     ---       #94
      00FF4B91H   LINE      CODE     ---       #95
      00FF4B91H   LINE      CODE     ---       #99
      00FF4B94H   LINE      CODE     ---       #100
      00FF4B96H   LINE      CODE     ---       #101
      00FF4B9FH   LINE      CODE     ---       #102
      00FF4BA2H   LINE      CODE     ---       #103
      00FF4BA8H   LINE      CODE     ---       #104
      00FF4BACH   LINE      CODE     ---       #105
      00FF4BACH   LINE      CODE     ---       #109
      00FF4BAFH   LINE      CODE     ---       #112
      00FF4BBDH   LINE      CODE     ---       #114
      00FF4BCFH   LINE      CODE     ---       #115
      00FF4BD3H   LINE      CODE     ---       #117
      00FF4BDFH   LINE      CODE     ---       #119
      00FF4C1FH   LINE      CODE     ---       #121
      00FF4C27H   LINE      CODE     ---       #122
      00FF4C2BH   LINE      CODE     ---       #124
      00FF4C3DH   LINE      CODE     ---       #125
      00FF4C4FH   LINE      CODE     ---       #127
      00FF4C56H   LINE      CODE     ---       #129
      00FF4C58H   LINE      CODE     ---       #130
      ---         BLOCKEND  ---      ---       LVL=0

      00FF73D2H   BLOCK     CODE     ---       LVL=0
      00000022H.3 SYMBOL    BIT      BIT       ir_current
      00FF73D2H   LINE      CODE     ---       #140
      00FF73D2H   LINE      CODE     ---       #141
      00FF73D2H   LINE      CODE     ---       #142
      00FF73D6H   LINE      CODE     ---       #144
      00FF73DEH   LINE      CODE     ---       #145
      00FF73E2H   LINE      CODE     ---       #146
      00FF73E4H   LINE      CODE     ---       #147
      00FF73E6H   LINE      CODE     ---       #148
      00FF73F0H   LINE      CODE     ---       #149
      00FF73F6H   LINE      CODE     ---       #150
      00FF73F7H   LINE      CODE     ---       #151
      00FF7400H   LINE      CODE     ---       #152
      00FF7400H   LINE      CODE     ---       #153
      00FF7400H   LINE      CODE     ---       #154
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0006H   BLOCK     CODE     ---       LVL=0
      WR6         REGSYM    ---      INT       speed
      00FF0006H   LINE      CODE     ---       #162
      00FF0006H   LINE      CODE     ---       #164
      00FF000AH   LINE      CODE     ---       #165
      ---         BLOCKEND  ---      ---       LVL=0

      00FF5082H   BLOCK     CODE     ---       LVL=0
      00FF5082H   LINE      CODE     ---       #173
      00FF5082H   LINE      CODE     ---       #175
      00FF5088H   LINE      CODE     ---       #177
      00FF50A9H   LINE      CODE     ---       #178
      00FF50A9H   LINE      CODE     ---       #179
      00FF50AEH   LINE      CODE     ---       #180
      00FF50B4H   LINE      CODE     ---       #181
      00FF50BCH   LINE      CODE     ---       #182
      00FF50C2H   LINE      CODE     ---       #183
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 28


      00FF50C2H   LINE      CODE     ---       #184
      00FF50C3H   LINE      CODE     ---       #186
      00FF50C3H   LINE      CODE     ---       #187
      00FF50D0H   LINE      CODE     ---       #188
      00FF50D8H   LINE      CODE     ---       #189
      00FF50E0H   LINE      CODE     ---       #190
      00FF50E0H   LINE      CODE     ---       #191
      00FF50E8H   LINE      CODE     ---       #192
      00FF50E8H   LINE      CODE     ---       #193
      00FF50E8H   LINE      CODE     ---       #194
      00FF50EBH   LINE      CODE     ---       #196
      00FF50EBH   LINE      CODE     ---       #197
      00FF50F2H   LINE      CODE     ---       #198
      00FF50FCH   LINE      CODE     ---       #201
      00FF5112H   LINE      CODE     ---       #202
      00FF511AH   LINE      CODE     ---       #203
      00FF511AH   LINE      CODE     ---       #204
      00FF511AH   LINE      CODE     ---       #205
      00FF511CH   LINE      CODE     ---       #207
      00FF511CH   LINE      CODE     ---       #208
      00FF5123H   LINE      CODE     ---       #209
      00FF512DH   LINE      CODE     ---       #212
      00FF5133H   LINE      CODE     ---       #213
      00FF513BH   LINE      CODE     ---       #214
      00FF5147H   LINE      CODE     ---       #215
      00FF5149H   LINE      CODE     ---       #216
      00FF5149H   LINE      CODE     ---       #217
      00FF514BH   LINE      CODE     ---       #219
      00FF514BH   LINE      CODE     ---       #220
      00FF5152H   LINE      CODE     ---       #221
      00FF515CH   LINE      CODE     ---       #224
      00FF5172H   LINE      CODE     ---       #225
      00FF5178H   LINE      CODE     ---       #226
      00FF517CH   LINE      CODE     ---       #227
      00FF517CH   LINE      CODE     ---       #228
      00FF517DH   LINE      CODE     ---       #230
      00FF517DH   LINE      CODE     ---       #231
      00FF5183H   LINE      CODE     ---       #232
      00FF5183H   LINE      CODE     ---       #233
      00FF5183H   LINE      CODE     ---       #234
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       isr
      00FF001AH   PUBLIC    CODE     ---       IIC_I
      00FF0021H   PUBLIC    CODE     ---       CMP_I
      00FF0022H   PUBLIC    CODE     ---       LVD_I
      00FF0029H   PUBLIC    CODE     ---       SPI_I
      00FF7D29H   PUBLIC    CODE     ---       UART1_I
      00FF7999H   PUBLIC    CODE     ---       UART2_I
      00FF79B5H   PUBLIC    CODE     ---       UART3_I
      00FF79D1H   PUBLIC    CODE     ---       UART4_I
      00FF6E5EH   PUBLIC    CODE     ---       DMA_UART1_RXD_isr
      00FF6EA0H   PUBLIC    CODE     ---       DMA_UART2_RXD_isr
      00FF6EE2H   PUBLIC    CODE     ---       DMA_UART3_RXD_isr
      00FF0066H   PUBLIC    CODE     ---       DMA_UART1_TXD_isr
      00FF6F24H   PUBLIC    CODE     ---       DMA_UART4_RXD_isr
      00FF7925H   PUBLIC    CODE     ---       DMA_UART2_TXD_isr
      00FF7942H   PUBLIC    CODE     ---       DMA_UART3_TXD_isr
      00FF795FH   PUBLIC    CODE     ---       DMA_UART4_TXD_isr
      00FF002AH   PUBLIC    CODE     ---       DMA_ADC_isr
      00FF65B9H   PUBLIC    CODE     ---       CAN1_I
      00FF6624H   PUBLIC    CODE     ---       CAN2_I
      00FF0031H   PUBLIC    CODE     ---       DMA_M2M_isr
      00FF77F9H   PUBLIC    CODE     ---       PIN0_I
      00FF7817H   PUBLIC    CODE     ---       PIN1_I
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 29


      00FF7835H   PUBLIC    CODE     ---       PIN2_I
      00FF7853H   PUBLIC    CODE     ---       PIN3_I
      00FF7871H   PUBLIC    CODE     ---       PIN4_I
      00FF0032H   PUBLIC    CODE     ---       INT0_I
      00FF788FH   PUBLIC    CODE     ---       PIN5_I
      00FF0049H   PUBLIC    CODE     ---       INT1_I
      00FF78ADH   PUBLIC    CODE     ---       PIN6_I
      00FF73A2H   PUBLIC    CODE     ---       INT2_I
      00FF78CBH   PUBLIC    CODE     ---       PIN7_I
      00FF004AH   PUBLIC    CODE     ---       INT3_I
      00FF004EH   PUBLIC    CODE     ---       INT4_I
      00FF004FH   PUBLIC    CODE     ---       DMA_SPI_isr
      00FF7203H   PUBLIC    CODE     ---       TIMER0_I
      00FF0050H   PUBLIC    CODE     ---       TIMER1_I
      00FF0051H   PUBLIC    CODE     ---       TIMER2_I
      00FF0052H   PUBLIC    CODE     ---       PWMA_I
      00FF0056H   PUBLIC    CODE     ---       TIMER3_I
      00FF0057H   PUBLIC    CODE     ---       PWMB_I
      00FF0058H   PUBLIC    CODE     ---       TIMER4_I
      00FF0059H   PUBLIC    CODE     ---       ADC_I
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000097H.3 SFRSYM    DATA     BIT       CANSEL
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000FDH.1 SFRSYM    DATA     BIT       S4TI
      000000ACH.1 SFRSYM    DATA     BIT       S3TI
      0000009AH.1 SFRSYM    DATA     BIT       S2TI
      000000FDH.0 SFRSYM    DATA     BIT       S4RI
      000000ACH.0 SFRSYM    DATA     BIT       S3RI
      0000009AH.0 SFRSYM    DATA     BIT       S2RI
      00000098H.1 SFRSYM    DATA     BIT       TI
      00000098H.0 SFRSYM    DATA     BIT       RI
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 30


      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF0032H   BLOCK     CODE     ---       LVL=0
      00FF0032H   LINE      CODE     ---       #4
      00FF0032H   LINE      CODE     ---       #7
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7203H   BLOCK     CODE     ---       LVL=0
      00FF7203H   LINE      CODE     ---       #8
      00FF7219H   LINE      CODE     ---       #10
      00FF721CH   LINE      CODE     ---       #11
      00FF721FH   LINE      CODE     ---       #12
      00FF7222H   LINE      CODE     ---       #13
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0049H   BLOCK     CODE     ---       LVL=0
      00FF0049H   LINE      CODE     ---       #14
      00FF0049H   LINE      CODE     ---       #17
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0050H   BLOCK     CODE     ---       LVL=0
      00FF0050H   LINE      CODE     ---       #18
      00FF0050H   LINE      CODE     ---       #21
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7D29H   BLOCK     CODE     ---       LVL=0
      00FF7D29H   LINE      CODE     ---       #22
      00FF7D2DH   LINE      CODE     ---       #24
      00FF7D30H   LINE      CODE     ---       #26
      00FF7D32H   LINE      CODE     ---       #27
      00FF7D37H   LINE      CODE     ---       #28
      00FF7D37H   LINE      CODE     ---       #29
      00FF7D3AH   LINE      CODE     ---       #32
      00FF7D3CH   LINE      CODE     ---       #33
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0059H   BLOCK     CODE     ---       LVL=0
      00FF0059H   LINE      CODE     ---       #36
      00FF0059H   LINE      CODE     ---       #39
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0022H   BLOCK     CODE     ---       LVL=0
      00FF0022H   LINE      CODE     ---       #40
      00FF0022H   LINE      CODE     ---       #43
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7999H   BLOCK     CODE     ---       LVL=0
      00FF7999H   LINE      CODE     ---       #44
      00FF799DH   LINE      CODE     ---       #46
      00FF79A1H   LINE      CODE     ---       #48
      00FF79A4H   LINE      CODE     ---       #49
      00FF79A9H   LINE      CODE     ---       #50
      00FF79A9H   LINE      CODE     ---       #51
      00FF79ADH   LINE      CODE     ---       #54
      00FF79B0H   LINE      CODE     ---       #55
      ---         BLOCKEND  ---      ---       LVL=0

L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 31


      00FF0029H   BLOCK     CODE     ---       LVL=0
      00FF0029H   LINE      CODE     ---       #57
      00FF0029H   LINE      CODE     ---       #60
      ---         BLOCKEND  ---      ---       LVL=0

      00FF73A2H   BLOCK     CODE     ---       LVL=0
      00FF73A2H   LINE      CODE     ---       #61
      00FF73B8H   LINE      CODE     ---       #63
      00FF73BBH   LINE      CODE     ---       #64
      ---         BLOCKEND  ---      ---       LVL=0

      00FF004AH   BLOCK     CODE     ---       LVL=0
      00FF004AH   LINE      CODE     ---       #65
      00FF004AH   LINE      CODE     ---       #68
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0051H   BLOCK     CODE     ---       LVL=0
      00FF0051H   LINE      CODE     ---       #69
      00FF0051H   LINE      CODE     ---       #72
      ---         BLOCKEND  ---      ---       LVL=0

      00FF004EH   BLOCK     CODE     ---       LVL=0
      00FF004EH   LINE      CODE     ---       #73
      00FF004EH   LINE      CODE     ---       #76
      ---         BLOCKEND  ---      ---       LVL=0

      00FF79B5H   BLOCK     CODE     ---       LVL=0
      00FF79B5H   LINE      CODE     ---       #77
      00FF79B9H   LINE      CODE     ---       #79
      00FF79BDH   LINE      CODE     ---       #81
      00FF79C0H   LINE      CODE     ---       #82
      00FF79C5H   LINE      CODE     ---       #83
      00FF79C5H   LINE      CODE     ---       #84
      00FF79C9H   LINE      CODE     ---       #87
      00FF79CCH   LINE      CODE     ---       #88
      ---         BLOCKEND  ---      ---       LVL=0

      00FF79D1H   BLOCK     CODE     ---       LVL=0
      00FF79D1H   LINE      CODE     ---       #90
      00FF79D5H   LINE      CODE     ---       #92
      00FF79D9H   LINE      CODE     ---       #94
      00FF79DCH   LINE      CODE     ---       #95
      00FF79E1H   LINE      CODE     ---       #96
      00FF79E1H   LINE      CODE     ---       #97
      00FF79E5H   LINE      CODE     ---       #100
      00FF79E8H   LINE      CODE     ---       #101
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0056H   BLOCK     CODE     ---       LVL=0
      00FF0056H   LINE      CODE     ---       #103
      00FF0056H   LINE      CODE     ---       #106
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0058H   BLOCK     CODE     ---       LVL=0
      00FF0058H   LINE      CODE     ---       #107
      00FF0058H   LINE      CODE     ---       #110
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0021H   BLOCK     CODE     ---       LVL=0
      00FF0021H   LINE      CODE     ---       #111
      00FF0021H   LINE      CODE     ---       #114
      ---         BLOCKEND  ---      ---       LVL=0

      00FF001AH   BLOCK     CODE     ---       LVL=0
      00FF001AH   LINE      CODE     ---       #115
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 32


      00FF001AH   LINE      CODE     ---       #118
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0052H   BLOCK     CODE     ---       LVL=0
      00FF0052H   LINE      CODE     ---       #123
      00FF0052H   LINE      CODE     ---       #126
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0057H   BLOCK     CODE     ---       LVL=0
      00FF0057H   LINE      CODE     ---       #127
      00FF0057H   LINE      CODE     ---       #130
      ---         BLOCKEND  ---      ---       LVL=0

      00FF65B9H   BLOCK     CODE     ---       LVL=0
      00FF65CFH   BLOCK     CODE     NEAR LAB  LVL=1
      R10         REGSYM    ---      BYTE      isr
      ---         BLOCKEND  ---      ---       LVL=1
      00FF65B9H   LINE      CODE     ---       #132
      00FF65CFH   LINE      CODE     ---       #133
      00FF65CFH   LINE      CODE     ---       #136
      00FF65D2H   LINE      CODE     ---       #137
      00FF65D9H   LINE      CODE     ---       #138
      00FF65E6H   LINE      CODE     ---       #139
      00FF65F1H   LINE      CODE     ---       #141
      00FF65F8H   LINE      CODE     ---       #143
      00FF65FAH   LINE      CODE     ---       #144
      00FF65FAH   LINE      CODE     ---       #145
      00FF65FAH   LINE      CODE     ---       #148
      00FF65FAH   LINE      CODE     ---       #149
      00FF65FAH   LINE      CODE     ---       #152
      00FF65FAH   LINE      CODE     ---       #153
      00FF6601H   LINE      CODE     ---       #155
      00FF6605H   LINE      CODE     ---       #156
      00FF660DH   LINE      CODE     ---       #157
      00FF660DH   LINE      CODE     ---       #158
      00FF660DH   LINE      CODE     ---       #161
      00FF660DH   LINE      CODE     ---       #162
      00FF660DH   LINE      CODE     ---       #165
      00FF660DH   LINE      CODE     ---       #166
      00FF660DH   LINE      CODE     ---       #169
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6624H   BLOCK     CODE     ---       LVL=0
      00FF663AH   BLOCK     CODE     NEAR LAB  LVL=1
      R10         REGSYM    ---      BYTE      isr
      ---         BLOCKEND  ---      ---       LVL=1
      00FF6624H   LINE      CODE     ---       #172
      00FF663AH   LINE      CODE     ---       #173
      00FF663AH   LINE      CODE     ---       #176
      00FF663DH   LINE      CODE     ---       #177
      00FF6644H   LINE      CODE     ---       #178
      00FF6651H   LINE      CODE     ---       #179
      00FF665CH   LINE      CODE     ---       #181
      00FF6663H   LINE      CODE     ---       #183
      00FF6665H   LINE      CODE     ---       #184
      00FF6665H   LINE      CODE     ---       #185
      00FF6665H   LINE      CODE     ---       #188
      00FF6665H   LINE      CODE     ---       #189
      00FF6665H   LINE      CODE     ---       #192
      00FF6665H   LINE      CODE     ---       #193
      00FF666CH   LINE      CODE     ---       #195
      00FF6670H   LINE      CODE     ---       #196
      00FF6678H   LINE      CODE     ---       #197
      00FF6678H   LINE      CODE     ---       #198
      00FF6678H   LINE      CODE     ---       #201
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 33


      00FF6678H   LINE      CODE     ---       #202
      00FF6678H   LINE      CODE     ---       #205
      00FF6678H   LINE      CODE     ---       #206
      00FF6678H   LINE      CODE     ---       #209
      ---         BLOCKEND  ---      ---       LVL=0

      00FF77F9H   BLOCK     CODE     ---       LVL=0
      00FF77FFH   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      intf
      ---         BLOCKEND  ---      ---       LVL=1
      00FF77F9H   LINE      CODE     ---       #212
      00FF77FFH   LINE      CODE     ---       #213
      00FF77FFH   LINE      CODE     ---       #215
      00FF780AH   LINE      CODE     ---       #216
      00FF780CH   LINE      CODE     ---       #218
      00FF7810H   LINE      CODE     ---       #219
      00FF7810H   LINE      CODE     ---       #222
      00FF7810H   LINE      CODE     ---       #223
      00FF7810H   LINE      CODE     ---       #226
      00FF7810H   LINE      CODE     ---       #227
      00FF7810H   LINE      CODE     ---       #230
      00FF7810H   LINE      CODE     ---       #231
      00FF7810H   LINE      CODE     ---       #234
      00FF7810H   LINE      CODE     ---       #235
      00FF7810H   LINE      CODE     ---       #238
      00FF7810H   LINE      CODE     ---       #239
      00FF7810H   LINE      CODE     ---       #242
      00FF7810H   LINE      CODE     ---       #243
      00FF7810H   LINE      CODE     ---       #246
      00FF7810H   LINE      CODE     ---       #247
      00FF7810H   LINE      CODE     ---       #250
      00FF7810H   LINE      CODE     ---       #252
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7817H   BLOCK     CODE     ---       LVL=0
      00FF781DH   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      intf
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7817H   LINE      CODE     ---       #253
      00FF781DH   LINE      CODE     ---       #254
      00FF781DH   LINE      CODE     ---       #256
      00FF7828H   LINE      CODE     ---       #257
      00FF782AH   LINE      CODE     ---       #259
      00FF782EH   LINE      CODE     ---       #260
      00FF782EH   LINE      CODE     ---       #263
      00FF782EH   LINE      CODE     ---       #264
      00FF782EH   LINE      CODE     ---       #267
      00FF782EH   LINE      CODE     ---       #268
      00FF782EH   LINE      CODE     ---       #271
      00FF782EH   LINE      CODE     ---       #272
      00FF782EH   LINE      CODE     ---       #275
      00FF782EH   LINE      CODE     ---       #276
      00FF782EH   LINE      CODE     ---       #279
      00FF782EH   LINE      CODE     ---       #280
      00FF782EH   LINE      CODE     ---       #283
      00FF782EH   LINE      CODE     ---       #284
      00FF782EH   LINE      CODE     ---       #287
      00FF782EH   LINE      CODE     ---       #288
      00FF782EH   LINE      CODE     ---       #291
      00FF782EH   LINE      CODE     ---       #293
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7835H   BLOCK     CODE     ---       LVL=0
      00FF783BH   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      intf
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 34


      ---         BLOCKEND  ---      ---       LVL=1
      00FF7835H   LINE      CODE     ---       #294
      00FF783BH   LINE      CODE     ---       #295
      00FF783BH   LINE      CODE     ---       #297
      00FF7846H   LINE      CODE     ---       #298
      00FF7848H   LINE      CODE     ---       #300
      00FF784CH   LINE      CODE     ---       #301
      00FF784CH   LINE      CODE     ---       #304
      00FF784CH   LINE      CODE     ---       #305
      00FF784CH   LINE      CODE     ---       #308
      00FF784CH   LINE      CODE     ---       #309
      00FF784CH   LINE      CODE     ---       #312
      00FF784CH   LINE      CODE     ---       #313
      00FF784CH   LINE      CODE     ---       #316
      00FF784CH   LINE      CODE     ---       #317
      00FF784CH   LINE      CODE     ---       #320
      00FF784CH   LINE      CODE     ---       #321
      00FF784CH   LINE      CODE     ---       #324
      00FF784CH   LINE      CODE     ---       #325
      00FF784CH   LINE      CODE     ---       #328
      00FF784CH   LINE      CODE     ---       #329
      00FF784CH   LINE      CODE     ---       #332
      00FF784CH   LINE      CODE     ---       #334
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7853H   BLOCK     CODE     ---       LVL=0
      00FF7859H   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      intf
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7853H   LINE      CODE     ---       #335
      00FF7859H   LINE      CODE     ---       #336
      00FF7859H   LINE      CODE     ---       #338
      00FF7864H   LINE      CODE     ---       #339
      00FF7866H   LINE      CODE     ---       #341
      00FF786AH   LINE      CODE     ---       #342
      00FF786AH   LINE      CODE     ---       #345
      00FF786AH   LINE      CODE     ---       #346
      00FF786AH   LINE      CODE     ---       #349
      00FF786AH   LINE      CODE     ---       #350
      00FF786AH   LINE      CODE     ---       #353
      00FF786AH   LINE      CODE     ---       #354
      00FF786AH   LINE      CODE     ---       #357
      00FF786AH   LINE      CODE     ---       #358
      00FF786AH   LINE      CODE     ---       #361
      00FF786AH   LINE      CODE     ---       #362
      00FF786AH   LINE      CODE     ---       #365
      00FF786AH   LINE      CODE     ---       #366
      00FF786AH   LINE      CODE     ---       #369
      00FF786AH   LINE      CODE     ---       #370
      00FF786AH   LINE      CODE     ---       #373
      00FF786AH   LINE      CODE     ---       #375
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7871H   BLOCK     CODE     ---       LVL=0
      00FF7877H   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      intf
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7871H   LINE      CODE     ---       #376
      00FF7877H   LINE      CODE     ---       #377
      00FF7877H   LINE      CODE     ---       #379
      00FF7882H   LINE      CODE     ---       #380
      00FF7884H   LINE      CODE     ---       #382
      00FF7888H   LINE      CODE     ---       #383
      00FF7888H   LINE      CODE     ---       #386
      00FF7888H   LINE      CODE     ---       #387
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 35


      00FF7888H   LINE      CODE     ---       #390
      00FF7888H   LINE      CODE     ---       #391
      00FF7888H   LINE      CODE     ---       #394
      00FF7888H   LINE      CODE     ---       #395
      00FF7888H   LINE      CODE     ---       #398
      00FF7888H   LINE      CODE     ---       #399
      00FF7888H   LINE      CODE     ---       #402
      00FF7888H   LINE      CODE     ---       #403
      00FF7888H   LINE      CODE     ---       #406
      00FF7888H   LINE      CODE     ---       #407
      00FF7888H   LINE      CODE     ---       #410
      00FF7888H   LINE      CODE     ---       #411
      00FF7888H   LINE      CODE     ---       #414
      00FF7888H   LINE      CODE     ---       #416
      ---         BLOCKEND  ---      ---       LVL=0

      00FF788FH   BLOCK     CODE     ---       LVL=0
      00FF7895H   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      intf
      ---         BLOCKEND  ---      ---       LVL=1
      00FF788FH   LINE      CODE     ---       #417
      00FF7895H   LINE      CODE     ---       #418
      00FF7895H   LINE      CODE     ---       #420
      00FF78A0H   LINE      CODE     ---       #421
      00FF78A2H   LINE      CODE     ---       #423
      00FF78A6H   LINE      CODE     ---       #424
      00FF78A6H   LINE      CODE     ---       #427
      00FF78A6H   LINE      CODE     ---       #428
      00FF78A6H   LINE      CODE     ---       #431
      00FF78A6H   LINE      CODE     ---       #432
      00FF78A6H   LINE      CODE     ---       #435
      00FF78A6H   LINE      CODE     ---       #436
      00FF78A6H   LINE      CODE     ---       #439
      00FF78A6H   LINE      CODE     ---       #440
      00FF78A6H   LINE      CODE     ---       #443
      00FF78A6H   LINE      CODE     ---       #444
      00FF78A6H   LINE      CODE     ---       #447
      00FF78A6H   LINE      CODE     ---       #449
      ---         BLOCKEND  ---      ---       LVL=0

      00FF78ADH   BLOCK     CODE     ---       LVL=0
      00FF78B3H   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      intf
      ---         BLOCKEND  ---      ---       LVL=1
      00FF78ADH   LINE      CODE     ---       #450
      00FF78B3H   LINE      CODE     ---       #451
      00FF78B3H   LINE      CODE     ---       #453
      00FF78BEH   LINE      CODE     ---       #454
      00FF78C0H   LINE      CODE     ---       #456
      00FF78C4H   LINE      CODE     ---       #457
      00FF78C4H   LINE      CODE     ---       #460
      00FF78C4H   LINE      CODE     ---       #461
      00FF78C4H   LINE      CODE     ---       #464
      00FF78C4H   LINE      CODE     ---       #465
      00FF78C4H   LINE      CODE     ---       #468
      00FF78C4H   LINE      CODE     ---       #469
      00FF78C4H   LINE      CODE     ---       #472
      00FF78C4H   LINE      CODE     ---       #473
      00FF78C4H   LINE      CODE     ---       #476
      00FF78C4H   LINE      CODE     ---       #477
      00FF78C4H   LINE      CODE     ---       #480
      00FF78C4H   LINE      CODE     ---       #481
      00FF78C4H   LINE      CODE     ---       #484
      00FF78C4H   LINE      CODE     ---       #485
      00FF78C4H   LINE      CODE     ---       #488
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 36


      00FF78C4H   LINE      CODE     ---       #490
      ---         BLOCKEND  ---      ---       LVL=0

      00FF78CBH   BLOCK     CODE     ---       LVL=0
      00FF78D1H   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      intf
      ---         BLOCKEND  ---      ---       LVL=1
      00FF78CBH   LINE      CODE     ---       #491
      00FF78D1H   LINE      CODE     ---       #492
      00FF78D1H   LINE      CODE     ---       #494
      00FF78DCH   LINE      CODE     ---       #495
      00FF78DEH   LINE      CODE     ---       #497
      00FF78E2H   LINE      CODE     ---       #498
      00FF78E2H   LINE      CODE     ---       #501
      00FF78E2H   LINE      CODE     ---       #502
      00FF78E2H   LINE      CODE     ---       #505
      00FF78E2H   LINE      CODE     ---       #506
      00FF78E2H   LINE      CODE     ---       #509
      00FF78E2H   LINE      CODE     ---       #510
      00FF78E2H   LINE      CODE     ---       #513
      00FF78E2H   LINE      CODE     ---       #514
      00FF78E2H   LINE      CODE     ---       #517
      00FF78E2H   LINE      CODE     ---       #518
      00FF78E2H   LINE      CODE     ---       #521
      00FF78E2H   LINE      CODE     ---       #522
      00FF78E2H   LINE      CODE     ---       #525
      00FF78E2H   LINE      CODE     ---       #526
      00FF78E2H   LINE      CODE     ---       #529
      00FF78E2H   LINE      CODE     ---       #531
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0031H   BLOCK     CODE     ---       LVL=0
      00FF0031H   LINE      CODE     ---       #535
      00FF0031H   LINE      CODE     ---       #538
      ---         BLOCKEND  ---      ---       LVL=0

      00FF002AH   BLOCK     CODE     ---       LVL=0
      00FF002AH   LINE      CODE     ---       #540
      00FF002AH   LINE      CODE     ---       #543
      ---         BLOCKEND  ---      ---       LVL=0

      00FF004FH   BLOCK     CODE     ---       LVL=0
      00FF004FH   LINE      CODE     ---       #545
      00FF004FH   LINE      CODE     ---       #548
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0066H   BLOCK     CODE     ---       LVL=0
      00FF0066H   LINE      CODE     ---       #550
      00FF006CH   LINE      CODE     ---       #552
      00FF007CH   LINE      CODE     ---       #553
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6E5EH   BLOCK     CODE     ---       LVL=0
      00FF6E5EH   LINE      CODE     ---       #554
      00FF6E66H   LINE      CODE     ---       #556
      00FF6E74H   LINE      CODE     ---       #558
      00FF6E7CH   LINE      CODE     ---       #560
      00FF6E89H   LINE      CODE     ---       #561
      00FF6E89H   LINE      CODE     ---       #562
      00FF6E8FH   LINE      CODE     ---       #564
      00FF6E97H   LINE      CODE     ---       #565
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7925H   BLOCK     CODE     ---       LVL=0
      00FF7925H   LINE      CODE     ---       #568
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 37


      00FF792BH   LINE      CODE     ---       #570
      00FF793BH   LINE      CODE     ---       #571
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6EA0H   BLOCK     CODE     ---       LVL=0
      00FF6EA0H   LINE      CODE     ---       #572
      00FF6EA8H   LINE      CODE     ---       #574
      00FF6EB6H   LINE      CODE     ---       #576
      00FF6EBEH   LINE      CODE     ---       #578
      00FF6ECBH   LINE      CODE     ---       #579
      00FF6ECBH   LINE      CODE     ---       #580
      00FF6ED1H   LINE      CODE     ---       #582
      00FF6ED9H   LINE      CODE     ---       #583
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7942H   BLOCK     CODE     ---       LVL=0
      00FF7942H   LINE      CODE     ---       #585
      00FF7948H   LINE      CODE     ---       #587
      00FF7958H   LINE      CODE     ---       #588
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6EE2H   BLOCK     CODE     ---       LVL=0
      00FF6EE2H   LINE      CODE     ---       #589
      00FF6EEAH   LINE      CODE     ---       #591
      00FF6EF8H   LINE      CODE     ---       #593
      00FF6F00H   LINE      CODE     ---       #595
      00FF6F0DH   LINE      CODE     ---       #596
      00FF6F0DH   LINE      CODE     ---       #597
      00FF6F13H   LINE      CODE     ---       #599
      00FF6F1BH   LINE      CODE     ---       #600
      ---         BLOCKEND  ---      ---       LVL=0

      00FF795FH   BLOCK     CODE     ---       LVL=0
      00FF795FH   LINE      CODE     ---       #602
      00FF7965H   LINE      CODE     ---       #604
      00FF7975H   LINE      CODE     ---       #605
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6F24H   BLOCK     CODE     ---       LVL=0
      00FF6F24H   LINE      CODE     ---       #606
      00FF6F2CH   LINE      CODE     ---       #608
      00FF6F3AH   LINE      CODE     ---       #610
      00FF6F42H   LINE      CODE     ---       #612
      00FF6F4FH   LINE      CODE     ---       #613
      00FF6F4FH   LINE      CODE     ---       #614
      00FF6F55H   LINE      CODE     ---       #616
      00FF6F5DH   LINE      CODE     ---       #617
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       config
      00FF001EH   PUBLIC    CODE     ---       HID_isr
      00FF6392H   PUBLIC    CODE     ---       System_init
      00FF7F69H   PUBLIC    CODE     ---       fmax
      00FF7D89H   PUBLIC    CODE     ---       Limit_int
      00FF7027H   PUBLIC    CODE     ---       Key_Rst
      00FF7FE4H   PUBLIC    CODE     ---       Usb_Rst
      00FF7DA0H   PUBLIC    CODE     ---       HEX2BCD
      00FF7A5DH   PUBLIC    CODE     ---       BCD2HEX
      00FF000EH   PUBLIC    CODE     ---       sq
      00FF4A26H   PUBLIC    CODE     ---       set_clk
      00FF7735H   PUBLIC    CODE     ---       Limit_float
      00000021H.1 PUBLIC    BIT      BIT       Key_Flag
      00000021H.2 PUBLIC    BIT      BIT       USB_flag
      000001F4H   PUBLIC    EDATA    ---       float_uchar
      000001F8H   PUBLIC    EDATA    ---       char_uchar
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 38


      000001F9H   PUBLIC    EDATA    ---       long_uchar
      000001FDH   PUBLIC    EDATA    ---       int_uchar
      000001FFH   PUBLIC    EDATA    ---       USER_STCISPCMD
      00000203H   PUBLIC    EDATA    WORD      Key_cnt
      00000205H   PUBLIC    EDATA    ---       USER_PRODUCTDESC
      00000209H   PUBLIC    EDATA    ---       USER_DEVICEDESC
      0000020DH   PUBLIC    EDATA    DWORD     sys_clk
      000002D1H   PUBLIC    EDATA    BYTE      ?Limit_int?BYTE
      000002D5H   PUBLIC    EDATA    BYTE      ?Limit_float?BYTE
      00000094H   SFRSYM    DATA     BYTE      P0M0
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000BAH.7 SFRSYM    DATA     BIT       EAXFR
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EAH   SFRSYM    DATA     BYTE      CKCON
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000A6H   SFRSYM    DATA     BYTE      VRTRIM
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      0000009FH   SFRSYM    DATA     BYTE      IRTRIM
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000E9H   SFRSYM    DATA     BYTE      WTST
      000000B0H.2 SFRSYM    DATA     BIT       Reset_PIN
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000DCH   SFRSYM    DATA     BYTE      USBCLK
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      000000B5H.7 SFRSYM    DATA     BIT       PUSB
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000AFH.7 SFRSYM    DATA     BIT       EUSB
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E1H   SFRSYM    DATA     BYTE      P7M1
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000CBH   SFRSYM    DATA     BYTE      P6M1
      000000E2H   SFRSYM    DATA     BYTE      P7M0
      000000B6H.7 SFRSYM    DATA     BIT       PUSBH
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000C9H   SFRSYM    DATA     BYTE      P5M1
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 39


      000000CCH   SFRSYM    DATA     BYTE      P6M0
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000B3H   SFRSYM    DATA     BYTE      P4M1
      000000CAH   SFRSYM    DATA     BYTE      P5M0
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B1H   SFRSYM    DATA     BYTE      P3M1
      000000B4H   SFRSYM    DATA     BYTE      P4M0
      00000090H   SFRSYM    DATA     BYTE      P1
      00000095H   SFRSYM    DATA     BYTE      P2M1
      000000B2H   SFRSYM    DATA     BYTE      P3M0
      00000080H   SFRSYM    DATA     BYTE      P0
      00000091H   SFRSYM    DATA     BYTE      P1M1
      00000096H   SFRSYM    DATA     BYTE      P2M0
      00000092H   SFRSYM    DATA     BYTE      P1M0
      00000093H   SFRSYM    DATA     BYTE      P0M1

      00FF6392H   BLOCK     CODE     ---       LVL=0
      00FF6392H   LINE      CODE     ---       #31
      00FF6392H   LINE      CODE     ---       #36
      00FF6395H   LINE      CODE     ---       #37
      00FF6398H   LINE      CODE     ---       #38
      00FF639BH   LINE      CODE     ---       #40
      00FF63A1H   LINE      CODE     ---       #41
      00FF63A7H   LINE      CODE     ---       #42
      00FF63ADH   LINE      CODE     ---       #43
      00FF63B3H   LINE      CODE     ---       #44
      00FF63B9H   LINE      CODE     ---       #45
      00FF63BFH   LINE      CODE     ---       #46
      00FF63C5H   LINE      CODE     ---       #47
      00FF63CBH   LINE      CODE     ---       #50
      00FF63CEH   LINE      CODE     ---       #51
      00FF63D1H   LINE      CODE     ---       #52
      00FF63D4H   LINE      CODE     ---       #53
      00FF63E1H   LINE      CODE     ---       #54
      00FF63EFH   LINE      CODE     ---       #55
      00FF63F2H   LINE      CODE     ---       #56
      00FF63F5H   LINE      CODE     ---       #57
      00FF63F8H   LINE      CODE     ---       #60
      00FF63FBH   LINE      CODE     ---       #61
      00FF63FEH   LINE      CODE     ---       #62
      00FF6401H   LINE      CODE     ---       #63
      ---         BLOCKEND  ---      ---       LVL=0

      00FF001EH   BLOCK     CODE     ---       LVL=0
      00FF001EH   LINE      CODE     ---       #65
      00FF001EH   LINE      CODE     ---       #67
      00FF0020H   LINE      CODE     ---       #68
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7FE4H   BLOCK     CODE     ---       LVL=0
      00FF7FE4H   LINE      CODE     ---       #70
      00FF7FE4H   LINE      CODE     ---       #72
      00FF7FE7H   LINE      CODE     ---       #74
      00FF7FEAH   LINE      CODE     ---       #76
      00FF7FEDH   LINE      CODE     ---       #77
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7027H   BLOCK     CODE     ---       LVL=0
      00FF7027H   LINE      CODE     ---       #80
      00FF7027H   LINE      CODE     ---       #82
      00FF702AH   LINE      CODE     ---       #84
      00FF702DH   LINE      CODE     ---       #86
      00FF7037H   LINE      CODE     ---       #87
      00FF703DH   LINE      CODE     ---       #89
      00FF703FH   LINE      CODE     ---       #91
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 40


      00FF7042H   LINE      CODE     ---       #92
      00FF7045H   LINE      CODE     ---       #93
      00FF7051H   LINE      CODE     ---       #95
      00FF7058H   LINE      CODE     ---       #96
      00FF705BH   LINE      CODE     ---       #97
      00FF705DH   LINE      CODE     ---       #103
      00FF7063H   LINE      CODE     ---       #104
      00FF7065H   LINE      CODE     ---       #105
      00FF7065H   LINE      CODE     ---       #106
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7F69H   BLOCK     CODE     ---       LVL=0
      DR24        REGSYM    ---      FLOAT     a
      DR28        REGSYM    ---      FLOAT     b
      00FF7F69H   LINE      CODE     ---       #109
      00FF7F6DH   LINE      CODE     ---       #110
      00FF7F77H   LINE      CODE     ---       #111
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7D89H   BLOCK     CODE     ---       LVL=0
      DR28        REGSYM    ---      LONG      min
      DR8         REGSYM    ---      LONG      num
      000002D9H   SYMBOL    EDATA    LONG      max
      00FF7D89H   LINE      CODE     ---       #113
      00FF7D8DH   LINE      CODE     ---       #115
      00FF7D97H   LINE      CODE     ---       #116
      00FF7D9DH   LINE      CODE     ---       #117
      00FF7D9FH   LINE      CODE     ---       #118
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7735H   BLOCK     CODE     ---       LVL=0
      DR24        REGSYM    ---      FLOAT     min
      DR28        REGSYM    ---      FLOAT     num
      000002DDH   SYMBOL    EDATA    FLOAT     max
      00FF7735H   LINE      CODE     ---       #120
      00FF7739H   LINE      CODE     ---       #122
      00FF7748H   LINE      CODE     ---       #123
      00FF7753H   LINE      CODE     ---       #124
      00FF7755H   LINE      CODE     ---       #125
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7A5DH   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      val
      R11         REGSYM    ---      BYTE      L
      00FF7A5DH   LINE      CODE     ---       #127
      00FF7A5DH   LINE      CODE     ---       #128
      00FF7A5DH   LINE      CODE     ---       #130
      00FF7A77H   LINE      CODE     ---       #131
      00FF7A77H   LINE      CODE     ---       #132
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7DA0H   BLOCK     CODE     ---       LVL=0
      R7          REGSYM    ---      BYTE      val
      R11         REGSYM    ---      BYTE      L
      00FF7DA0H   LINE      CODE     ---       #134
      00FF7DA0H   LINE      CODE     ---       #135
      00FF7DA0H   LINE      CODE     ---       #137
      00FF7DB6H   LINE      CODE     ---       #138
      00FF7DB6H   LINE      CODE     ---       #139
      ---         BLOCKEND  ---      ---       LVL=0

      00FF000EH   BLOCK     CODE     ---       LVL=0
      DR4         REGSYM    ---      FLOAT     num
      00FF000EH   LINE      CODE     ---       #141
      00FF000EH   LINE      CODE     ---       #143
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 41


      ---         BLOCKEND  ---      ---       LVL=0

      00FF4A26H   BLOCK     CODE     ---       LVL=0
      00FF4A26H   LINE      CODE     ---       #146
      00FF4A26H   LINE      CODE     ---       #149
      00FF4A29H   LINE      CODE     ---       #151
      00FF4A39H   LINE      CODE     ---       #154
      00FF4A46H   LINE      CODE     ---       #155
      00FF4A4AH   LINE      CODE     ---       #156
      00FF4A4AH   LINE      CODE     ---       #157
      00FF4A4AH   LINE      CODE     ---       #158
      00FF4A4AH   LINE      CODE     ---       #159
      00FF4A4CH   LINE      CODE     ---       #160
      00FF4A58H   LINE      CODE     ---       #163
      00FF4A65H   LINE      CODE     ---       #164
      00FF4A69H   LINE      CODE     ---       #165
      00FF4A69H   LINE      CODE     ---       #166
      00FF4A69H   LINE      CODE     ---       #167
      00FF4A69H   LINE      CODE     ---       #168
      00FF4A6BH   LINE      CODE     ---       #169
      00FF4A77H   LINE      CODE     ---       #172
      00FF4A84H   LINE      CODE     ---       #173
      00FF4A88H   LINE      CODE     ---       #174
      00FF4A88H   LINE      CODE     ---       #175
      00FF4A88H   LINE      CODE     ---       #176
      00FF4A88H   LINE      CODE     ---       #177
      00FF4A8AH   LINE      CODE     ---       #178
      00FF4A96H   LINE      CODE     ---       #182
      00FF4AA3H   LINE      CODE     ---       #183
      00FF4AA7H   LINE      CODE     ---       #184
      00FF4AA7H   LINE      CODE     ---       #185
      00FF4AA7H   LINE      CODE     ---       #186
      00FF4AA7H   LINE      CODE     ---       #187
      00FF4AA9H   LINE      CODE     ---       #188
      00FF4AB5H   LINE      CODE     ---       #191
      00FF4AC2H   LINE      CODE     ---       #192
      00FF4ACFH   LINE      CODE     ---       #193
      00FF4AD8H   LINE      CODE     ---       #194
      00FF4ADBH   LINE      CODE     ---       #195
      00FF4ADBH   LINE      CODE     ---       #196
      00FF4ADDH   LINE      CODE     ---       #197
      00FF4AE9H   LINE      CODE     ---       #200
      00FF4AF6H   LINE      CODE     ---       #201
      00FF4B03H   LINE      CODE     ---       #202
      00FF4B0CH   LINE      CODE     ---       #203
      00FF4B0FH   LINE      CODE     ---       #204
      00FF4B13H   LINE      CODE     ---       #205
      00FF4B14H   LINE      CODE     ---       #208
      00FF4B18H   LINE      CODE     ---       #210
      00FF4B25H   LINE      CODE     ---       #211
      00FF4B32H   LINE      CODE     ---       #212
      00FF4B3BH   LINE      CODE     ---       #213
      00FF4B3EH   LINE      CODE     ---       #214
      00FF4B42H   LINE      CODE     ---       #215
      00FF4B42H   LINE      CODE     ---       #216
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       usb
      00FF7EF7H   PUBLIC    CODE     ---       usb_setup_in
      00FF7F87H   PUBLIC    CODE     ---       usb_setup_out
      00FF66FAH   PUBLIC    CODE     ---       usb_ctrl_in
      00FF6BCBH   PUBLIC    CODE     ---       usb_ctrl_out
      00FF7D41H   PUBLIC    CODE     ---       usb_OUT_done
      00FF005AH   PUBLIC    CODE     ---       usb_suspend
      00FF00AEH   PUBLIC    CODE     ---       usb_bulk_intr_in
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 42


      00FF7BCBH   PUBLIC    CODE     ---       usb_bulk_intr_out
      00FF005EH   PUBLIC    CODE     ---       usb_resume
      00FF7401H   PUBLIC    CODE     ---       usb_IN
      00FF711CH   PUBLIC    CODE     ---       usb_read_fifo?_
      00FF6D44H   PUBLIC    CODE     ---       usb_reset
      00FF6402H   PUBLIC    CODE     ---       USB_SendData
      00FF6090H   PUBLIC    CODE     ---       usb_setup
      00FF7593H   PUBLIC    CODE     ---       usb_write_fifo?_
      00FF6828H   PUBLIC    CODE     ---       usb_init
      00FF7F95H   PUBLIC    CODE     ---       usb_setup_stall
      00FF0036H   PUBLIC    CODE     ---       usb_setup_status
      00FF0166H   PUBLIC    CODE     ---       usb_read_reg
      00FF745EH   PUBLIC    CODE     ---       usb_in_ep1
      00FF7568H   PUBLIC    CODE     ---       usb_in_ep2
      00FF55C8H   PUBLIC    CODE     ---       usb_out_ep1
      00FF7EBEH   PUBLIC    CODE     ---       usb_write_reg
      00FF611AH   PUBLIC    CODE     ---       usb_isr
      0000023CH   PUBLIC    EDATA    ---       Setup
      00000244H   PUBLIC    EDATA    BYTE      OutNumber
      00000246H   PUBLIC    EDATA    BYTE      DeviceState
      00000247H   PUBLIC    EDATA    ---       Ep0State
      0000024EH   PUBLIC    EDATA    BYTE      InEpState
      0000024FH   PUBLIC    EDATA    BYTE      OutEpState
      00000020H.6 PUBLIC    BIT      BIT       bUsbOutReady
      00000020H.7 PUBLIC    BIT      BIT       bUsbFeatureReady
      00000021H.0 PUBLIC    BIT      BIT       bUsbInBusy
      00010000H   PUBLIC    XDATA    ---       UsbInBuffer
      00010040H   PUBLIC    XDATA    ---       UsbOutBuffer
      00010080H   PUBLIC    XDATA    ---       UsbFeatureBuffer
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000DCH   SFRSYM    DATA     BYTE      USBCLK
      000000ECH   SFRSYM    DATA     BYTE      USBDAT
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      000000FCH   SFRSYM    DATA     BYTE      USBADR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000AFH.7 SFRSYM    DATA     BIT       EUSB
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 43


      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      ---         MODULE    ---      ---       usb_req_class
      00FF7E41H   PUBLIC    CODE     ---       usb_set_ctrl_line_state
      00FF765DH   PUBLIC    CODE     ---       usb_get_line_coding
      00FF7683H   PUBLIC    CODE     ---       usb_set_line_coding
      00FF7A78H   PUBLIC    CODE     ---       usb_req_class
      000002B3H   PUBLIC    EDATA    ---       LineCoding
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      ---         MODULE    ---      ---       usb_req_std
      00FF7066H   PUBLIC    CODE     ---       usb_get_interface
      00FF5A0EH   PUBLIC    CODE     ---       usb_clear_feature
      00FF760EH   PUBLIC    CODE     ---       usb_set_interface
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 44


      00FF5AADH   PUBLIC    CODE     ---       usb_get_descriptor
      00FF0026H   PUBLIC    CODE     ---       usb_set_descriptor
      00FF6886H   PUBLIC    CODE     ---       usb_req_std
      00FF002EH   PUBLIC    CODE     ---       usb_synch_frame
      00FF6CB1H   PUBLIC    CODE     ---       usb_set_address
      00FF5B4CH   PUBLIC    CODE     ---       usb_set_feature
      00FF698FH   PUBLIC    CODE     ---       usb_get_configuration
      00FF58C9H   PUBLIC    CODE     ---       usb_set_configuration
      00FF4F7FH   PUBLIC    CODE     ---       usb_get_status
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      ---         MODULE    ---      ---       usb_req_vendor
      00FF0046H   PUBLIC    CODE     ---       usb_req_vendor
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 45


      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      ---         MODULE    ---      ---       util
      00FF7E96H   PUBLIC    CODE     ---       LCD12864_DisplayClear
      00FF7156H   PUBLIC    CODE     ---       OLED12864_ScrollRight
      00FF7BE4H   PUBLIC    CODE     ---       OLED12864_DisplayOff
      00FF71CAH   PUBLIC    CODE     ---       OLED12864_ScrollLeft
      00FF7756H   PUBLIC    CODE     ---       OLED12864_SetAddressMode
      00FF7777H   PUBLIC    CODE     ---       OLED12864_SetContrast
      00FF7C16H   PUBLIC    CODE     ---       LCD12864_AutoWrapOff
      00FF6FA7H   PUBLIC    CODE     ---       OLED12864_ShowPicture
      00FF7A93H   PUBLIC    CODE     ---       OLED12864_ScrollStart
      00FF72D9H   PUBLIC    CODE     ---       SEG7_ShowFloat
      00FF6CFBH   PUBLIC    CODE     ---       SEG7_ShowString
      00FF726FH   PUBLIC    CODE     ---       SEG7_ShowCode
      00FF6D8CH   PUBLIC    CODE     ---       LED40_SendData
      00FF7AADH   PUBLIC    CODE     ---       OLED12864_DisplayOn
      00FF7798H   PUBLIC    CODE     ---       LCD12864_ReverseLine
      00FF7C2FH   PUBLIC    CODE     ---       OLED12864_ScrollStop
      00FF6DD3H   PUBLIC    CODE     ---       LED64_SendData
      00FF748BH   PUBLIC    CODE     ---       printf_hid
      00FF00EEH   PUBLIC    CODE     ---       SEG7_ShowLong
      00FF7AC7H   PUBLIC    CODE     ---       LCD12864_ScrollRight
      00FF7AE1H   PUBLIC    CODE     ---       LCD12864_AutoWrapOn
      00FF7C48H   PUBLIC    CODE     ---       LCD12864_DisplayOff
      00FF72A4H   PUBLIC    CODE     ---       reverse2
      00FF67C7H   PUBLIC    CODE     ---       reverse4
      00FF7C7AH   PUBLIC    CODE     ---       LCD12864_ScrollLeft
      00FF7C93H   PUBLIC    CODE     ---       OLED12864_HorizontalMirror
      00FF6B7CH   PUBLIC    CODE     ---       LCD12864_ShowPicture
      00FF7190H   PUBLIC    CODE     ---       OLED12864_ScrollUp
      00FF7CACH   PUBLIC    CODE     ---       OLED12864_DisplayContent
      00FF7EAAH   PUBLIC    CODE     ---       OLED12864_DisplayReverse
      00FF7AFBH   PUBLIC    CODE     ---       OLED12864_VerticalMirror
      00FF7B15H   PUBLIC    CODE     ---       LCD12864_CursorReturnHome
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 46


      00FF7B2FH   PUBLIC    CODE     ---       OLED12864_DisplayEntire
      00FF7B49H   PUBLIC    CODE     ---       LCD12864_DisplayOn
      00FF7B63H   PUBLIC    CODE     ---       LCD12864_CursorMoveRight
      00FF70A4H   PUBLIC    CODE     ---       LCD12864_ShowString
      00FF7CC5H   PUBLIC    CODE     ---       LCD12864_CursorOff
      00FF7D59H   PUBLIC    CODE     ---       sleep_ms
      00FF7CDEH   PUBLIC    CODE     ---       LCD12864_CursorMoveLeft
      00FF797CH   PUBLIC    CODE     ---       sleep_us
      00FF7636H   PUBLIC    CODE     ---       LCD12864_ScrollUp
      00FF7B7DH   PUBLIC    CODE     ---       LCD12864_CursorOn
      00000125H   PUBLIC    EDATA    BYTE      ?SEG7_ShowString?BYTE
      00000151H   PUBLIC    EDATA    BYTE      ?printf_hid?BYTE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      ---         MODULE    ---      ---       usb_desc
      00FF619EH   PUBLIC    CODE     ---       PRODUCTDESC
      00FF61BCH   PUBLIC    CODE     ---       LANGIDDESC
      00FF61C0H   PUBLIC    CODE     ---       DEVICEDESC
      00FF61D2H   PUBLIC    CODE     ---       CONFIGDESC
      00FF6215H   PUBLIC    CODE     ---       PACKET0
      00FF6217H   PUBLIC    CODE     ---       PACKET1
      00FF6219H   PUBLIC    CODE     ---       MANUFACTDESC
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 47


      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      ---         MODULE    ---      ---       MPU6050
      00FF693AH   PUBLIC    CODE     ---       mpu6050_simiic_read_regs
      00FF75BCH   PUBLIC    CODE     ---       mpu6050_simiic_read_reg
      00FF3FF8H   PUBLIC    CODE     ---       Kalman_Filter
      00FF6F66H   PUBLIC    CODE     ---       Yijielvbo
      00FF69E3H   PUBLIC    CODE     ---       mpu6050_init
      00FF6FE7H   PUBLIC    CODE     ---       mpu6050_get_acc
      00FF654DH   PUBLIC    CODE     ---       mpu6050_iic_init
      00FF6B2CH   PUBLIC    CODE     ---       mpu6050_get_gyro
      00FF2D0CH   PUBLIC    CODE     ---       IMUupdate
      00000023H   PUBLIC    EDATA    INT       mpu6050_acc_x
      00000025H   PUBLIC    EDATA    INT       mpu6050_acc_y
      00000027H   PUBLIC    EDATA    FLOAT     t_0
      0000002BH   PUBLIC    EDATA    INT       mpu6050_acc_z
      0000002DH   PUBLIC    EDATA    FLOAT     t_1
      00000031H   PUBLIC    EDATA    FLOAT     angle
      00000035H   PUBLIC    EDATA    FLOAT     exInt
      00000039H   PUBLIC    EDATA    FLOAT     eyInt
      0000003DH   PUBLIC    EDATA    FLOAT     ezInt
      00000041H   PUBLIC    EDATA    FLOAT     Angle_err
      00000045H   PUBLIC    EDATA    FLOAT     PCt_0
      00000049H   PUBLIC    EDATA    ---       Pdot
      00000059H   PUBLIC    EDATA    FLOAT     PCt_1
      0000005DH   PUBLIC    EDATA    INT       mpu6050_gyro_x
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 48


      0000005FH   PUBLIC    EDATA    INT       mpu6050_gyro_y
      00000061H   PUBLIC    EDATA    INT       mpu6050_gyro_z
      00000063H   PUBLIC    EDATA    ---       PP
      00000073H   PUBLIC    EDATA    FLOAT     q0
      00000077H   PUBLIC    EDATA    FLOAT     q1
      0000007BH   PUBLIC    EDATA    FLOAT     q2
      0000007FH   PUBLIC    EDATA    FLOAT     q3
      00000083H   PUBLIC    EDATA    FLOAT     angle_dot
      00000087H   PUBLIC    EDATA    FLOAT     d_t
      0000008BH   PUBLIC    EDATA    FLOAT     Q_angle
      0000008FH   PUBLIC    EDATA    FLOAT     R_angle
      00000093H   PUBLIC    EDATA    FLOAT     AngleX
      00000097H   PUBLIC    EDATA    FLOAT     AngleY
      0000009BH   PUBLIC    EDATA    FLOAT     E
      0000009FH   PUBLIC    EDATA    FLOAT     Q_bias
      000000A3H   PUBLIC    EDATA    CHAR      C_0
      000000A4H   PUBLIC    EDATA    FLOAT     K_0
      000000A8H   PUBLIC    EDATA    FLOAT     K_1
      000000ACH   PUBLIC    EDATA    INT       mpu_temp
      000000AEH   PUBLIC    EDATA    FLOAT     Q_gyro
      000000B2H   PUBLIC    EDATA    FLOAT     K1
      00000004H   PUBLIC    EDATA    BYTE      ?IMUupdate?BYTE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 49


      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0
      00FF7F78H   SYMBOL    CODE     ---       mpu6050_simiic_start
      00FF7F08H   SYMBOL    CODE     ---       mpu6050_simiic_stop
      00FF76F1H   SYMBOL    CODE     ---       mpu6050_simiic_write_reg
      00FF77B9H   SYMBOL    CODE     ---       mpu6050_simiic_sendack
      00FF70E1H   SYMBOL    CODE     ---       mpu6050_read_ch
      00FF7430H   SYMBOL    CODE     ---       mpu6050_send_ch
      00FF79EDH   SYMBOL    CODE     ---       mpu6050_sccb_waitack
      00FF7FD9H   SYMBOL    CODE     ---       mpu6050_simiic_delay

      00FF7FD9H   BLOCK     CODE     ---       LVL=0
      WR6         REGSYM    ---      WORD      j
      00FF7FD9H   LINE      CODE     ---       #21
      00FF7FD9H   LINE      CODE     ---       #22
      00FF7FD9H   LINE      CODE     ---       #23
      00FF7FDBH   LINE      CODE     ---       #24
      00FF7FE3H   LINE      CODE     ---       #25
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7F78H   BLOCK     CODE     ---       LVL=0
      00FF7F78H   LINE      CODE     ---       #28
      00FF7F78H   LINE      CODE     ---       #30
      00FF7F7AH   LINE      CODE     ---       #31
      00FF7F7CH   LINE      CODE     ---       #32
      00FF7F7FH   LINE      CODE     ---       #33
      00FF7F81H   LINE      CODE     ---       #34
      00FF7F84H   LINE      CODE     ---       #35
      00FF7F86H   LINE      CODE     ---       #36
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7F08H   BLOCK     CODE     ---       LVL=0
      00FF7F08H   LINE      CODE     ---       #39
      00FF7F08H   LINE      CODE     ---       #41
      00FF7F0AH   LINE      CODE     ---       #42
      00FF7F0CH   LINE      CODE     ---       #43
      00FF7F0FH   LINE      CODE     ---       #44
      00FF7F11H   LINE      CODE     ---       #45
      00FF7F14H   LINE      CODE     ---       #46
      00FF7F16H   LINE      CODE     ---       #47
      ---         BLOCKEND  ---      ---       LVL=0

      00FF77B9H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      ack_dat
      00FF77B9H   LINE      CODE     ---       #52
      00FF77BDH   LINE      CODE     ---       #54
      00FF77BFH   LINE      CODE     ---       #55
      00FF77C2H   LINE      CODE     ---       #56
      00FF77CAH   LINE      CODE     ---       #57
      00FF77CCH   LINE      CODE     ---       #59
      00FF77CEH   LINE      CODE     ---       #60
      00FF77D1H   LINE      CODE     ---       #61
      00FF77D3H   LINE      CODE     ---       #62
      00FF77D6H   LINE      CODE     ---       #63
      ---         BLOCKEND  ---      ---       LVL=0

      00FF79EDH   BLOCK     CODE     ---       LVL=0
      00FF79EDH   LINE      CODE     ---       #66
      00FF79EDH   LINE      CODE     ---       #68
      00FF79EFH   LINE      CODE     ---       #70
      00FF79F2H   LINE      CODE     ---       #72
      00FF79F4H   LINE      CODE     ---       #73
      00FF79F7H   LINE      CODE     ---       #75
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 50


      00FF79FAH   LINE      CODE     ---       #78
      00FF79FCH   LINE      CODE     ---       #79
      00FF79FFH   LINE      CODE     ---       #80
      00FF79FFH   LINE      CODE     ---       #82
      00FF7A01H   LINE      CODE     ---       #83
      00FF7A04H   LINE      CODE     ---       #84
      00FF7A08H   LINE      CODE     ---       #85
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7430H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      c
      00FF7434H   BLOCK     CODE     NEAR LAB  LVL=1
      R14         REGSYM    ---      BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7430H   LINE      CODE     ---       #91
      00FF7434H   LINE      CODE     ---       #92
      00FF7434H   LINE      CODE     ---       #93
      00FF7437H   LINE      CODE     ---       #94
      00FF7439H   LINE      CODE     ---       #96
      00FF7442H   LINE      CODE     ---       #97
      00FF7444H   LINE      CODE     ---       #98
      00FF7446H   LINE      CODE     ---       #99
      00FF7449H   LINE      CODE     ---       #100
      00FF744BH   LINE      CODE     ---       #101
      00FF744EH   LINE      CODE     ---       #102
      00FF7450H   LINE      CODE     ---       #103
      00FF7458H   LINE      CODE     ---       #104
      00FF745BH   LINE      CODE     ---       #105
      ---         BLOCKEND  ---      ---       LVL=0

      00FF70E1H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      ack_x
      00FF70E7H   BLOCK     CODE     NEAR LAB  LVL=1
      R13         REGSYM    ---      BYTE      i
      R14         REGSYM    ---      BYTE      c
      ---         BLOCKEND  ---      ---       LVL=1
      00FF70E1H   LINE      CODE     ---       #111
      00FF70E7H   LINE      CODE     ---       #112
      00FF70E7H   LINE      CODE     ---       #115
      00FF70E9H   LINE      CODE     ---       #116
      00FF70EBH   LINE      CODE     ---       #117
      00FF70EEH   LINE      CODE     ---       #118
      00FF70F0H   LINE      CODE     ---       #120
      00FF70F3H   LINE      CODE     ---       #122
      00FF70F6H   LINE      CODE     ---       #123
      00FF70F8H   LINE      CODE     ---       #124
      00FF70FBH   LINE      CODE     ---       #125
      00FF70FDH   LINE      CODE     ---       #126
      00FF7100H   LINE      CODE     ---       #127
      00FF7102H   LINE      CODE     ---       #128
      00FF7105H   LINE      CODE     ---       #130
      00FF7107H   LINE      CODE     ---       #131
      00FF710BH   LINE      CODE     ---       #134
      00FF710DH   LINE      CODE     ---       #135
      00FF7110H   LINE      CODE     ---       #136
      00FF7115H   LINE      CODE     ---       #138
      00FF7117H   LINE      CODE     ---       #139
      ---         BLOCKEND  ---      ---       LVL=0

      00FF76F1H   BLOCK     CODE     ---       LVL=0
      R13         REGSYM    ---      BYTE      dev_add
      R14         REGSYM    ---      BYTE      reg
      R15         REGSYM    ---      BYTE      dat
      00FF76F1H   LINE      CODE     ---       #151
      00FF76F9H   LINE      CODE     ---       #153
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 51


      00FF76FCH   LINE      CODE     ---       #154
      00FF7701H   LINE      CODE     ---       #155
      00FF7706H   LINE      CODE     ---       #156
      00FF770BH   LINE      CODE     ---       #157
      00FF770EH   LINE      CODE     ---       #158
      ---         BLOCKEND  ---      ---       LVL=0

      00FF75BCH   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      dev_add
      R14         REGSYM    ---      BYTE      reg
      00FF75C2H   BLOCK     CODE     NEAR LAB  LVL=1
      R15         REGSYM    ---      BYTE      dat
      ---         BLOCKEND  ---      ---       LVL=1
      00FF75BCH   LINE      CODE     ---       #169
      00FF75C2H   LINE      CODE     ---       #170
      00FF75C2H   LINE      CODE     ---       #172
      00FF75C5H   LINE      CODE     ---       #173
      00FF75CAH   LINE      CODE     ---       #174
      00FF75CFH   LINE      CODE     ---       #177
      00FF75D2H   LINE      CODE     ---       #178
      00FF75DBH   LINE      CODE     ---       #179
      00FF75DFH   LINE      CODE     ---       #180
      00FF75E2H   LINE      CODE     ---       #182
      00FF75E2H   LINE      CODE     ---       #183
      ---         BLOCKEND  ---      ---       LVL=0

      00FF693AH   BLOCK     CODE     ---       LVL=0
      000002FDH   SYMBOL    EDATA    BYTE      dev_add
      000002FEH   SYMBOL    EDATA    BYTE      reg
      REG=3       REGSYM    ---      ---       dat_add
      000002FFH   SYMBOL    EDATA    BYTE      num
      00FF693AH   LINE      CODE     ---       #196
      00FF694AH   LINE      CODE     ---       #198
      00FF694DH   LINE      CODE     ---       #199
      00FF6956H   LINE      CODE     ---       #200
      00FF695DH   LINE      CODE     ---       #203
      00FF6960H   LINE      CODE     ---       #204
      00FF696BH   LINE      CODE     ---       #205
      00FF696DH   LINE      CODE     ---       #207
      00FF6975H   LINE      CODE     ---       #208
      00FF6977H   LINE      CODE     ---       #209
      00FF6982H   LINE      CODE     ---       #210
      00FF6989H   LINE      CODE     ---       #211
      00FF698CH   LINE      CODE     ---       #212
      ---         BLOCKEND  ---      ---       LVL=0

      00FF69E3H   BLOCK     CODE     ---       LVL=0
      00FF69E3H   LINE      CODE     ---       #220
      00FF69E3H   LINE      CODE     ---       #222
      00FF69EAH   LINE      CODE     ---       #224
      00FF69F4H   LINE      CODE     ---       #225
      00FF69FFH   LINE      CODE     ---       #226
      00FF6A0AH   LINE      CODE     ---       #227
      00FF6A15H   LINE      CODE     ---       #228
      00FF6A20H   LINE      CODE     ---       #229
      00FF6A2AH   LINE      CODE     ---       #230
      00FF6A35H   LINE      CODE     ---       #231
      00FF6A36H   LINE      CODE     ---       #232
      ---         BLOCKEND  ---      ---       LVL=0

      00FF654DH   BLOCK     CODE     ---       LVL=0
      00FF654DH   LINE      CODE     ---       #234
      00FF654DH   LINE      CODE     ---       #236
      00FF6554H   LINE      CODE     ---       #237
      00FF6561H   LINE      CODE     ---       #238
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 52


      00FF6570H   LINE      CODE     ---       #239
      00FF657FH   LINE      CODE     ---       #240
      00FF658EH   LINE      CODE     ---       #241
      00FF659DH   LINE      CODE     ---       #242
      00FF65AAH   LINE      CODE     ---       #243
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6FE7H   BLOCK     CODE     ---       LVL=0
      000002BAH   SYMBOL    EDATA    ---       dat
      00FF6FE7H   LINE      CODE     ---       #254
      00FF6FE7H   LINE      CODE     ---       #255
      00FF6FE7H   LINE      CODE     ---       #258
      00FF6FF6H   LINE      CODE     ---       #259
      00FF7006H   LINE      CODE     ---       #260
      00FF7016H   LINE      CODE     ---       #261
      00FF7026H   LINE      CODE     ---       #262
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6B2CH   BLOCK     CODE     ---       LVL=0
      00000293H   SYMBOL    EDATA    ---       dat
      00FF6B2CH   LINE      CODE     ---       #270
      00FF6B2CH   LINE      CODE     ---       #271
      00FF6B2CH   LINE      CODE     ---       #274
      00FF6B3BH   LINE      CODE     ---       #275
      00FF6B4BH   LINE      CODE     ---       #276
      00FF6B5BH   LINE      CODE     ---       #277
      00FF6B6BH   LINE      CODE     ---       #278
      00FF6B7BH   LINE      CODE     ---       #279
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3FF8H   BLOCK     CODE     ---       LVL=0
      DR24        REGSYM    ---      FLOAT     Accel
      DR28        REGSYM    ---      FLOAT     Gyro
      00FF3FF8H   LINE      CODE     ---       #304
      00FF3FFEH   LINE      CODE     ---       #306
      00FF401BH   LINE      CODE     ---       #307
      00FF4035H   LINE      CODE     ---       #309
      00FF4040H   LINE      CODE     ---       #310
      00FF4044H   LINE      CODE     ---       #311
      00FF404CH   LINE      CODE     ---       #312
      00FF4060H   LINE      CODE     ---       #313
      00FF4072H   LINE      CODE     ---       #314
      00FF4084H   LINE      CODE     ---       #315
      00FF4098H   LINE      CODE     ---       #317
      00FF40A5H   LINE      CODE     ---       #319
      00FF40BBH   LINE      CODE     ---       #320
      00FF40CAH   LINE      CODE     ---       #322
      00FF40E0H   LINE      CODE     ---       #324
      00FF40EFH   LINE      CODE     ---       #325
      00FF40FCH   LINE      CODE     ---       #327
      00FF4100H   LINE      CODE     ---       #328
      00FF410FH   LINE      CODE     ---       #330
      00FF412BH   LINE      CODE     ---       #331
      00FF413FH   LINE      CODE     ---       #332
      00FF4155H   LINE      CODE     ---       #333
      00FF416BH   LINE      CODE     ---       #335
      00FF4183H   LINE      CODE     ---       #336
      00FF4195H   LINE      CODE     ---       #337
      00FF41A2H   LINE      CODE     ---       #338
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6F66H   BLOCK     CODE     ---       LVL=0
      DR24        REGSYM    ---      FLOAT     angle_m
      DR28        REGSYM    ---      FLOAT     gyro_m
      00FF6F66H   LINE      CODE     ---       #345
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 53


      00FF6F6AH   LINE      CODE     ---       #347
      00FF6FA6H   LINE      CODE     ---       #348
      ---         BLOCKEND  ---      ---       LVL=0

      00FF2D0CH   BLOCK     CODE     ---       LVL=0
      00000008H   SYMBOL    EDATA    FLOAT     gx
      DR12        REGSYM    ---      FLOAT     gy
      0000000CH   SYMBOL    EDATA    FLOAT     gz
      00000010H   SYMBOL    EDATA    FLOAT     ax
      00000014H   SYMBOL    EDATA    FLOAT     ay
      00000018H   SYMBOL    EDATA    FLOAT     az
      00FF2D14H   BLOCK     CODE     NEAR LAB  LVL=1
      DR28        REGSYM    ---      FLOAT     norm
      0000001CH   SYMBOL    EDATA    FLOAT     vx
      DR24        REGSYM    ---      FLOAT     vy
      DR28        REGSYM    ---      FLOAT     vz
      DR16        REGSYM    ---      FLOAT     ex
      DR20        REGSYM    ---      FLOAT     ey
      DR24        REGSYM    ---      FLOAT     ez
      ---         BLOCKEND  ---      ---       LVL=1
      00FF2D0CH   LINE      CODE     ---       #366
      00FF2D14H   LINE      CODE     ---       #367
      00FF2D14H   LINE      CODE     ---       #372
      00FF2D42H   LINE      CODE     ---       #373
      00FF2D4FH   LINE      CODE     ---       #374
      00FF2D5CH   LINE      CODE     ---       #375
      00FF2D69H   LINE      CODE     ---       #381
      00FF2D99H   LINE      CODE     ---       #382
      00FF2DBBH   LINE      CODE     ---       #383
      00FF2DF8H   LINE      CODE     ---       #385
      00FF2E13H   LINE      CODE     ---       #386
      00FF2E30H   LINE      CODE     ---       #387
      00FF2E4DH   LINE      CODE     ---       #389
      00FF2E67H   LINE      CODE     ---       #390
      00FF2E79H   LINE      CODE     ---       #391
      00FF2E8BH   LINE      CODE     ---       #393
      00FF2EACH   LINE      CODE     ---       #394
      00FF2EC1H   LINE      CODE     ---       #395
      00FF2EDAH   LINE      CODE     ---       #397
      00FF2F2AH   LINE      CODE     ---       #398
      00FF2F65H   LINE      CODE     ---       #399
      00FF2F9AH   LINE      CODE     ---       #400
      00FF2FD7H   LINE      CODE     ---       #402
      00FF3017H   LINE      CODE     ---       #403
      00FF3024H   LINE      CODE     ---       #404
      00FF3031H   LINE      CODE     ---       #405
      00FF303EH   LINE      CODE     ---       #406
      00FF304BH   LINE      CODE     ---       #408
      00FF3083H   LINE      CODE     ---       #409
      00FF30BBH   LINE      CODE     ---       #410
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       OLED_SPI
      00FF7E57H   PUBLIC    CODE     ---       OLED_Display_On
      00FF74B8H   PUBLIC    CODE     ---       OLED_DisplayTurn
      00FF5428H   PUBLIC    CODE     ---       OLED_Init
      00FF4E7BH   PUBLIC    CODE     ---       OLED_ShowFloat
      00FF631FH   PUBLIC    CODE     ---       OLED_ShowString
      00FF7B97H   PUBLIC    CODE     ---       OLED_WR_Byte
      00FF568CH   PUBLIC    CODE     ---       OLED_ShowChar
      00FF78E9H   PUBLIC    CODE     ---       OLED_ColorTurn
      00FF75E5H   PUBLIC    CODE     ---       OLED_Set_Pos
      00FF5D15H   PUBLIC    CODE     ---       OLED_ShowInt
      00FF5184H   PUBLIC    CODE     ---       OLED_ShowNum
      00FF7E6CH   PUBLIC    CODE     ---       OLED_Display_Off
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 54


      00FF7BB1H   PUBLIC    CODE     ---       oled_pow
      00FF62A3H   PUBLIC    CODE     ---       OLED_ShowChinese
      00FF7239H   PUBLIC    CODE     ---       OLED_Clear
      00FF5DADH   PUBLIC    CODE     ---       OLED_DrawBMP
      00FF0E5BH   PUBLIC    CODE     ---       asc2_0806
      00FF1083H   PUBLIC    CODE     ---       asc2_1608
      00FF1673H   PUBLIC    CODE     ---       Hzk
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000A0H.6 SFRSYM    DATA     BIT       OLED_RES
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000A0H.2 SFRSYM    DATA     BIT       OLED_CS
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A0H.4 SFRSYM    DATA     BIT       OLED_DC
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF78E9H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      i
      00FF78E9H   LINE      CODE     ---       #222
      00FF78EDH   LINE      CODE     ---       #224
      00FF78F1H   LINE      CODE     ---       #226
      00FF78F8H   LINE      CODE     ---       #227
      00FF78F8H   LINE      CODE     ---       #228
      00FF78FDH   LINE      CODE     ---       #230
      00FF7904H   LINE      CODE     ---       #231
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 55


      ---         BLOCKEND  ---      ---       LVL=0

      00FF74B8H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      i
      00FF74B8H   LINE      CODE     ---       #235
      00FF74BCH   LINE      CODE     ---       #237
      00FF74C0H   LINE      CODE     ---       #239
      00FF74C7H   LINE      CODE     ---       #240
      00FF74CEH   LINE      CODE     ---       #241
      00FF74CEH   LINE      CODE     ---       #242
      00FF74D3H   LINE      CODE     ---       #244
      00FF74DAH   LINE      CODE     ---       #245
      00FF74E1H   LINE      CODE     ---       #246
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7B97H   BLOCK     CODE     ---       LVL=0
      R7          REGSYM    ---      BYTE      dat
      R10         REGSYM    ---      BYTE      cmd
      00FF7B97H   LINE      CODE     ---       #276
      00FF7B9BH   LINE      CODE     ---       #278
      00FF7B9FH   LINE      CODE     ---       #279
      00FF7BA3H   LINE      CODE     ---       #281
      00FF7BA5H   LINE      CODE     ---       #282
      00FF7BA7H   LINE      CODE     ---       #283
      00FF7BACH   LINE      CODE     ---       #284
      00FF7BAEH   LINE      CODE     ---       #285
      00FF7BB0H   LINE      CODE     ---       #286
      ---         BLOCKEND  ---      ---       LVL=0

      00FF75E5H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      x
      R10         REGSYM    ---      BYTE      y
      00FF75E5H   LINE      CODE     ---       #290
      00FF75E9H   LINE      CODE     ---       #292
      00FF75F4H   LINE      CODE     ---       #293
      00FF7602H   LINE      CODE     ---       #294
      00FF760BH   LINE      CODE     ---       #295
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7E57H   BLOCK     CODE     ---       LVL=0
      00FF7E57H   LINE      CODE     ---       #297
      00FF7E57H   LINE      CODE     ---       #299
      00FF7E5EH   LINE      CODE     ---       #300
      00FF7E65H   LINE      CODE     ---       #301
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7E6CH   BLOCK     CODE     ---       LVL=0
      00FF7E6CH   LINE      CODE     ---       #304
      00FF7E6CH   LINE      CODE     ---       #306
      00FF7E73H   LINE      CODE     ---       #307
      00FF7E7AH   LINE      CODE     ---       #308
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7239H   BLOCK     CODE     ---       LVL=0
      00FF723BH   BLOCK     CODE     NEAR LAB  LVL=1
      R15         REGSYM    ---      BYTE      i
      R14         REGSYM    ---      BYTE      n
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7239H   LINE      CODE     ---       #311
      00FF723BH   LINE      CODE     ---       #312
      00FF723BH   LINE      CODE     ---       #314
      00FF723DH   LINE      CODE     ---       #316
      00FF7248H   LINE      CODE     ---       #317
      00FF724EH   LINE      CODE     ---       #318
      00FF7255H   LINE      CODE     ---       #319
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 56


      00FF7265H   LINE      CODE     ---       #320
      00FF726CH   LINE      CODE     ---       #321
      ---         BLOCKEND  ---      ---       LVL=0

      00FF568CH   BLOCK     CODE     ---       LVL=0
      R13         REGSYM    ---      BYTE      x
      R14         REGSYM    ---      BYTE      y
      R10         REGSYM    ---      BYTE      chr
      R15         REGSYM    ---      BYTE      sizey
      00FF5696H   BLOCK     CODE     NEAR LAB  LVL=1
      R12         REGSYM    ---      BYTE      c
      000002CFH   SYMBOL    EDATA    BYTE      sizex
      000002D0H   SYMBOL    EDATA    WORD      i
      000002D2H   SYMBOL    EDATA    WORD      size1
      ---         BLOCKEND  ---      ---       LVL=1
      00FF568CH   LINE      CODE     ---       #327
      00FF5696H   LINE      CODE     ---       #328
      00FF5696H   LINE      CODE     ---       #329
      00FF56A0H   LINE      CODE     ---       #330
      00FF56A6H   LINE      CODE     ---       #331
      00FF56B5H   LINE      CODE     ---       #332
      00FF56D3H   LINE      CODE     ---       #333
      00FF56DBH   LINE      CODE     ---       #334
      00FF56E2H   LINE      CODE     ---       #335
      00FF56E6H   LINE      CODE     ---       #337
      00FF5704H   LINE      CODE     ---       #338
      00FF5718H   LINE      CODE     ---       #339
      00FF5738H   LINE      CODE     ---       #341
      00FF5738H   LINE      CODE     ---       #342
      00FF574CH   LINE      CODE     ---       #343
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7BB1H   BLOCK     CODE     ---       LVL=0
      R8          REGSYM    ---      BYTE      m
      R9          REGSYM    ---      BYTE      n
      00FF7BB5H   BLOCK     CODE     NEAR LAB  LVL=1
      DR4         REGSYM    ---      DWORD     result
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7BB1H   LINE      CODE     ---       #345
      00FF7BB5H   LINE      CODE     ---       #346
      00FF7BB5H   LINE      CODE     ---       #347
      00FF7BB9H   LINE      CODE     ---       #348
      00FF7BCAH   LINE      CODE     ---       #349
      00FF7BCAH   LINE      CODE     ---       #350
      ---         BLOCKEND  ---      ---       LVL=0

      00FF5184H   BLOCK     CODE     ---       LVL=0
      0000029BH   SYMBOL    EDATA    BYTE      x
      0000029CH   SYMBOL    EDATA    BYTE      y
      DR12        REGSYM    ---      DWORD     num
      0000029DH   SYMBOL    EDATA    BYTE      len
      0000029EH   SYMBOL    EDATA    BYTE      sizey
      00FF5198H   BLOCK     CODE     NEAR LAB  LVL=1
      0000029FH   SYMBOL    EDATA    BYTE      t
      000002A0H   SYMBOL    EDATA    BYTE      temp
      000002A1H   SYMBOL    EDATA    BYTE      m
      000002A2H   SYMBOL    EDATA    BYTE      enshow
      ---         BLOCKEND  ---      ---       LVL=1
      00FF5184H   LINE      CODE     ---       #356
      00FF5198H   LINE      CODE     ---       #357
      00FF5198H   LINE      CODE     ---       #358
      00FF519DH   LINE      CODE     ---       #359
      00FF51A1H   LINE      CODE     ---       #360
      00FF51AEH   LINE      CODE     ---       #361
      00FF51B2H   LINE      CODE     ---       #363
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 57


      00FF51D9H   LINE      CODE     ---       #364
      00FF51F1H   LINE      CODE     ---       #366
      00FF51F7H   LINE      CODE     ---       #368
      00FF5218H   LINE      CODE     ---       #369
      00FF521AH   LINE      CODE     ---       #370
      00FF5220H   LINE      CODE     ---       #371
      00FF5220H   LINE      CODE     ---       #372
      00FF5253H   LINE      CODE     ---       #373
      00FF5269H   LINE      CODE     ---       #374
      ---         BLOCKEND  ---      ---       LVL=0

      00FF631FH   BLOCK     CODE     ---       LVL=0
      000002E5H   SYMBOL    EDATA    BYTE      x
      000002E6H   SYMBOL    EDATA    BYTE      y
      REG=3       REGSYM    ---      ---       chr
      000002E7H   SYMBOL    EDATA    BYTE      sizey
      00FF632FH   BLOCK     CODE     NEAR LAB  LVL=1
      000002E8H   SYMBOL    EDATA    BYTE      j
      ---         BLOCKEND  ---      ---       LVL=1
      00FF631FH   LINE      CODE     ---       #376
      00FF632FH   LINE      CODE     ---       #377
      00FF632FH   LINE      CODE     ---       #378
      00FF6334H   LINE      CODE     ---       #379
      00FF6336H   LINE      CODE     ---       #381
      00FF635AH   LINE      CODE     ---       #382
      00FF636FH   LINE      CODE     ---       #383
      00FF6380H   LINE      CODE     ---       #384
      00FF638FH   LINE      CODE     ---       #385
      ---         BLOCKEND  ---      ---       LVL=0

      00FF62A3H   BLOCK     CODE     ---       LVL=0
      R12         REGSYM    ---      BYTE      x
      R14         REGSYM    ---      BYTE      y
      R13         REGSYM    ---      BYTE      no
      R15         REGSYM    ---      BYTE      sizey
      00FF62ADH   BLOCK     CODE     NEAR LAB  LVL=1
      000002E9H   SYMBOL    EDATA    WORD      i
      000002EBH   SYMBOL    EDATA    WORD      size1
      ---         BLOCKEND  ---      ---       LVL=1
      00FF62A3H   LINE      CODE     ---       #387
      00FF62ADH   LINE      CODE     ---       #388
      00FF62ADH   LINE      CODE     ---       #389
      00FF62CFH   LINE      CODE     ---       #390
      00FF62D3H   LINE      CODE     ---       #392
      00FF62E8H   LINE      CODE     ---       #393
      00FF6308H   LINE      CODE     ---       #395
      00FF6308H   LINE      CODE     ---       #396
      00FF631CH   LINE      CODE     ---       #397
      ---         BLOCKEND  ---      ---       LVL=0

      00FF5DADH   BLOCK     CODE     ---       LVL=0
      000002A3H   SYMBOL    EDATA    BYTE      x
      000002A4H   SYMBOL    EDATA    BYTE      y
      000002A5H   SYMBOL    EDATA    BYTE      sizex
      000002A6H   SYMBOL    EDATA    BYTE      sizey
      REG=3       REGSYM    ---      ---       BMP
      00FF5DC1H   BLOCK     CODE     NEAR LAB  LVL=1
      000002A7H   SYMBOL    EDATA    WORD      j
      000002A9H   SYMBOL    EDATA    BYTE      i
      000002AAH   SYMBOL    EDATA    BYTE      m
      ---         BLOCKEND  ---      ---       LVL=1
      00FF5DADH   LINE      CODE     ---       #404
      00FF5DC1H   LINE      CODE     ---       #405
      00FF5DC1H   LINE      CODE     ---       #406
      00FF5DC7H   LINE      CODE     ---       #408
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 58


      00FF5DE7H   LINE      CODE     ---       #409
      00FF5DEAH   LINE      CODE     ---       #411
      00FF5DFFH   LINE      CODE     ---       #412
      00FF5E02H   LINE      CODE     ---       #414
      00FF5E1BH   LINE      CODE     ---       #415
      00FF5E2EH   LINE      CODE     ---       #416
      00FF5E41H   LINE      CODE     ---       #417
      ---         BLOCKEND  ---      ---       LVL=0

      00FF4E7BH   BLOCK     CODE     ---       LVL=0
      00000155H   SYMBOL    EDATA    BYTE      x
      00000156H   SYMBOL    EDATA    BYTE      y
      DR12        REGSYM    ---      FLOAT     dat
      00000157H   SYMBOL    EDATA    BYTE      num
      00000158H   SYMBOL    EDATA    BYTE      pointnum
      00000159H   SYMBOL    EDATA    BYTE      size2
      00FF4E93H   BLOCK     CODE     NEAR LAB  LVL=1
      0000015AH   SYMBOL    EDATA    BYTE      length
      0000015BH   SYMBOL    EDATA    ---       buff
      0000017DH   SYMBOL    EDATA    CHAR      start
      0000017EH   SYMBOL    EDATA    CHAR      end
      R7          REGSYM    ---      CHAR      point
      ---         BLOCKEND  ---      ---       LVL=1
      00FF4E7BH   LINE      CODE     ---       #429
      00FF4E93H   LINE      CODE     ---       #430
      00FF4E93H   LINE      CODE     ---       #435
      00FF4EA2H   LINE      CODE     ---       #436
      00FF4EB1H   LINE      CODE     ---       #438
      00FF4EBAH   LINE      CODE     ---       #439
      00FF4ED1H   LINE      CODE     ---       #440
      00FF4ED3H   LINE      CODE     ---       #441
      00FF4EECH   LINE      CODE     ---       #442
      00FF4EF1H   LINE      CODE     ---       #443
      00FF4EF1H   LINE      CODE     ---       #445
      00FF4EFBH   LINE      CODE     ---       #446
      00FF4F0BH   LINE      CODE     ---       #447
      00FF4F1BH   LINE      CODE     ---       #449
      00FF4F20H   LINE      CODE     ---       #450
      00FF4F2AH   LINE      CODE     ---       #451
      00FF4F33H   LINE      CODE     ---       #452
      00FF4F3CH   LINE      CODE     ---       #453
      00FF4F45H   LINE      CODE     ---       #455
      00FF4F4EH   LINE      CODE     ---       #456
      00FF4F52H   LINE      CODE     ---       #458
      00FF4F5EH   LINE      CODE     ---       #460
      00FF4F69H   LINE      CODE     ---       #462
      00FF4F7CH   LINE      CODE     ---       #463
      ---         BLOCKEND  ---      ---       LVL=0

      00FF5D15H   BLOCK     CODE     ---       LVL=0
      R12         REGSYM    ---      BYTE      x
      R13         REGSYM    ---      BYTE      y
      000001A7H   SYMBOL    EDATA    LONG      dat
      R15         REGSYM    ---      BYTE      num
      R14         REGSYM    ---      BYTE      size2
      00FF5D23H   BLOCK     CODE     NEAR LAB  LVL=1
      000001ABH   SYMBOL    EDATA    ---       buff
      000001CDH   SYMBOL    EDATA    BYTE      length
      ---         BLOCKEND  ---      ---       LVL=1
      00FF5D15H   LINE      CODE     ---       #475
      00FF5D23H   LINE      CODE     ---       #476
      00FF5D23H   LINE      CODE     ---       #480
      00FF5D2BH   LINE      CODE     ---       #481
      00FF5D2DH   LINE      CODE     ---       #483
      00FF5D37H   LINE      CODE     ---       #484
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 59


      00FF5D4EH   LINE      CODE     ---       #485
      00FF5D50H   LINE      CODE     ---       #486
      00FF5D56H   LINE      CODE     ---       #487
      00FF5D73H   LINE      CODE     ---       #488
      00FF5D78H   LINE      CODE     ---       #489
      00FF5D78H   LINE      CODE     ---       #491
      00FF5D7DH   LINE      CODE     ---       #492
      00FF5D87H   LINE      CODE     ---       #493
      00FF5D90H   LINE      CODE     ---       #494
      00FF5D96H   LINE      CODE     ---       #496
      00FF5D9DH   LINE      CODE     ---       #498
      00FF5DAAH   LINE      CODE     ---       #499
      ---         BLOCKEND  ---      ---       LVL=0

      00FF5428H   BLOCK     CODE     ---       LVL=0
      00FF5428H   LINE      CODE     ---       #502
      00FF5428H   LINE      CODE     ---       #505
      00FF542FH   LINE      CODE     ---       #507
      00FF5431H   LINE      CODE     ---       #508
      00FF5438H   LINE      CODE     ---       #509
      00FF543AH   LINE      CODE     ---       #510
      00FF5441H   LINE      CODE     ---       #511
      00FF5447H   LINE      CODE     ---       #512
      00FF544EH   LINE      CODE     ---       #513
      00FF5455H   LINE      CODE     ---       #515
      00FF545CH   LINE      CODE     ---       #516
      00FF5463H   LINE      CODE     ---       #518
      00FF546AH   LINE      CODE     ---       #519
      00FF5471H   LINE      CODE     ---       #521
      00FF5478H   LINE      CODE     ---       #523
      00FF547FH   LINE      CODE     ---       #524
      00FF5486H   LINE      CODE     ---       #526
      00FF548DH   LINE      CODE     ---       #527
      00FF5493H   LINE      CODE     ---       #529
      00FF549AH   LINE      CODE     ---       #530
      00FF54A1H   LINE      CODE     ---       #532
      00FF54A8H   LINE      CODE     ---       #533
      00FF54AFH   LINE      CODE     ---       #535
      00FF54B6H   LINE      CODE     ---       #536
      00FF54BDH   LINE      CODE     ---       #538
      00FF54C4H   LINE      CODE     ---       #539
      00FF54CBH   LINE      CODE     ---       #541
      00FF54D2H   LINE      CODE     ---       #542
      00FF54D9H   LINE      CODE     ---       #544
      00FF54E0H   LINE      CODE     ---       #545
      00FF54E7H   LINE      CODE     ---       #547
      00FF54EEH   LINE      CODE     ---       #548
      00FF54F5H   LINE      CODE     ---       #550
      00FF54F8H   LINE      CODE     ---       #551
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       MKS
      00FF526CH   PUBLIC    CODE     ---       CAN_MKS_CONTROL
      00FF5E44H   PUBLIC    CODE     ---       CAN_MKS_SPD_CONTROL
      00FF54FFH   PUBLIC    CODE     ---       MKS_HOME
      00FF5350H   PUBLIC    CODE     ---       MKS_PID_SET
      00FF005FH   PUBLIC    CODE     ---       MKS_ALL_RUN
      00000211H   PUBLIC    EDATA    WORD      MOTOR_state
      00000213H   PUBLIC    EDATA    ---       long_uchar1
      00000217H   PUBLIC    EDATA    WORD      MKS_KD
      00000219H   PUBLIC    EDATA    WORD      MKS_KI
      0000021BH   PUBLIC    EDATA    WORD      MKS_KP
      0000021DH   PUBLIC    EDATA    WORD      MKS_KV
      0000021FH   PUBLIC    EDATA    ---       MKS_TX
      00000021H.3 PUBLIC    BIT      BIT       ?CAN_MKS_CONTROL?BIT
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 60


      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF526CH   BLOCK     CODE     ---       LVL=0
      WR2         REGSYM    ---      WORD      Motor
      DR28        REGSYM    ---      LONG      Pos
      WR8         REGSYM    ---      WORD      Spd
      R10         REGSYM    ---      BYTE      Acc
      00000021H.3 SYMBOL    BIT      BIT       Mode
      00FF5276H   BLOCK     CODE     NEAR LAB  LVL=1
      R15         REGSYM    ---      BYTE      crc
      ---         BLOCKEND  ---      ---       LVL=1
      00FF526CH   LINE      CODE     ---       #22
      00FF5276H   LINE      CODE     ---       #23
      00FF5276H   LINE      CODE     ---       #25
      00FF5276H   LINE      CODE     ---       #27
      00FF527AH   LINE      CODE     ---       #29
      00FF527DH   LINE      CODE     ---       #31
      00FF5283H   LINE      CODE     ---       #32
      00FF5289H   LINE      CODE     ---       #33
      00FF528FH   LINE      CODE     ---       #34
      00FF5293H   LINE      CODE     ---       #36
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 61


      00FF52A4H   LINE      CODE     ---       #37
      00FF52A4H   LINE      CODE     ---       #38
      00FF52A4H   LINE      CODE     ---       #39
      00FF52A4H   LINE      CODE     ---       #41
      00FF52A4H   LINE      CODE     ---       #42
      00FF52A4H   LINE      CODE     ---       #43
      00FF52A6H   LINE      CODE     ---       #46
      00FF52ACH   LINE      CODE     ---       #48
      00FF52B8H   LINE      CODE     ---       #49
      00FF52BEH   LINE      CODE     ---       #50
      00FF52C2H   LINE      CODE     ---       #52
      00FF52D3H   LINE      CODE     ---       #53
      00FF52DBH   LINE      CODE     ---       #54
      00FF52E3H   LINE      CODE     ---       #55
      00FF52EBH   LINE      CODE     ---       #57
      00FF531FH   LINE      CODE     ---       #58
      00FF5323H   LINE      CODE     ---       #59
      00FF5323H   LINE      CODE     ---       #61
      00FF5338H   LINE      CODE     ---       #62
      00FF534DH   LINE      CODE     ---       #64
      ---         BLOCKEND  ---      ---       LVL=0

      00FF5E44H   BLOCK     CODE     ---       LVL=0
      WR14        REGSYM    ---      WORD      Motor
      WR2         REGSYM    ---      INT       Spd
      R13         REGSYM    ---      BYTE      Acc
      00FF5E4EH   BLOCK     CODE     NEAR LAB  LVL=1
      R7          REGSYM    ---      BYTE      crc
      ---         BLOCKEND  ---      ---       LVL=1
      00FF5E44H   LINE      CODE     ---       #65
      00FF5E4EH   LINE      CODE     ---       #66
      00FF5E4EH   LINE      CODE     ---       #68
      00FF5E4EH   LINE      CODE     ---       #69
      00FF5E54H   LINE      CODE     ---       #70
      00FF5E59H   LINE      CODE     ---       #71
      00FF5E67H   LINE      CODE     ---       #72
      00FF5E71H   LINE      CODE     ---       #73
      00FF5E78H   LINE      CODE     ---       #74
      00FF5E92H   LINE      CODE     ---       #75
      00FF5E9DH   LINE      CODE     ---       #76
      00FF5EA1H   LINE      CODE     ---       #77
      00FF5EBDH   LINE      CODE     ---       #78
      00FF5EC1H   LINE      CODE     ---       #79
      00FF5ED6H   LINE      CODE     ---       #81
      ---         BLOCKEND  ---      ---       LVL=0

      00FF54FFH   BLOCK     CODE     ---       LVL=0
      WR14        REGSYM    ---      WORD      Motor
      WR2         REGSYM    ---      INT       Spd
      00FF5505H   BLOCK     CODE     NEAR LAB  LVL=1
      R7          REGSYM    ---      BYTE      crc
      ---         BLOCKEND  ---      ---       LVL=1
      00FF54FFH   LINE      CODE     ---       #82
      00FF5505H   LINE      CODE     ---       #83
      00FF5505H   LINE      CODE     ---       #85
      00FF550BH   LINE      CODE     ---       #86
      00FF5510H   LINE      CODE     ---       #87
      00FF551AH   LINE      CODE     ---       #88
      00FF551FH   LINE      CODE     ---       #89
      00FF5526H   LINE      CODE     ---       #90
      00FF5533H   LINE      CODE     ---       #91
      00FF553EH   LINE      CODE     ---       #92
      00FF5544H   LINE      CODE     ---       #93
      00FF5572H   LINE      CODE     ---       #94
      00FF5576H   LINE      CODE     ---       #95
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 62


      00FF558BH   LINE      CODE     ---       #96
      00FF5591H   LINE      CODE     ---       #97
      00FF5597H   LINE      CODE     ---       #98
      00FF559BH   LINE      CODE     ---       #99
      00FF55B2H   LINE      CODE     ---       #100
      00FF55C5H   LINE      CODE     ---       #101
      ---         BLOCKEND  ---      ---       LVL=0

      00FF5350H   BLOCK     CODE     ---       LVL=0
      WR14        REGSYM    ---      WORD      Motor
      00FF5354H   BLOCK     CODE     NEAR LAB  LVL=1
      R7          REGSYM    ---      BYTE      crc
      ---         BLOCKEND  ---      ---       LVL=1
      00FF5350H   LINE      CODE     ---       #104
      00FF5354H   LINE      CODE     ---       #105
      00FF5354H   LINE      CODE     ---       #107
      00FF5354H   LINE      CODE     ---       #109
      00FF535AH   LINE      CODE     ---       #110
      00FF535FH   LINE      CODE     ---       #111
      00FF5367H   LINE      CODE     ---       #112
      00FF536BH   LINE      CODE     ---       #113
      00FF5373H   LINE      CODE     ---       #114
      00FF5377H   LINE      CODE     ---       #115
      00FF53A3H   LINE      CODE     ---       #116
      00FF53A7H   LINE      CODE     ---       #117
      00FF53BCH   LINE      CODE     ---       #120
      00FF53C2H   LINE      CODE     ---       #121
      00FF53C8H   LINE      CODE     ---       #122
      00FF53D0H   LINE      CODE     ---       #123
      00FF53D4H   LINE      CODE     ---       #124
      00FF53DCH   LINE      CODE     ---       #125
      00FF53E0H   LINE      CODE     ---       #126
      00FF540CH   LINE      CODE     ---       #127
      00FF5410H   LINE      CODE     ---       #128
      00FF5425H   LINE      CODE     ---       #130
      ---         BLOCKEND  ---      ---       LVL=0

      00FF005FH   BLOCK     CODE     ---       LVL=0
      00FF005FH   LINE      CODE     ---       #132
      00FF005FH   LINE      CODE     ---       #135
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       D2Car
      00FF730CH   PUBLIC    CODE     ---       ESD_M1_SPD_CONTROL
      00FF6471H   PUBLIC    CODE     ---       ESD_M1_POS_CONTROL
      00FF7371H   PUBLIC    CODE     ---       ESD_M1_PWM_CONTROL
      00FF574FH   PUBLIC    CODE     ---       CAR_PID_CONTROL
      00FF4C5BH   PUBLIC    CODE     ---       CAR_ADD_ANGLE
      000000B6H   PUBLIC    EDATA    INT       ACC_X
      000000B8H   PUBLIC    EDATA    INT       ACC_Y
      000000BAH   PUBLIC    EDATA    INT       ACC_Z
      000000BCH   PUBLIC    EDATA    FLOAT     CAR_ANG
      000000C0H   PUBLIC    EDATA    ---       D2long_uchar
      000000C4H   PUBLIC    EDATA    FLOAT     ANG_ERR
      000000C8H   PUBLIC    EDATA    FLOAT     SET_ANG
      000000CCH   PUBLIC    EDATA    INT       GYRO_Z_OFFSET
      000000CEH   PUBLIC    EDATA    FLOAT     ANG_OUT
      000000D2H   PUBLIC    EDATA    ---       D2_CAN_TX
      000000DAH   PUBLIC    EDATA    ---       D2int_uchar
      000000DCH   PUBLIC    EDATA    INT       GYRO_Z_OFFSET_ADD
      000000DEH   PUBLIC    EDATA    BYTE      GYRO_Z_OFFSET_N
      000000DFH   PUBLIC    EDATA    FLOAT     ANG_ERR_OLD
      000000E3H   PUBLIC    EDATA    ---       MPU_data
      000000F1H   PUBLIC    EDATA    FLOAT     GYRO_X
      000000F5H   PUBLIC    EDATA    FLOAT     GYRO_Y
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 63


      000000F9H   PUBLIC    EDATA    FLOAT     GYRO_Z
      00000021H.4 PUBLIC    BIT      BIT       ?ESD_M1_POS_CONTROL?BIT
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF574FH   BLOCK     CODE     ---       LVL=0
      00FF574FH   LINE      CODE     ---       #38
      00FF574FH   LINE      CODE     ---       #40
      00FF5758H   LINE      CODE     ---       #42
      00FF575DH   LINE      CODE     ---       #43
      00FF5769H   LINE      CODE     ---       #44
      00FF576AH   LINE      CODE     ---       #47
      00FF5784H   LINE      CODE     ---       #48
      00FF57AEH   LINE      CODE     ---       #49
      00FF57BFH   LINE      CODE     ---       #50
      00FF57E7H   LINE      CODE     ---       #52
      00FF57F2H   LINE      CODE     ---       #53
      00FF5807H   LINE      CODE     ---       #55
      00FF580FH   LINE      CODE     ---       #56
      00FF580FH   LINE      CODE     ---       #57
      ---         BLOCKEND  ---      ---       LVL=0

L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 64


      00FF4C5BH   BLOCK     CODE     ---       LVL=0
      DR28        REGSYM    ---      FLOAT     SET_ANGLE
      00FF4C5FH   BLOCK     CODE     NEAR LAB  LVL=1
      R15         REGSYM    ---      BYTE      n
      ---         BLOCKEND  ---      ---       LVL=1
      00FF4C5BH   LINE      CODE     ---       #60
      00FF4C5FH   LINE      CODE     ---       #61
      00FF4C5FH   LINE      CODE     ---       #62
      00FF4C62H   LINE      CODE     ---       #64
      00FF4C6FH   LINE      CODE     ---       #65
      00FF4C6FH   LINE      CODE     ---       #66
      00FF4C71H   LINE      CODE     ---       #68
      00FF4C86H   LINE      CODE     ---       #69
      00FF4C9BH   LINE      CODE     ---       #70
      00FF4CB0H   LINE      CODE     ---       #71
      00FF4CC5H   LINE      CODE     ---       #72
      00FF4CCCH   LINE      CODE     ---       #74
      00FF4CDEH   LINE      CODE     ---       #75
      00FF4CE0H   LINE      CODE     ---       #77
      00FF4CF5H   LINE      CODE     ---       #78
      00FF4D0AH   LINE      CODE     ---       #79
      00FF4D1FH   LINE      CODE     ---       #80
      00FF4D34H   LINE      CODE     ---       #81
      00FF4D3BH   LINE      CODE     ---       #83
      00FF4D43H   LINE      CODE     ---       #84
      00FF4D4DH   LINE      CODE     ---       #85
      00FF4D57H   LINE      CODE     ---       #86
      00FF4D61H   LINE      CODE     ---       #87
      00FF4D6BH   LINE      CODE     ---       #88
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6471H   BLOCK     CODE     ---       LVL=0
      WR2         REGSYM    ---      WORD      Motor
      DR28        REGSYM    ---      LONG      Pos
      WR4         REGSYM    ---      WORD      Spd
      R10         REGSYM    ---      BYTE      Acc
      00000021H.4 SYMBOL    BIT      BIT       Mode
      00FF6471H   LINE      CODE     ---       #91
      00FF6477H   LINE      CODE     ---       #93
      00FF647EH   LINE      CODE     ---       #94
      00FF6484H   LINE      CODE     ---       #96
      00FF6488H   LINE      CODE     ---       #97
      00FF648EH   LINE      CODE     ---       #99
      00FF6492H   LINE      CODE     ---       #101
      00FF6496H   LINE      CODE     ---       #103
      00FF649EH   LINE      CODE     ---       #104
      00FF64A6H   LINE      CODE     ---       #105
      00FF64AEH   LINE      CODE     ---       #106
      00FF64B6H   LINE      CODE     ---       #108
      00FF64CBH   LINE      CODE     ---       #110
      ---         BLOCKEND  ---      ---       LVL=0

      00FF730CH   BLOCK     CODE     ---       LVL=0
      WR8         REGSYM    ---      WORD      Motor
      WR4         REGSYM    ---      INT       Spd
      R10         REGSYM    ---      BYTE      Acc
      00FF730CH   LINE      CODE     ---       #114
      00FF730EH   LINE      CODE     ---       #116
      00FF7314H   LINE      CODE     ---       #118
      00FF7318H   LINE      CODE     ---       #120
      00FF7320H   LINE      CODE     ---       #121
      00FF7328H   LINE      CODE     ---       #122
      00FF732CH   LINE      CODE     ---       #124
      ---         BLOCKEND  ---      ---       LVL=0

L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 65


      00FF7371H   BLOCK     CODE     ---       LVL=0
      R10         REGSYM    ---      BYTE      Motor
      WR6         REGSYM    ---      INT       PWM_dat
      00FF7371H   LINE      CODE     ---       #127
      00FF7373H   LINE      CODE     ---       #129
      00FF7379H   LINE      CODE     ---       #131
      00FF737DH   LINE      CODE     ---       #133
      00FF7385H   LINE      CODE     ---       #134
      00FF738DH   LINE      CODE     ---       #136
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ADC
      00FF64E0H   PUBLIC    CODE     ---       ADC_init
      00FF6E1AH   PUBLIC    CODE     ---       ADC_get
      00000094H   SFRSYM    DATA     BYTE      P0M0
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000BEH   SFRSYM    DATA     BYTE      ADC_RESL
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000BCH.5 SFRSYM    DATA     BIT       ADC_FLAG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      000000BCH.6 SFRSYM    DATA     BIT       ADC_START
      000000BCH.7 SFRSYM    DATA     BIT       ADC_POWER
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000BDH   SFRSYM    DATA     BYTE      ADC_RES
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 66


      00000091H   SFRSYM    DATA     BYTE      P1M1
      00000092H   SFRSYM    DATA     BYTE      P1M0
      00000093H   SFRSYM    DATA     BYTE      P0M1

      00FF64E0H   BLOCK     CODE     ---       LVL=0
      WR2         REGSYM    ---      INT       adcn
      WR0         REGSYM    ---      INT       speed
      00FF64E0H   LINE      CODE     ---       #6
      00FF64E4H   LINE      CODE     ---       #8
      00FF64E7H   LINE      CODE     ---       #10
      00FF64EAH   LINE      CODE     ---       #11
      00FF64F0H   LINE      CODE     ---       #12
      00FF64FDH   LINE      CODE     ---       #15
      00FF650BH   LINE      CODE     ---       #18
      00FF6520H   LINE      CODE     ---       #19
      00FF6524H   LINE      CODE     ---       #20
      00FF6526H   LINE      CODE     ---       #21
      00FF652AH   LINE      CODE     ---       #24
      00FF653FH   LINE      CODE     ---       #25
      00FF6543H   LINE      CODE     ---       #26
      00FF6543H   LINE      CODE     ---       #28
      00FF6549H   LINE      CODE     ---       #30
      00FF654CH   LINE      CODE     ---       #31
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6E1AH   BLOCK     CODE     ---       LVL=0
      WR6         REGSYM    ---      INT       adcn
      WR6         REGSYM    ---      WORD      adc_value
      00FF6E1AH   LINE      CODE     ---       #33
      00FF6E1AH   LINE      CODE     ---       #34
      00FF6E1AH   LINE      CODE     ---       #37
      00FF6E1DH   LINE      CODE     ---       #38
      00FF6E21H   LINE      CODE     ---       #40
      00FF6E24H   LINE      CODE     ---       #41
      00FF6E28H   LINE      CODE     ---       #42
      00FF6E2CH   LINE      CODE     ---       #43
      00FF6E2FH   LINE      CODE     ---       #45
      00FF6E31H   LINE      CODE     ---       #46
      00FF6E35H   LINE      CODE     ---       #47
      00FF6E3BH   LINE      CODE     ---       #49
      00FF6E3EH   LINE      CODE     ---       #50
      00FF6E41H   LINE      CODE     ---       #54
      00FF6E5EH   LINE      CODE     ---       #56
      00FF6E5EH   LINE      CODE     ---       #57
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       Delay
      00FF7DB7H   PUBLIC    CODE     ---       Delay_X_mS
      00FF7DCEH   PUBLIC    CODE     ---       Delay_X_uS
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 67


      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF7DB7H   BLOCK     CODE     ---       LVL=0
      WR4         REGSYM    ---      WORD      ms
      00FF7DB9H   BLOCK     CODE     NEAR LAB  LVL=1
      WR6         REGSYM    ---      WORD      i
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7DB7H   LINE      CODE     ---       #3
      00FF7DB9H   LINE      CODE     ---       #4
      00FF7DB9H   LINE      CODE     ---       #6
      00FF7DB9H   LINE      CODE     ---       #7
      00FF7DBDH   LINE      CODE     ---       #8
      00FF7DC5H   LINE      CODE     ---       #9
      00FF7DCDH   LINE      CODE     ---       #10
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7DCEH   BLOCK     CODE     ---       LVL=0
      WR4         REGSYM    ---      WORD      us
      00FF7DD0H   BLOCK     CODE     NEAR LAB  LVL=1
      WR6         REGSYM    ---      WORD      i
      ---         BLOCKEND  ---      ---       LVL=1
      00FF7DCEH   LINE      CODE     ---       #11
      00FF7DD0H   LINE      CODE     ---       #12
      00FF7DD0H   LINE      CODE     ---       #14
      00FF7DD0H   LINE      CODE     ---       #15
      00FF7DD4H   LINE      CODE     ---       #16
      00FF7DDCH   LINE      CODE     ---       #17
      00FF7DE4H   LINE      CODE     ---       #18
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       GPIO
      00FF3718H   PUBLIC    CODE     ---       Get_IO
      00FF434CH   PUBLIC    CODE     ---       GPIO_init_8pin
      00FF6761H   PUBLIC    CODE     ---       GPIO_init_allpin
      00FF3BC9H   PUBLIC    CODE     ---       Out_IO
      00FF4D6EH   PUBLIC    CODE     ---       GPIO_isr_deinit
      00FF340AH   PUBLIC    CODE     ---       GPIO_init_pin
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 68


      00FF2739H   PUBLIC    CODE     ---       GPIO_isr_init
      00FF41A5H   PUBLIC    CODE     ---       GPIO_pull_pin
      00000021H.6 PUBLIC    BIT      BIT       ?Out_IO?BIT
      00000094H   SFRSYM    DATA     BYTE      P0M0
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      000000F8H.7 SFRSYM    DATA     BIT       P77
      000000E8H.7 SFRSYM    DATA     BIT       P67
      000000F8H.6 SFRSYM    DATA     BIT       P76
      000000E8H.6 SFRSYM    DATA     BIT       P66
      000000F8H.5 SFRSYM    DATA     BIT       P75
      000000E8H.5 SFRSYM    DATA     BIT       P65
      000000F8H.4 SFRSYM    DATA     BIT       P74
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000C8H.5 SFRSYM    DATA     BIT       P55
      000000E8H.4 SFRSYM    DATA     BIT       P64
      000000F8H.3 SFRSYM    DATA     BIT       P73
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000C8H.4 SFRSYM    DATA     BIT       P54
      000000E8H.3 SFRSYM    DATA     BIT       P63
      000000F8H.2 SFRSYM    DATA     BIT       P72
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000A0H.6 SFRSYM    DATA     BIT       P26
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000C0H.4 SFRSYM    DATA     BIT       P44
      000000C8H.3 SFRSYM    DATA     BIT       P53
      000000E8H.2 SFRSYM    DATA     BIT       P62
      000000F8H.1 SFRSYM    DATA     BIT       P71
      00000080H.7 SFRSYM    DATA     BIT       P07
      00000090H.6 SFRSYM    DATA     BIT       P16
      000000A0H.5 SFRSYM    DATA     BIT       P25
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000C0H.3 SFRSYM    DATA     BIT       P43
      000000C8H.2 SFRSYM    DATA     BIT       P52
      000000E8H.1 SFRSYM    DATA     BIT       P61
      000000F8H.0 SFRSYM    DATA     BIT       P70
      00000080H.6 SFRSYM    DATA     BIT       P06
      00000090H.5 SFRSYM    DATA     BIT       P15
      000000A0H.4 SFRSYM    DATA     BIT       P24
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000C0H.2 SFRSYM    DATA     BIT       P42
      000000C8H.1 SFRSYM    DATA     BIT       P51
      000000E8H.0 SFRSYM    DATA     BIT       P60
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000090H.4 SFRSYM    DATA     BIT       P14
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 69


      000000A0H.3 SFRSYM    DATA     BIT       P23
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000C0H.1 SFRSYM    DATA     BIT       P41
      000000C8H.0 SFRSYM    DATA     BIT       P50
      00000080H.4 SFRSYM    DATA     BIT       P04
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000A0H.2 SFRSYM    DATA     BIT       P22
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000C0H.0 SFRSYM    DATA     BIT       P40
      00000080H.3 SFRSYM    DATA     BIT       P03
      00000090H.2 SFRSYM    DATA     BIT       P12
      000000A0H.1 SFRSYM    DATA     BIT       P21
      000000B0H.0 SFRSYM    DATA     BIT       P30
      00000080H.2 SFRSYM    DATA     BIT       P02
      00000090H.1 SFRSYM    DATA     BIT       P11
      000000A0H.0 SFRSYM    DATA     BIT       P20
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000080H.1 SFRSYM    DATA     BIT       P01
      00000087H   SFRSYM    DATA     BYTE      PCON
      00000090H.0 SFRSYM    DATA     BIT       P10
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      00000080H.0 SFRSYM    DATA     BIT       P00
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E1H   SFRSYM    DATA     BYTE      P7M1
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000CBH   SFRSYM    DATA     BYTE      P6M1
      000000E2H   SFRSYM    DATA     BYTE      P7M0
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000C9H   SFRSYM    DATA     BYTE      P5M1
      000000CCH   SFRSYM    DATA     BYTE      P6M0
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000B3H   SFRSYM    DATA     BYTE      P4M1
      000000CAH   SFRSYM    DATA     BYTE      P5M0
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B1H   SFRSYM    DATA     BYTE      P3M1
      000000B4H   SFRSYM    DATA     BYTE      P4M0
      00000090H   SFRSYM    DATA     BYTE      P1
      00000095H   SFRSYM    DATA     BYTE      P2M1
      000000B2H   SFRSYM    DATA     BYTE      P3M0
      00000080H   SFRSYM    DATA     BYTE      P0
      00000091H   SFRSYM    DATA     BYTE      P1M1
      00000096H   SFRSYM    DATA     BYTE      P2M0
      00000092H   SFRSYM    DATA     BYTE      P1M0
      00000093H   SFRSYM    DATA     BYTE      P0M1
      00FF825AH   SYMBOL    HCONST   ---       ?tpl?0001
      00FF8262H   SYMBOL    HCONST   ---       ?tpl?0002
      00FF826AH   SYMBOL    HCONST   ---       ?tpl?0003

      00FF340AH   BLOCK     CODE     ---       LVL=0
      R14         REGSYM    ---      BYTE      pin
      R15         REGSYM    ---      BYTE      mode
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 70


      00FF3410H   BLOCK     CODE     NEAR LAB  LVL=1
      R10         REGSYM    ---      BYTE      a
      R7          REGSYM    ---      BYTE      b
      00000270H   SYMBOL    EDATA    ---       c
      ---         BLOCKEND  ---      ---       LVL=1
      00FF340AH   LINE      CODE     ---       #2
      00FF3410H   LINE      CODE     ---       #3
      00FF3410H   LINE      CODE     ---       #5
      00FF342FH   LINE      CODE     ---       #6
      00FF3439H   LINE      CODE     ---       #7
      00FF3444H   LINE      CODE     ---       #8
      00FF346EH   LINE      CODE     ---       #10
      00FF346EH   LINE      CODE     ---       #11
      00FF3480H   LINE      CODE     ---       #13
      00FF348CH   LINE      CODE     ---       #14
      00FF349FH   LINE      CODE     ---       #15
      00FF34B0H   LINE      CODE     ---       #16
      00FF34C1H   LINE      CODE     ---       #17
      00FF34C1H   LINE      CODE     ---       #18
      00FF34C4H   LINE      CODE     ---       #19
      00FF34C4H   LINE      CODE     ---       #20
      00FF34D6H   LINE      CODE     ---       #22
      00FF34E2H   LINE      CODE     ---       #23
      00FF34F5H   LINE      CODE     ---       #24
      00FF3506H   LINE      CODE     ---       #25
      00FF3517H   LINE      CODE     ---       #26
      00FF3517H   LINE      CODE     ---       #27
      00FF351AH   LINE      CODE     ---       #28
      00FF351AH   LINE      CODE     ---       #29
      00FF352CH   LINE      CODE     ---       #31
      00FF3538H   LINE      CODE     ---       #32
      00FF354BH   LINE      CODE     ---       #33
      00FF355CH   LINE      CODE     ---       #34
      00FF356DH   LINE      CODE     ---       #35
      00FF356DH   LINE      CODE     ---       #36
      00FF3570H   LINE      CODE     ---       #37
      00FF3570H   LINE      CODE     ---       #38
      00FF3582H   LINE      CODE     ---       #40
      00FF358EH   LINE      CODE     ---       #41
      00FF35A1H   LINE      CODE     ---       #42
      00FF35B2H   LINE      CODE     ---       #43
      00FF35C3H   LINE      CODE     ---       #44
      00FF35C3H   LINE      CODE     ---       #45
      00FF35C6H   LINE      CODE     ---       #46
      00FF35C6H   LINE      CODE     ---       #47
      00FF35D8H   LINE      CODE     ---       #49
      00FF35E4H   LINE      CODE     ---       #50
      00FF35F7H   LINE      CODE     ---       #51
      00FF3608H   LINE      CODE     ---       #52
      00FF3619H   LINE      CODE     ---       #53
      00FF3619H   LINE      CODE     ---       #54
      00FF361CH   LINE      CODE     ---       #55
      00FF361CH   LINE      CODE     ---       #56
      00FF362EH   LINE      CODE     ---       #58
      00FF363AH   LINE      CODE     ---       #59
      00FF364DH   LINE      CODE     ---       #60
      00FF365EH   LINE      CODE     ---       #61
      00FF366FH   LINE      CODE     ---       #62
      00FF366FH   LINE      CODE     ---       #63
      00FF3672H   LINE      CODE     ---       #64
      00FF3672H   LINE      CODE     ---       #65
      00FF3684H   LINE      CODE     ---       #67
      00FF3690H   LINE      CODE     ---       #68
      00FF36A2H   LINE      CODE     ---       #69
      00FF36B2H   LINE      CODE     ---       #70
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 71


      00FF36C3H   LINE      CODE     ---       #71
      00FF36C3H   LINE      CODE     ---       #72
      00FF36C5H   LINE      CODE     ---       #73
      00FF36C5H   LINE      CODE     ---       #74
      00FF36D6H   LINE      CODE     ---       #76
      00FF36E2H   LINE      CODE     ---       #77
      00FF36F4H   LINE      CODE     ---       #78
      00FF3704H   LINE      CODE     ---       #79
      00FF3715H   LINE      CODE     ---       #80
      00FF3715H   LINE      CODE     ---       #81
      00FF3715H   LINE      CODE     ---       #82
      00FF3715H   LINE      CODE     ---       #83
      ---         BLOCKEND  ---      ---       LVL=0

      00FF434CH   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      pin
      R3          REGSYM    ---      BYTE      mode
      00FF434CH   LINE      CODE     ---       #86
      00FF434EH   LINE      CODE     ---       #88
      00FF4381H   LINE      CODE     ---       #90
      00FF4381H   LINE      CODE     ---       #92
      00FF4393H   LINE      CODE     ---       #94
      00FF4398H   LINE      CODE     ---       #95
      00FF439FH   LINE      CODE     ---       #96
      00FF43A4H   LINE      CODE     ---       #97
      00FF43AAH   LINE      CODE     ---       #98
      00FF43AAH   LINE      CODE     ---       #99
      00FF43ABH   LINE      CODE     ---       #100
      00FF43ABH   LINE      CODE     ---       #102
      00FF43BDH   LINE      CODE     ---       #104
      00FF43C2H   LINE      CODE     ---       #105
      00FF43C9H   LINE      CODE     ---       #106
      00FF43CEH   LINE      CODE     ---       #107
      00FF43D4H   LINE      CODE     ---       #108
      00FF43D4H   LINE      CODE     ---       #109
      00FF43D5H   LINE      CODE     ---       #110
      00FF43D5H   LINE      CODE     ---       #112
      00FF43E7H   LINE      CODE     ---       #114
      00FF43ECH   LINE      CODE     ---       #115
      00FF43F3H   LINE      CODE     ---       #116
      00FF43F8H   LINE      CODE     ---       #117
      00FF43FEH   LINE      CODE     ---       #118
      00FF43FEH   LINE      CODE     ---       #119
      00FF43FFH   LINE      CODE     ---       #120
      00FF43FFH   LINE      CODE     ---       #122
      00FF4411H   LINE      CODE     ---       #124
      00FF4416H   LINE      CODE     ---       #125
      00FF441DH   LINE      CODE     ---       #126
      00FF4422H   LINE      CODE     ---       #127
      00FF4428H   LINE      CODE     ---       #128
      00FF4428H   LINE      CODE     ---       #129
      00FF4429H   LINE      CODE     ---       #130
      00FF4429H   LINE      CODE     ---       #132
      00FF443BH   LINE      CODE     ---       #134
      00FF4440H   LINE      CODE     ---       #135
      00FF4447H   LINE      CODE     ---       #136
      00FF444CH   LINE      CODE     ---       #137
      00FF4452H   LINE      CODE     ---       #138
      00FF4452H   LINE      CODE     ---       #139
      00FF4453H   LINE      CODE     ---       #140
      00FF4453H   LINE      CODE     ---       #142
      00FF4462H   LINE      CODE     ---       #144
      00FF4467H   LINE      CODE     ---       #145
      00FF446EH   LINE      CODE     ---       #146
      00FF4473H   LINE      CODE     ---       #147
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 72


      00FF4479H   LINE      CODE     ---       #148
      00FF4479H   LINE      CODE     ---       #149
      00FF447AH   LINE      CODE     ---       #150
      00FF447AH   LINE      CODE     ---       #152
      00FF4489H   LINE      CODE     ---       #154
      00FF448EH   LINE      CODE     ---       #155
      00FF4495H   LINE      CODE     ---       #156
      00FF449AH   LINE      CODE     ---       #157
      00FF44A0H   LINE      CODE     ---       #158
      00FF44A0H   LINE      CODE     ---       #159
      00FF44A1H   LINE      CODE     ---       #160
      00FF44A1H   LINE      CODE     ---       #162
      00FF44B2H   LINE      CODE     ---       #164
      00FF44B7H   LINE      CODE     ---       #165
      00FF44BEH   LINE      CODE     ---       #166
      00FF44C3H   LINE      CODE     ---       #167
      00FF44C9H   LINE      CODE     ---       #168
      00FF44C9H   LINE      CODE     ---       #169
      00FF44C9H   LINE      CODE     ---       #170
      00FF44C9H   LINE      CODE     ---       #171
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6761H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      mode
      00FF6767H   BLOCK     CODE     NEAR LAB  LVL=1
      R13         REGSYM    ---      BYTE      a
      R14         REGSYM    ---      BYTE      b
      ---         BLOCKEND  ---      ---       LVL=1
      00FF6761H   LINE      CODE     ---       #173
      00FF6767H   LINE      CODE     ---       #174
      00FF6767H   LINE      CODE     ---       #176
      00FF676AH   LINE      CODE     ---       #177
      00FF677BH   LINE      CODE     ---       #179
      00FF677BH   LINE      CODE     ---       #181
      00FF677DH   LINE      CODE     ---       #182
      00FF677FH   LINE      CODE     ---       #183
      00FF6781H   LINE      CODE     ---       #184
      00FF6781H   LINE      CODE     ---       #186
      00FF6784H   LINE      CODE     ---       #187
      00FF6786H   LINE      CODE     ---       #188
      00FF6788H   LINE      CODE     ---       #189
      00FF6788H   LINE      CODE     ---       #191
      00FF678AH   LINE      CODE     ---       #192
      00FF678AH   LINE      CODE     ---       #193
      00FF678CH   LINE      CODE     ---       #194
      00FF678CH   LINE      CODE     ---       #196
      00FF678FH   LINE      CODE     ---       #197
      00FF6792H   LINE      CODE     ---       #198
      00FF6792H   LINE      CODE     ---       #199
      00FF6792H   LINE      CODE     ---       #200
      00FF6795H   LINE      CODE     ---       #201
      00FF6798H   LINE      CODE     ---       #202
      00FF679BH   LINE      CODE     ---       #203
      00FF679EH   LINE      CODE     ---       #204
      00FF67A1H   LINE      CODE     ---       #205
      00FF67A4H   LINE      CODE     ---       #206
      00FF67A7H   LINE      CODE     ---       #207
      00FF67AAH   LINE      CODE     ---       #208
      00FF67ADH   LINE      CODE     ---       #209
      00FF67B0H   LINE      CODE     ---       #210
      00FF67B3H   LINE      CODE     ---       #211
      00FF67B6H   LINE      CODE     ---       #212
      00FF67B9H   LINE      CODE     ---       #213
      00FF67BCH   LINE      CODE     ---       #214
      00FF67BFH   LINE      CODE     ---       #215
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 73


      00FF67C2H   LINE      CODE     ---       #217
      ---         BLOCKEND  ---      ---       LVL=0

      00FF41A5H   BLOCK     CODE     ---       LVL=0
      R14         REGSYM    ---      BYTE      pin
      R15         REGSYM    ---      BYTE      mode
      00FF41ABH   BLOCK     CODE     NEAR LAB  LVL=1
      R10         REGSYM    ---      BYTE      a
      R7          REGSYM    ---      BYTE      b
      00000270H   SYMBOL    EDATA    ---       c
      ---         BLOCKEND  ---      ---       LVL=1
      00FF41A5H   LINE      CODE     ---       #219
      00FF41ABH   LINE      CODE     ---       #220
      00FF41ABH   LINE      CODE     ---       #222
      00FF41CAH   LINE      CODE     ---       #223
      00FF41D4H   LINE      CODE     ---       #224
      00FF41DFH   LINE      CODE     ---       #226
      00FF41E2H   LINE      CODE     ---       #227
      00FF420CH   LINE      CODE     ---       #229
      00FF420CH   LINE      CODE     ---       #230
      00FF4217H   LINE      CODE     ---       #232
      00FF4224H   LINE      CODE     ---       #233
      00FF4230H   LINE      CODE     ---       #234
      00FF4230H   LINE      CODE     ---       #235
      00FF4233H   LINE      CODE     ---       #236
      00FF4233H   LINE      CODE     ---       #237
      00FF423EH   LINE      CODE     ---       #239
      00FF424BH   LINE      CODE     ---       #240
      00FF4257H   LINE      CODE     ---       #241
      00FF4257H   LINE      CODE     ---       #242
      00FF425AH   LINE      CODE     ---       #243
      00FF425AH   LINE      CODE     ---       #244
      00FF4265H   LINE      CODE     ---       #246
      00FF4272H   LINE      CODE     ---       #247
      00FF427EH   LINE      CODE     ---       #248
      00FF427EH   LINE      CODE     ---       #249
      00FF4281H   LINE      CODE     ---       #250
      00FF4281H   LINE      CODE     ---       #251
      00FF428CH   LINE      CODE     ---       #253
      00FF4299H   LINE      CODE     ---       #254
      00FF42A5H   LINE      CODE     ---       #255
      00FF42A5H   LINE      CODE     ---       #256
      00FF42A8H   LINE      CODE     ---       #257
      00FF42A8H   LINE      CODE     ---       #258
      00FF42B3H   LINE      CODE     ---       #260
      00FF42BFH   LINE      CODE     ---       #261
      00FF42CBH   LINE      CODE     ---       #262
      00FF42CBH   LINE      CODE     ---       #263
      00FF42CDH   LINE      CODE     ---       #264
      00FF42CDH   LINE      CODE     ---       #265
      00FF42D5H   LINE      CODE     ---       #267
      00FF42E1H   LINE      CODE     ---       #268
      00FF42EDH   LINE      CODE     ---       #269
      00FF42EDH   LINE      CODE     ---       #270
      00FF42EFH   LINE      CODE     ---       #271
      00FF42EFH   LINE      CODE     ---       #272
      00FF42F7H   LINE      CODE     ---       #274
      00FF4303H   LINE      CODE     ---       #275
      00FF430FH   LINE      CODE     ---       #276
      00FF430FH   LINE      CODE     ---       #277
      00FF4311H   LINE      CODE     ---       #278
      00FF4311H   LINE      CODE     ---       #279
      00FF4319H   LINE      CODE     ---       #281
      00FF432EH   LINE      CODE     ---       #282
      00FF4346H   LINE      CODE     ---       #283
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 74


      00FF4346H   LINE      CODE     ---       #284
      00FF4346H   LINE      CODE     ---       #285
      00FF4346H   LINE      CODE     ---       #286
      00FF4349H   LINE      CODE     ---       #287
      ---         BLOCKEND  ---      ---       LVL=0

      00FF2739H   BLOCK     CODE     ---       LVL=0
      R14         REGSYM    ---      BYTE      pin
      R15         REGSYM    ---      BYTE      mode
      00FF273FH   BLOCK     CODE     NEAR LAB  LVL=1
      R7          REGSYM    ---      BYTE      psw2_old
      R10         REGSYM    ---      BYTE      a
      R6          REGSYM    ---      BYTE      b
      000002ABH   SYMBOL    EDATA    ---       c
      ---         BLOCKEND  ---      ---       LVL=1
      00FF2739H   LINE      CODE     ---       #290
      00FF273FH   LINE      CODE     ---       #291
      00FF273FH   LINE      CODE     ---       #294
      00FF275EH   LINE      CODE     ---       #295
      00FF2768H   LINE      CODE     ---       #296
      00FF2773H   LINE      CODE     ---       #299
      00FF2776H   LINE      CODE     ---       #300
      00FF2779H   LINE      CODE     ---       #302
      00FF27A3H   LINE      CODE     ---       #304
      00FF27A3H   LINE      CODE     ---       #305
      00FF27B5H   LINE      CODE     ---       #307
      00FF27CAH   LINE      CODE     ---       #308
      00FF27F3H   LINE      CODE     ---       #309
      00FF2816H   LINE      CODE     ---       #310
      00FF283AH   LINE      CODE     ---       #311
      00FF283AH   LINE      CODE     ---       #312
      00FF284BH   LINE      CODE     ---       #313
      00FF284EH   LINE      CODE     ---       #314
      00FF284EH   LINE      CODE     ---       #315
      00FF2860H   LINE      CODE     ---       #317
      00FF2875H   LINE      CODE     ---       #318
      00FF289EH   LINE      CODE     ---       #319
      00FF28C1H   LINE      CODE     ---       #320
      00FF28E5H   LINE      CODE     ---       #321
      00FF28E5H   LINE      CODE     ---       #322
      00FF28F6H   LINE      CODE     ---       #323
      00FF28F9H   LINE      CODE     ---       #324
      00FF28F9H   LINE      CODE     ---       #325
      00FF290BH   LINE      CODE     ---       #327
      00FF2920H   LINE      CODE     ---       #328
      00FF2949H   LINE      CODE     ---       #329
      00FF296CH   LINE      CODE     ---       #330
      00FF2990H   LINE      CODE     ---       #331
      00FF2990H   LINE      CODE     ---       #332
      00FF29A1H   LINE      CODE     ---       #333
      00FF29A4H   LINE      CODE     ---       #334
      00FF29A4H   LINE      CODE     ---       #335
      00FF29B6H   LINE      CODE     ---       #337
      00FF29CBH   LINE      CODE     ---       #338
      00FF29F4H   LINE      CODE     ---       #339
      00FF2A17H   LINE      CODE     ---       #340
      00FF2A3BH   LINE      CODE     ---       #341
      00FF2A3BH   LINE      CODE     ---       #342
      00FF2A4CH   LINE      CODE     ---       #343
      00FF2A4FH   LINE      CODE     ---       #344
      00FF2A4FH   LINE      CODE     ---       #345
      00FF2A61H   LINE      CODE     ---       #347
      00FF2A76H   LINE      CODE     ---       #348
      00FF2A9FH   LINE      CODE     ---       #349
      00FF2AC2H   LINE      CODE     ---       #350
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 75


      00FF2AE6H   LINE      CODE     ---       #351
      00FF2AE6H   LINE      CODE     ---       #352
      00FF2AF7H   LINE      CODE     ---       #353
      00FF2AFAH   LINE      CODE     ---       #354
      00FF2AFAH   LINE      CODE     ---       #355
      00FF2B0CH   LINE      CODE     ---       #357
      00FF2B21H   LINE      CODE     ---       #358
      00FF2B4AH   LINE      CODE     ---       #359
      00FF2B6DH   LINE      CODE     ---       #360
      00FF2B91H   LINE      CODE     ---       #361
      00FF2B91H   LINE      CODE     ---       #362
      00FF2BA2H   LINE      CODE     ---       #363
      00FF2BA5H   LINE      CODE     ---       #364
      00FF2BA5H   LINE      CODE     ---       #365
      00FF2BB7H   LINE      CODE     ---       #367
      00FF2BCCH   LINE      CODE     ---       #368
      00FF2BF5H   LINE      CODE     ---       #369
      00FF2C18H   LINE      CODE     ---       #370
      00FF2C3CH   LINE      CODE     ---       #371
      00FF2C3CH   LINE      CODE     ---       #372
      00FF2C4DH   LINE      CODE     ---       #373
      00FF2C50H   LINE      CODE     ---       #374
      00FF2C50H   LINE      CODE     ---       #375
      00FF2C64H   LINE      CODE     ---       #377
      00FF2C79H   LINE      CODE     ---       #378
      00FF2CA2H   LINE      CODE     ---       #379
      00FF2CC5H   LINE      CODE     ---       #380
      00FF2CE9H   LINE      CODE     ---       #381
      00FF2CE9H   LINE      CODE     ---       #382
      00FF2D06H   LINE      CODE     ---       #383
      00FF2D06H   LINE      CODE     ---       #385
      00FF2D06H   LINE      CODE     ---       #387
      00FF2D09H   LINE      CODE     ---       #389
      ---         BLOCKEND  ---      ---       LVL=0

      00FF4D6EH   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      pin
      R7          REGSYM    ---      BYTE      psw2_old
      R2          REGSYM    ---      BYTE      a
      R6          REGSYM    ---      BYTE      b
      00FF4D6EH   LINE      CODE     ---       #391
      00FF4D6EH   LINE      CODE     ---       #392
      00FF4D6EH   LINE      CODE     ---       #396
      00FF4D78H   LINE      CODE     ---       #397
      00FF4D83H   LINE      CODE     ---       #400
      00FF4D86H   LINE      CODE     ---       #401
      00FF4D89H   LINE      CODE     ---       #403
      00FF4DB3H   LINE      CODE     ---       #405
      00FF4DCBH   LINE      CODE     ---       #406
      00FF4DE3H   LINE      CODE     ---       #407
      00FF4DFAH   LINE      CODE     ---       #408
      00FF4E11H   LINE      CODE     ---       #409
      00FF4E28H   LINE      CODE     ---       #410
      00FF4E3FH   LINE      CODE     ---       #411
      00FF4E56H   LINE      CODE     ---       #412
      00FF4E77H   LINE      CODE     ---       #413
      00FF4E77H   LINE      CODE     ---       #414
      00FF4E7AH   LINE      CODE     ---       #415
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3718H   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      IO
      00000021H.5 SYMBOL    BIT      BIT       status
      00FF3718H   LINE      CODE     ---       #417
      00FF3718H   LINE      CODE     ---       #418
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 76


      00FF3718H   LINE      CODE     ---       #420
      00FF3812H   LINE      CODE     ---       #422
      00FF3819H   LINE      CODE     ---       #423
      00FF3820H   LINE      CODE     ---       #424
      00FF3827H   LINE      CODE     ---       #425
      00FF382EH   LINE      CODE     ---       #426
      00FF3835H   LINE      CODE     ---       #427
      00FF383CH   LINE      CODE     ---       #428
      00FF3843H   LINE      CODE     ---       #429
      00FF384AH   LINE      CODE     ---       #431
      00FF3851H   LINE      CODE     ---       #432
      00FF3858H   LINE      CODE     ---       #433
      00FF385FH   LINE      CODE     ---       #434
      00FF3866H   LINE      CODE     ---       #435
      00FF386DH   LINE      CODE     ---       #436
      00FF3874H   LINE      CODE     ---       #437
      00FF387BH   LINE      CODE     ---       #438
      00FF3882H   LINE      CODE     ---       #440
      00FF3889H   LINE      CODE     ---       #441
      00FF3890H   LINE      CODE     ---       #442
      00FF3897H   LINE      CODE     ---       #443
      00FF389EH   LINE      CODE     ---       #444
      00FF38A5H   LINE      CODE     ---       #445
      00FF38ACH   LINE      CODE     ---       #446
      00FF38B3H   LINE      CODE     ---       #447
      00FF38BAH   LINE      CODE     ---       #449
      00FF38C1H   LINE      CODE     ---       #450
      00FF38C8H   LINE      CODE     ---       #451
      00FF38CFH   LINE      CODE     ---       #452
      00FF38D6H   LINE      CODE     ---       #453
      00FF38DDH   LINE      CODE     ---       #454
      00FF38E4H   LINE      CODE     ---       #455
      00FF38EBH   LINE      CODE     ---       #456
      00FF38F2H   LINE      CODE     ---       #458
      00FF38F9H   LINE      CODE     ---       #459
      00FF3900H   LINE      CODE     ---       #460
      00FF3907H   LINE      CODE     ---       #461
      00FF390EH   LINE      CODE     ---       #462
      00FF3915H   LINE      CODE     ---       #464
      00FF391CH   LINE      CODE     ---       #465
      00FF3922H   LINE      CODE     ---       #466
      00FF3928H   LINE      CODE     ---       #467
      00FF392EH   LINE      CODE     ---       #468
      00FF3934H   LINE      CODE     ---       #469
      00FF393AH   LINE      CODE     ---       #471
      00FF3940H   LINE      CODE     ---       #472
      00FF3946H   LINE      CODE     ---       #473
      00FF394CH   LINE      CODE     ---       #474
      00FF3952H   LINE      CODE     ---       #475
      00FF3958H   LINE      CODE     ---       #476
      00FF395EH   LINE      CODE     ---       #477
      00FF3964H   LINE      CODE     ---       #478
      00FF396AH   LINE      CODE     ---       #480
      00FF3970H   LINE      CODE     ---       #481
      00FF3976H   LINE      CODE     ---       #482
      00FF397CH   LINE      CODE     ---       #483
      00FF3982H   LINE      CODE     ---       #484
      00FF3988H   LINE      CODE     ---       #485
      00FF398EH   LINE      CODE     ---       #486
      00FF3994H   LINE      CODE     ---       #487
      00FF399AH   LINE      CODE     ---       #488
      00FF399CH   LINE      CODE     ---       #489
      00FF399CH   LINE      CODE     ---       #490
      00FF399EH   LINE      CODE     ---       #491
      ---         BLOCKEND  ---      ---       LVL=0
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 77



      00FF3BC9H   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      IO
      00000021H.6 SYMBOL    BIT      BIT       status
      00FF3BC9H   LINE      CODE     ---       #492
      00FF3BC9H   LINE      CODE     ---       #494
      00FF3CC3H   LINE      CODE     ---       #496
      00FF3CC8H   LINE      CODE     ---       #497
      00FF3CCDH   LINE      CODE     ---       #498
      00FF3CD2H   LINE      CODE     ---       #499
      00FF3CD7H   LINE      CODE     ---       #500
      00FF3CDCH   LINE      CODE     ---       #501
      00FF3CE1H   LINE      CODE     ---       #502
      00FF3CE6H   LINE      CODE     ---       #503
      00FF3CEBH   LINE      CODE     ---       #505
      00FF3CF0H   LINE      CODE     ---       #506
      00FF3CF5H   LINE      CODE     ---       #507
      00FF3CFAH   LINE      CODE     ---       #508
      00FF3CFFH   LINE      CODE     ---       #509
      00FF3D04H   LINE      CODE     ---       #510
      00FF3D09H   LINE      CODE     ---       #511
      00FF3D0EH   LINE      CODE     ---       #512
      00FF3D13H   LINE      CODE     ---       #514
      00FF3D18H   LINE      CODE     ---       #515
      00FF3D1DH   LINE      CODE     ---       #516
      00FF3D22H   LINE      CODE     ---       #517
      00FF3D27H   LINE      CODE     ---       #518
      00FF3D2CH   LINE      CODE     ---       #519
      00FF3D31H   LINE      CODE     ---       #520
      00FF3D36H   LINE      CODE     ---       #521
      00FF3D3BH   LINE      CODE     ---       #523
      00FF3D40H   LINE      CODE     ---       #524
      00FF3D45H   LINE      CODE     ---       #525
      00FF3D4AH   LINE      CODE     ---       #526
      00FF3D4FH   LINE      CODE     ---       #527
      00FF3D54H   LINE      CODE     ---       #528
      00FF3D59H   LINE      CODE     ---       #529
      00FF3D5EH   LINE      CODE     ---       #530
      00FF3D63H   LINE      CODE     ---       #532
      00FF3D68H   LINE      CODE     ---       #533
      00FF3D6DH   LINE      CODE     ---       #534
      00FF3D72H   LINE      CODE     ---       #535
      00FF3D77H   LINE      CODE     ---       #536
      00FF3D7CH   LINE      CODE     ---       #538
      00FF3D81H   LINE      CODE     ---       #539
      00FF3D86H   LINE      CODE     ---       #540
      00FF3D8BH   LINE      CODE     ---       #541
      00FF3D90H   LINE      CODE     ---       #542
      00FF3D95H   LINE      CODE     ---       #543
      00FF3D9AH   LINE      CODE     ---       #545
      00FF3D9FH   LINE      CODE     ---       #546
      00FF3DA4H   LINE      CODE     ---       #547
      00FF3DA9H   LINE      CODE     ---       #548
      00FF3DAEH   LINE      CODE     ---       #549
      00FF3DB3H   LINE      CODE     ---       #550
      00FF3DB8H   LINE      CODE     ---       #551
      00FF3DBDH   LINE      CODE     ---       #552
      00FF3DC2H   LINE      CODE     ---       #554
      00FF3DC7H   LINE      CODE     ---       #555
      00FF3DCCH   LINE      CODE     ---       #556
      00FF3DD1H   LINE      CODE     ---       #557
      00FF3DD6H   LINE      CODE     ---       #558
      00FF3DDBH   LINE      CODE     ---       #559
      00FF3DE0H   LINE      CODE     ---       #560
      00FF3DE5H   LINE      CODE     ---       #561
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 78


      00FF3DE9H   LINE      CODE     ---       #562
      00FF3DE9H   LINE      CODE     ---       #563
      00FF3DE9H   LINE      CODE     ---       #564
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       IIC
      00FF7D10H   PUBLIC    CODE     ---       ESD_IIC_Send_nack
      00FF68E2H   PUBLIC    CODE     ---       ESD_Write_IIC
      00FF7A09H   PUBLIC    CODE     ---       ESD_IIC_Recv_data
      00FF7F19H   PUBLIC    CODE     ---       ESD_IIC_Start
      00FF7DE5H   PUBLIC    CODE     ---       ESD_IIC_WRITE_START_BYTE
      00FF596EH   PUBLIC    CODE     ---       ESD_Init_IIC
      00FF7DFCH   PUBLIC    CODE     ---       ESD_IIC_Wait
      00FF7D71H   PUBLIC    CODE     ---       ESD_IIC_Send_ack
      00FF7F29H   PUBLIC    CODE     ---       ESD_IIC_Recv_ack
      00FF7F39H   PUBLIC    CODE     ---       ESD_IIC_Stop
      00FF7A25H   PUBLIC    CODE     ---       ESD_IIC_READ_NACK_BYTE
      00FF7A41H   PUBLIC    CODE     ---       ESD_IIC_READ_ACK_BYTE
      00FF7E13H   PUBLIC    CODE     ---       ESD_IIC_WRITE_ONE_BYTE
      00FF6221H   PUBLIC    CODE     ---       ESD_Read_IIC
      00FF7E2AH   PUBLIC    CODE     ---       ESD_IIC_Send_data
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E1H   SFRSYM    DATA     BYTE      P7M1
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000E2H   SFRSYM    DATA     BYTE      P7M0
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 79


      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B1H   SFRSYM    DATA     BYTE      P3M1
      00000090H   SFRSYM    DATA     BYTE      P1
      00000095H   SFRSYM    DATA     BYTE      P2M1
      000000B2H   SFRSYM    DATA     BYTE      P3M0
      00000080H   SFRSYM    DATA     BYTE      P0
      00000091H   SFRSYM    DATA     BYTE      P1M1
      00000096H   SFRSYM    DATA     BYTE      P2M0
      00000092H   SFRSYM    DATA     BYTE      P1M0

      00FF7DFCH   BLOCK     CODE     ---       LVL=0
      00FF7DFCH   LINE      CODE     ---       #9
      00FF7DFCH   LINE      CODE     ---       #11
      00FF7E0AH   LINE      CODE     ---       #12
      00FF7E12H   LINE      CODE     ---       #13
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7F19H   BLOCK     CODE     ---       LVL=0
      00FF7F19H   LINE      CODE     ---       #18
      00FF7F19H   LINE      CODE     ---       #20
      00FF7F26H   LINE      CODE     ---       #21
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7E2AH   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      dat
      00FF7E2AH   LINE      CODE     ---       #26
      00FF7E2AH   LINE      CODE     ---       #28
      00FF7E35H   LINE      CODE     ---       #29
      00FF7E3EH   LINE      CODE     ---       #30
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7F29H   BLOCK     CODE     ---       LVL=0
      00FF7F29H   LINE      CODE     ---       #35
      00FF7F29H   LINE      CODE     ---       #37
      00FF7F36H   LINE      CODE     ---       #38
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7A09H   BLOCK     CODE     ---       LVL=0
      00FF7A09H   LINE      CODE     ---       #43
      00FF7A09H   LINE      CODE     ---       #45
      00FF7A16H   LINE      CODE     ---       #46
      00FF7A19H   LINE      CODE     ---       #47
      00FF7A24H   LINE      CODE     ---       #48
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7D71H   BLOCK     CODE     ---       LVL=0
      00FF7D71H   LINE      CODE     ---       #53
      00FF7D71H   LINE      CODE     ---       #55
      00FF7D7DH   LINE      CODE     ---       #56
      00FF7D86H   LINE      CODE     ---       #57
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7D10H   BLOCK     CODE     ---       LVL=0
      00FF7D10H   LINE      CODE     ---       #62
      00FF7D10H   LINE      CODE     ---       #64
      00FF7D1DH   LINE      CODE     ---       #65
      00FF7D26H   LINE      CODE     ---       #66
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7F39H   BLOCK     CODE     ---       LVL=0
      00FF7F39H   LINE      CODE     ---       #72
      00FF7F39H   LINE      CODE     ---       #74
      00FF7F46H   LINE      CODE     ---       #75
      ---         BLOCKEND  ---      ---       LVL=0

L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 80


      00FF7DE5H   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      dat
      00FF7DE5H   LINE      CODE     ---       #81
      00FF7DE5H   LINE      CODE     ---       #83
      00FF7DF0H   LINE      CODE     ---       #84
      00FF7DF9H   LINE      CODE     ---       #85
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7E13H   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      dat
      00FF7E13H   LINE      CODE     ---       #91
      00FF7E13H   LINE      CODE     ---       #93
      00FF7E1EH   LINE      CODE     ---       #94
      00FF7E27H   LINE      CODE     ---       #95
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7A41H   BLOCK     CODE     ---       LVL=0
      00FF7A41H   LINE      CODE     ---       #101
      00FF7A41H   LINE      CODE     ---       #103
      00FF7A4EH   LINE      CODE     ---       #104
      00FF7A51H   LINE      CODE     ---       #105
      00FF7A5CH   LINE      CODE     ---       #106
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7A25H   BLOCK     CODE     ---       LVL=0
      00FF7A25H   LINE      CODE     ---       #111
      00FF7A25H   LINE      CODE     ---       #113
      00FF7A32H   LINE      CODE     ---       #114
      00FF7A35H   LINE      CODE     ---       #115
      00FF7A40H   LINE      CODE     ---       #116
      ---         BLOCKEND  ---      ---       LVL=0

      00FF68E2H   BLOCK     CODE     ---       LVL=0
      000002EDH   SYMBOL    EDATA    BYTE      dev
      000002EEH   SYMBOL    EDATA    BYTE      reg
      000002EFH   SYMBOL    EDATA    BYTE      length
      REG=3       REGSYM    ---      ---       dat
      00FF68F2H   BLOCK     CODE     NEAR LAB  LVL=1
      000002F0H   SYMBOL    EDATA    BYTE      count
      ---         BLOCKEND  ---      ---       LVL=1
      00FF68E2H   LINE      CODE     ---       #128
      00FF68F2H   LINE      CODE     ---       #129
      00FF68F2H   LINE      CODE     ---       #130
      00FF68F2H   LINE      CODE     ---       #132
      00FF68F5H   LINE      CODE     ---       #133
      00FF68FEH   LINE      CODE     ---       #134
      00FF6901H   LINE      CODE     ---       #135
      00FF6908H   LINE      CODE     ---       #136
      00FF690BH   LINE      CODE     ---       #137
      00FF690EH   LINE      CODE     ---       #139
      00FF691EH   LINE      CODE     ---       #140
      00FF6921H   LINE      CODE     ---       #141
      00FF6934H   LINE      CODE     ---       #142
      00FF6937H   LINE      CODE     ---       #143
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6221H   BLOCK     CODE     ---       LVL=0
      000002F1H   SYMBOL    EDATA    BYTE      dev
      000002F2H   SYMBOL    EDATA    BYTE      reg
      000002F3H   SYMBOL    EDATA    BYTE      length
      REG=3       REGSYM    ---      ---       dat
      00FF6231H   BLOCK     CODE     NEAR LAB  LVL=1
      000002F4H   SYMBOL    EDATA    BYTE      count
      ---         BLOCKEND  ---      ---       LVL=1
      00FF6221H   LINE      CODE     ---       #153
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 81


      00FF6231H   LINE      CODE     ---       #154
      00FF6231H   LINE      CODE     ---       #155
      00FF6231H   LINE      CODE     ---       #157
      00FF6234H   LINE      CODE     ---       #158
      00FF623DH   LINE      CODE     ---       #159
      00FF6240H   LINE      CODE     ---       #160
      00FF6247H   LINE      CODE     ---       #161
      00FF624AH   LINE      CODE     ---       #163
      00FF624DH   LINE      CODE     ---       #164
      00FF625AH   LINE      CODE     ---       #165
      00FF625DH   LINE      CODE     ---       #167
      00FF6260H   LINE      CODE     ---       #169
      00FF6270H   LINE      CODE     ---       #170
      00FF6287H   LINE      CODE     ---       #171
      00FF628AH   LINE      CODE     ---       #173
      00FF629DH   LINE      CODE     ---       #174
      00FF62A0H   LINE      CODE     ---       #176
      ---         BLOCKEND  ---      ---       LVL=0

      00FF596EH   BLOCK     CODE     ---       LVL=0
      WR6         REGSYM    ---      INT       iic_n
      00FF596EH   LINE      CODE     ---       #180
      00FF596EH   LINE      CODE     ---       #183
      00FF5980H   LINE      CODE     ---       #185
      00FF5980H   LINE      CODE     ---       #188
      00FF5983H   LINE      CODE     ---       #189
      00FF5993H   LINE      CODE     ---       #190
      00FF5996H   LINE      CODE     ---       #191
      00FF5999H   LINE      CODE     ---       #193
      00FF599BH   LINE      CODE     ---       #194
      00FF599BH   LINE      CODE     ---       #197
      00FF599EH   LINE      CODE     ---       #198
      00FF59A1H   LINE      CODE     ---       #199
      00FF59B1H   LINE      CODE     ---       #200
      00FF59B4H   LINE      CODE     ---       #201
      00FF59B7H   LINE      CODE     ---       #202
      00FF59B9H   LINE      CODE     ---       #203
      00FF59B9H   LINE      CODE     ---       #206
      00FF59BCH   LINE      CODE     ---       #207
      00FF59BFH   LINE      CODE     ---       #208
      00FF59CFH   LINE      CODE     ---       #209
      00FF59D2H   LINE      CODE     ---       #210
      00FF59D5H   LINE      CODE     ---       #211
      00FF59D7H   LINE      CODE     ---       #212
      00FF59D7H   LINE      CODE     ---       #215
      00FF59DAH   LINE      CODE     ---       #216
      00FF59EAH   LINE      CODE     ---       #217
      00FF59EDH   LINE      CODE     ---       #218
      00FF59F0H   LINE      CODE     ---       #219
      00FF59F0H   LINE      CODE     ---       #220
      00FF59F0H   LINE      CODE     ---       #222
      00FF59FDH   LINE      CODE     ---       #223
      00FF5A05H   LINE      CODE     ---       #224
      00FF5A0DH   LINE      CODE     ---       #225
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       INT
      00FF733FH   PUBLIC    CODE     ---       INT_init
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 82


      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.0 SFRSYM    DATA     BIT       IT0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF733FH   BLOCK     CODE     ---       LVL=0
      R6          REGSYM    ---      BYTE      int_n
      R7          REGSYM    ---      BYTE      mode
      00FF733FH   LINE      CODE     ---       #2
      00FF7341H   LINE      CODE     ---       #4
      00FF7345H   LINE      CODE     ---       #6
      00FF734EH   LINE      CODE     ---       #7
      00FF7350H   LINE      CODE     ---       #8
      00FF7350H   LINE      CODE     ---       #9
      00FF7354H   LINE      CODE     ---       #11
      00FF7359H   LINE      CODE     ---       #12
      00FF735BH   LINE      CODE     ---       #13
      00FF735BH   LINE      CODE     ---       #14
      00FF735FH   LINE      CODE     ---       #16
      00FF7362H   LINE      CODE     ---       #17
      00FF7362H   LINE      CODE     ---       #18
      00FF7366H   LINE      CODE     ---       #20
      00FF7369H   LINE      CODE     ---       #21
      00FF7369H   LINE      CODE     ---       #22
      00FF736DH   LINE      CODE     ---       #24
      00FF7370H   LINE      CODE     ---       #25
      ---         BLOCKEND  ---      ---       LVL=0
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 83



      ---         MODULE    ---      ---       PIT
      00FF6C18H   PUBLIC    CODE     ---       PIT_count_get
      00FF6004H   PUBLIC    CODE     ---       PIT_init_ms
      00FF6C65H   PUBLIC    CODE     ---       PIT_init_encoder
      00FF5F71H   PUBLIC    CODE     ---       PIT_init_us
      00FF6A37H   PUBLIC    CODE     ---       PIT_count_clean
      00000278H   PUBLIC    EDATA    WORD      T0_cnt
      0000027AH   PUBLIC    EDATA    WORD      T1_cnt
      0000027CH   PUBLIC    EDATA    WORD      T2_cnt
      0000027EH   PUBLIC    EDATA    WORD      T3_cnt
      00000280H   PUBLIC    EDATA    WORD      T4_cnt
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      00000088H.6 SFRSYM    DATA     BIT       TR1
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D3H   SFRSYM    DATA     BYTE      T4L
      000000D5H   SFRSYM    DATA     BYTE      T3L
      000000D7H   SFRSYM    DATA     BYTE      T2L
      0000008BH   SFRSYM    DATA     BYTE      TL1
      0000008AH   SFRSYM    DATA     BYTE      TL0
      000000D2H   SFRSYM    DATA     BYTE      T4H
      000000D4H   SFRSYM    DATA     BYTE      T3H
      000000D6H   SFRSYM    DATA     BYTE      T2H
      0000008DH   SFRSYM    DATA     BYTE      TH1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 84


      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF6004H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      tim_n
      WR2         REGSYM    ---      WORD      time_ms
      00FF600AH   BLOCK     CODE     NEAR LAB  LVL=1
      WR6         REGSYM    ---      WORD      temp
      ---         BLOCKEND  ---      ---       LVL=1
      00FF6004H   LINE      CODE     ---       #10
      00FF600AH   LINE      CODE     ---       #11
      00FF600AH   LINE      CODE     ---       #13
      00FF6023H   LINE      CODE     ---       #14
      00FF6027H   LINE      CODE     ---       #16
      00FF602BH   LINE      CODE     ---       #17
      00FF602FH   LINE      CODE     ---       #18
      00FF6033H   LINE      CODE     ---       #19
      00FF6035H   LINE      CODE     ---       #20
      00FF6037H   LINE      CODE     ---       #21
      00FF6039H   LINE      CODE     ---       #22
      00FF603EH   LINE      CODE     ---       #24
      00FF6042H   LINE      CODE     ---       #25
      00FF6046H   LINE      CODE     ---       #26
      00FF604AH   LINE      CODE     ---       #27
      00FF604CH   LINE      CODE     ---       #28
      00FF604EH   LINE      CODE     ---       #29
      00FF6050H   LINE      CODE     ---       #30
      00FF6055H   LINE      CODE     ---       #32
      00FF6059H   LINE      CODE     ---       #33
      00FF605DH   LINE      CODE     ---       #34
      00FF6060H   LINE      CODE     ---       #35
      00FF6063H   LINE      CODE     ---       #36
      00FF6065H   LINE      CODE     ---       #37
      00FF606AH   LINE      CODE     ---       #39
      00FF606EH   LINE      CODE     ---       #40
      00FF6072H   LINE      CODE     ---       #41
      00FF6075H   LINE      CODE     ---       #42
      00FF6078H   LINE      CODE     ---       #43
      00FF607AH   LINE      CODE     ---       #44
      00FF607FH   LINE      CODE     ---       #46
      00FF6083H   LINE      CODE     ---       #47
      00FF6087H   LINE      CODE     ---       #48
      00FF608AH   LINE      CODE     ---       #49
      00FF608DH   LINE      CODE     ---       #50
      ---         BLOCKEND  ---      ---       LVL=0

      00FF5F71H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      tim_n
      WR30        REGSYM    ---      WORD      time_us
      00FF5F77H   BLOCK     CODE     NEAR LAB  LVL=1
      WR6         REGSYM    ---      WORD      temp
      ---         BLOCKEND  ---      ---       LVL=1
      00FF5F71H   LINE      CODE     ---       #52
      00FF5F77H   LINE      CODE     ---       #53
      00FF5F77H   LINE      CODE     ---       #55
      00FF5F97H   LINE      CODE     ---       #56
      00FF5F9BH   LINE      CODE     ---       #58
      00FF5F9FH   LINE      CODE     ---       #59
      00FF5FA3H   LINE      CODE     ---       #60
      00FF5FA7H   LINE      CODE     ---       #61
      00FF5FA9H   LINE      CODE     ---       #62
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 85


      00FF5FABH   LINE      CODE     ---       #63
      00FF5FADH   LINE      CODE     ---       #64
      00FF5FB2H   LINE      CODE     ---       #66
      00FF5FB6H   LINE      CODE     ---       #67
      00FF5FBAH   LINE      CODE     ---       #68
      00FF5FBEH   LINE      CODE     ---       #69
      00FF5FC0H   LINE      CODE     ---       #70
      00FF5FC2H   LINE      CODE     ---       #71
      00FF5FC4H   LINE      CODE     ---       #72
      00FF5FC9H   LINE      CODE     ---       #74
      00FF5FCDH   LINE      CODE     ---       #75
      00FF5FD1H   LINE      CODE     ---       #76
      00FF5FD4H   LINE      CODE     ---       #77
      00FF5FD7H   LINE      CODE     ---       #78
      00FF5FD9H   LINE      CODE     ---       #79
      00FF5FDEH   LINE      CODE     ---       #81
      00FF5FE2H   LINE      CODE     ---       #82
      00FF5FE6H   LINE      CODE     ---       #83
      00FF5FE9H   LINE      CODE     ---       #84
      00FF5FECH   LINE      CODE     ---       #85
      00FF5FEEH   LINE      CODE     ---       #86
      00FF5FF3H   LINE      CODE     ---       #88
      00FF5FF7H   LINE      CODE     ---       #89
      00FF5FFBH   LINE      CODE     ---       #90
      00FF5FFEH   LINE      CODE     ---       #91
      00FF6001H   LINE      CODE     ---       #92
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6C65H   BLOCK     CODE     ---       LVL=0
      R7          REGSYM    ---      BYTE      tim_n
      00FF6C65H   LINE      CODE     ---       #94
      00FF6C67H   LINE      CODE     ---       #96
      00FF6C7BH   LINE      CODE     ---       #98
      00FF6C7BH   LINE      CODE     ---       #100
      00FF6C7EH   LINE      CODE     ---       #101
      00FF6C81H   LINE      CODE     ---       #102
      00FF6C84H   LINE      CODE     ---       #103
      00FF6C86H   LINE      CODE     ---       #104
      00FF6C87H   LINE      CODE     ---       #106
      00FF6C87H   LINE      CODE     ---       #108
      00FF6C8AH   LINE      CODE     ---       #109
      00FF6C8DH   LINE      CODE     ---       #110
      00FF6C90H   LINE      CODE     ---       #111
      00FF6C92H   LINE      CODE     ---       #112
      00FF6C93H   LINE      CODE     ---       #114
      00FF6C93H   LINE      CODE     ---       #116
      00FF6C96H   LINE      CODE     ---       #117
      00FF6C99H   LINE      CODE     ---       #118
      00FF6C9CH   LINE      CODE     ---       #119
      00FF6C9DH   LINE      CODE     ---       #121
      00FF6C9DH   LINE      CODE     ---       #123
      00FF6CA0H   LINE      CODE     ---       #124
      00FF6CA3H   LINE      CODE     ---       #125
      00FF6CA6H   LINE      CODE     ---       #126
      00FF6CA7H   LINE      CODE     ---       #128
      00FF6CA7H   LINE      CODE     ---       #130
      00FF6CAAH   LINE      CODE     ---       #131
      00FF6CADH   LINE      CODE     ---       #132
      00FF6CB0H   LINE      CODE     ---       #133
      00FF6CB0H   LINE      CODE     ---       #135
      00FF6CB0H   LINE      CODE     ---       #136
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6C18H   BLOCK     CODE     ---       LVL=0
      R5          REGSYM    ---      BYTE      tim_n
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 86


      00FF6C1AH   BLOCK     CODE     NEAR LAB  LVL=1
      WR6         REGSYM    ---      WORD      dat
      ---         BLOCKEND  ---      ---       LVL=1
      00FF6C18H   LINE      CODE     ---       #137
      00FF6C1AH   LINE      CODE     ---       #138
      00FF6C1AH   LINE      CODE     ---       #139
      00FF6C1CH   LINE      CODE     ---       #140
      00FF6C30H   LINE      CODE     ---       #142
      00FF6C30H   LINE      CODE     ---       #144
      00FF6C36H   LINE      CODE     ---       #145
      00FF6C38H   LINE      CODE     ---       #146
      00FF6C3AH   LINE      CODE     ---       #148
      00FF6C3AH   LINE      CODE     ---       #150
      00FF6C40H   LINE      CODE     ---       #151
      00FF6C42H   LINE      CODE     ---       #152
      00FF6C44H   LINE      CODE     ---       #154
      00FF6C44H   LINE      CODE     ---       #156
      00FF6C4AH   LINE      CODE     ---       #157
      00FF6C4CH   LINE      CODE     ---       #158
      00FF6C4EH   LINE      CODE     ---       #160
      00FF6C4EH   LINE      CODE     ---       #162
      00FF6C54H   LINE      CODE     ---       #163
      00FF6C56H   LINE      CODE     ---       #164
      00FF6C58H   LINE      CODE     ---       #166
      00FF6C58H   LINE      CODE     ---       #168
      00FF6C5EH   LINE      CODE     ---       #169
      00FF6C64H   LINE      CODE     ---       #170
      00FF6C64H   LINE      CODE     ---       #172
      00FF6C64H   LINE      CODE     ---       #173
      00FF6C64H   LINE      CODE     ---       #174
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6A37H   BLOCK     CODE     ---       LVL=0
      R7          REGSYM    ---      BYTE      tim_n
      00FF6A37H   LINE      CODE     ---       #175
      00FF6A39H   LINE      CODE     ---       #177
      00FF6A4DH   LINE      CODE     ---       #179
      00FF6A4DH   LINE      CODE     ---       #181
      00FF6A4FH   LINE      CODE     ---       #182
      00FF6A52H   LINE      CODE     ---       #183
      00FF6A55H   LINE      CODE     ---       #184
      00FF6A57H   LINE      CODE     ---       #185
      00FF6A58H   LINE      CODE     ---       #187
      00FF6A58H   LINE      CODE     ---       #189
      00FF6A5AH   LINE      CODE     ---       #190
      00FF6A5DH   LINE      CODE     ---       #191
      00FF6A60H   LINE      CODE     ---       #192
      00FF6A62H   LINE      CODE     ---       #193
      00FF6A63H   LINE      CODE     ---       #195
      00FF6A63H   LINE      CODE     ---       #197
      00FF6A66H   LINE      CODE     ---       #198
      00FF6A69H   LINE      CODE     ---       #199
      00FF6A6CH   LINE      CODE     ---       #200
      00FF6A6FH   LINE      CODE     ---       #201
      00FF6A70H   LINE      CODE     ---       #203
      00FF6A70H   LINE      CODE     ---       #205
      00FF6A73H   LINE      CODE     ---       #206
      00FF6A76H   LINE      CODE     ---       #207
      00FF6A79H   LINE      CODE     ---       #208
      00FF6A7CH   LINE      CODE     ---       #209
      00FF6A7DH   LINE      CODE     ---       #211
      00FF6A7DH   LINE      CODE     ---       #213
      00FF6A80H   LINE      CODE     ---       #214
      00FF6A83H   LINE      CODE     ---       #215
      00FF6A86H   LINE      CODE     ---       #216
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 87


      00FF6A89H   LINE      CODE     ---       #217
      00FF6A89H   LINE      CODE     ---       #219
      00FF6A89H   LINE      CODE     ---       #220
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       PWM
      00FF30BEH   PUBLIC    CODE     ---       PWM_init
      00FF399FH   PUBLIC    CODE     ---       PWM_change
      00FF8202H   PUBLIC    HCONST   ---       PWM_CCR_ADDR
      00FF8222H   PUBLIC    HCONST   ---       PWM_ARR_ADDR
      00FF822AH   PUBLIC    HCONST   ---       PWM_CCER_ADDR
      00FF823AH   PUBLIC    HCONST   ---       PWM_CCMR_ADDR
      0000026AH   PUBLIC    EDATA    BYTE      ?PWM_init?BYTE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF30BEH   BLOCK     CODE     ---       LVL=0
      WR30        REGSYM    ---      INT       pwmch
      DR24        REGSYM    ---      DWORD     freq
      00000270H   SYMBOL    EDATA    DWORD     duty
      00FF30C0H   BLOCK     CODE     NEAR LAB  LVL=1
      DR20        REGSYM    ---      DWORD     match_temp
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 88


      DR24        REGSYM    ---      DWORD     period_temp
      WR28        REGSYM    ---      WORD      freq_div
      ---         BLOCKEND  ---      ---       LVL=1
      00FF30BEH   LINE      CODE     ---       #14
      00FF30C0H   LINE      CODE     ---       #15
      00FF30C0H   LINE      CODE     ---       #19
      00FF30C0H   LINE      CODE     ---       #29
      00FF30CDH   LINE      CODE     ---       #30
      00FF30CDH   LINE      CODE     ---       #31
      00FF30D6H   LINE      CODE     ---       #33
      00FF30E0H   LINE      CODE     ---       #35
      00FF30FFH   LINE      CODE     ---       #36
      00FF3101H   LINE      CODE     ---       #39
      00FF3105H   LINE      CODE     ---       #40
      00FF3105H   LINE      CODE     ---       #43
      00FF310EH   LINE      CODE     ---       #46
      00FF3139H   LINE      CODE     ---       #47
      00FF315AH   LINE      CODE     ---       #50
      00FF31A6H   LINE      CODE     ---       #53
      00FF31B5H   LINE      CODE     ---       #54
      00FF31C0H   LINE      CODE     ---       #56
      00FF31C9H   LINE      CODE     ---       #57
      00FF31CFH   LINE      CODE     ---       #58
      00FF31D2H   LINE      CODE     ---       #61
      00FF3200H   LINE      CODE     ---       #62
      00FF3222H   LINE      CODE     ---       #63
      00FF3245H   LINE      CODE     ---       #66
      00FF3291H   LINE      CODE     ---       #67
      00FF32E7H   LINE      CODE     ---       #71
      00FF32F6H   LINE      CODE     ---       #72
      00FF3301H   LINE      CODE     ---       #74
      00FF330AH   LINE      CODE     ---       #75
      00FF3317H   LINE      CODE     ---       #76
      00FF3317H   LINE      CODE     ---       #79
      00FF3343H   LINE      CODE     ---       #80
      00FF3371H   LINE      CODE     ---       #83
      00FF3399H   LINE      CODE     ---       #84
      00FF33C7H   LINE      CODE     ---       #87
      00FF33DEH   LINE      CODE     ---       #88
      00FF3409H   LINE      CODE     ---       #91
      ---         BLOCKEND  ---      ---       LVL=0

      00FF399FH   BLOCK     CODE     ---       LVL=0
      WR30        REGSYM    ---      INT       pwmch
      DR24        REGSYM    ---      DWORD     duty
      00FF39A3H   BLOCK     CODE     NEAR LAB  LVL=1
      DR20        REGSYM    ---      DWORD     match_temp
      DR16        REGSYM    ---      DWORD     arr
      ---         BLOCKEND  ---      ---       LVL=1
      00FF399FH   LINE      CODE     ---       #92
      00FF39A3H   LINE      CODE     ---       #93
      00FF39A3H   LINE      CODE     ---       #95
      00FF39E7H   LINE      CODE     ---       #98
      00FF39EDH   LINE      CODE     ---       #101
      00FF3A18H   LINE      CODE     ---       #102
      00FF3A31H   LINE      CODE     ---       #104
      00FF3A34H   LINE      CODE     ---       #107
      00FF3A62H   LINE      CODE     ---       #108
      00FF3A84H   LINE      CODE     ---       #109
      00FF3AA7H   LINE      CODE     ---       #112
      00FF3AF3H   LINE      CODE     ---       #113
      00FF3B49H   LINE      CODE     ---       #115
      00FF3B49H   LINE      CODE     ---       #117
      00FF3B4FH   LINE      CODE     ---       #119
      00FF3B70H   LINE      CODE     ---       #120
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 89


      00FF3B72H   LINE      CODE     ---       #123
      00FF3B76H   LINE      CODE     ---       #124
      00FF3B76H   LINE      CODE     ---       #126
      00FF3B9EH   LINE      CODE     ---       #127
      00FF3BC8H   LINE      CODE     ---       #131
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       UART
      00FF1793H   PUBLIC    CODE     ---       UART_Send_float
      00FF7907H   PUBLIC    CODE     ---       UART_Send_string
      00FF6A8AH   PUBLIC    CODE     ---       UART_Send_byte
      00FF44CAH   PUBLIC    CODE     ---       UART_init
      00FF1FB0H   PUBLIC    CODE     ---       UART_Send_int
      000002F5H   PUBLIC    EDATA    BYTE      UART1_OK
      000002F6H   PUBLIC    EDATA    BYTE      UART2_OK
      000002F7H   PUBLIC    EDATA    BYTE      UART3_OK
      000002F8H   PUBLIC    EDATA    BYTE      UART4_OK
      0000025FH   PUBLIC    EDATA    BYTE      ?UART_Send_int?BYTE
      0000024FH   PUBLIC    EDATA    BYTE      ?UART_Send_float?BYTE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000FEH   SFRSYM    DATA     BYTE      S4BUF
      000000ADH   SFRSYM    DATA     BYTE      S3BUF
      000000DDH.5 SFRSYM    DATA     BIT       T4x12
      0000009BH   SFRSYM    DATA     BYTE      S2BUF
      000000DDH.1 SFRSYM    DATA     BIT       T3x12
      0000008EH.2 SFRSYM    DATA     BIT       T2x12
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000DDH.7 SFRSYM    DATA     BIT       T4R
      000000DDH.3 SFRSYM    DATA     BIT       T3R
      0000008EH.4 SFRSYM    DATA     BIT       T2R
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000D3H   SFRSYM    DATA     BYTE      T4L
      000000D5H   SFRSYM    DATA     BYTE      T3L
      000000D7H   SFRSYM    DATA     BYTE      T2L
      0000008BH   SFRSYM    DATA     BYTE      TL1
      000000D2H   SFRSYM    DATA     BYTE      T4H
      000000D4H   SFRSYM    DATA     BYTE      T3H
      000000D6H   SFRSYM    DATA     BYTE      T2H
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000AFH.4 SFRSYM    DATA     BIT       ES4
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000AFH.3 SFRSYM    DATA     BIT       ES3
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000AFH.0 SFRSYM    DATA     BIT       ES2
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      00000099H   SFRSYM    DATA     BYTE      SBUF
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 90


      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000A8H.4 SFRSYM    DATA     BIT       ES
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF44CAH   BLOCK     CODE     ---       LVL=0
      R13         REGSYM    ---      BYTE      pin
      DR28        REGSYM    ---      DWORD     btl
      R14         REGSYM    ---      BYTE      n
      R15         REGSYM    ---      BYTE      isr
      00FF44CAH   LINE      CODE     ---       #8
      00FF44D6H   LINE      CODE     ---       #10
      00FF44DBH   LINE      CODE     ---       #12
      00FF44DEH   LINE      CODE     ---       #13
      00FF44EEH   LINE      CODE     ---       #15
      00FF44F4H   LINE      CODE     ---       #16
      00FF44F9H   LINE      CODE     ---       #17
      00FF44FEH   LINE      CODE     ---       #18
      00FF4501H   LINE      CODE     ---       #19
      00FF4501H   LINE      CODE     ---       #20
      00FF4504H   LINE      CODE     ---       #21
      00FF4507H   LINE      CODE     ---       #22
      00FF450BH   LINE      CODE     ---       #23
      00FF4530H   LINE      CODE     ---       #24
      00FF4534H   LINE      CODE     ---       #25
      00FF4536H   LINE      CODE     ---       #26
      00FF4539H   LINE      CODE     ---       #27
      00FF453EH   LINE      CODE     ---       #28
      00FF4540H   LINE      CODE     ---       #30
      00FF4545H   LINE      CODE     ---       #33
      00FF454FH   LINE      CODE     ---       #34
      00FF4552H   LINE      CODE     ---       #35
      00FF4555H   LINE      CODE     ---       #36
      00FF4562H   LINE      CODE     ---       #37
      00FF4587H   LINE      CODE     ---       #38
      00FF458BH   LINE      CODE     ---       #39
      00FF458EH   LINE      CODE     ---       #40
      00FF4591H   LINE      CODE     ---       #41
      00FF4596H   LINE      CODE     ---       #42
      00FF4599H   LINE      CODE     ---       #44
      00FF459EH   LINE      CODE     ---       #46
      00FF45A8H   LINE      CODE     ---       #47
      00FF45ABH   LINE      CODE     ---       #48
      00FF45AEH   LINE      CODE     ---       #49
      00FF45D3H   LINE      CODE     ---       #50
      00FF45D7H   LINE      CODE     ---       #51
      00FF45DAH   LINE      CODE     ---       #52
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 91


      00FF45DDH   LINE      CODE     ---       #53
      00FF45E2H   LINE      CODE     ---       #54
      00FF45E5H   LINE      CODE     ---       #56
      00FF45EAH   LINE      CODE     ---       #58
      00FF45F4H   LINE      CODE     ---       #59
      00FF45F7H   LINE      CODE     ---       #60
      00FF45FAH   LINE      CODE     ---       #61
      00FF461FH   LINE      CODE     ---       #62
      00FF4623H   LINE      CODE     ---       #63
      00FF4626H   LINE      CODE     ---       #64
      00FF4629H   LINE      CODE     ---       #65
      00FF462EH   LINE      CODE     ---       #66
      00FF4631H   LINE      CODE     ---       #69
      ---         BLOCKEND  ---      ---       LVL=0

      00FF6A8AH   BLOCK     CODE     ---       LVL=0
      R7          REGSYM    ---      BYTE      pin
      R10         REGSYM    ---      BYTE      c
      00FF6A8AH   LINE      CODE     ---       #70
      00FF6A8EH   LINE      CODE     ---       #72
      00FF6A92H   LINE      CODE     ---       #74
      00FF6A98H   LINE      CODE     ---       #75
      00FF6A9EH   LINE      CODE     ---       #76
      00FF6AA1H   LINE      CODE     ---       #77
      00FF6AA1H   LINE      CODE     ---       #78
      00FF6AA5H   LINE      CODE     ---       #80
      00FF6AABH   LINE      CODE     ---       #81
      00FF6AB1H   LINE      CODE     ---       #82
      00FF6AB4H   LINE      CODE     ---       #83
      00FF6AB4H   LINE      CODE     ---       #84
      00FF6AB8H   LINE      CODE     ---       #86
      00FF6ABEH   LINE      CODE     ---       #87
      00FF6AC4H   LINE      CODE     ---       #88
      00FF6AC7H   LINE      CODE     ---       #89
      00FF6AC7H   LINE      CODE     ---       #90
      00FF6ACBH   LINE      CODE     ---       #92
      00FF6AD1H   LINE      CODE     ---       #93
      00FF6AD7H   LINE      CODE     ---       #94
      00FF6ADAH   LINE      CODE     ---       #95
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7907H   BLOCK     CODE     ---       LVL=0
      00000302H   SYMBOL    EDATA    BYTE      pin
      REG=3       REGSYM    ---      ---       pt
      00FF7907H   LINE      CODE     ---       #97
      00FF790FH   LINE      CODE     ---       #99
      00FF7911H   LINE      CODE     ---       #101
      00FF791DH   LINE      CODE     ---       #102
      00FF7922H   LINE      CODE     ---       #103
      ---         BLOCKEND  ---      ---       LVL=0

      00FF1FB0H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      pin
      00000260H   SYMBOL    EDATA    ---       pa
      00000264H   SYMBOL    EDATA    LONG      num
      00000268H   SYMBOL    EDATA    ---       pb
      00FF1FBCH   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      k
      ---         BLOCKEND  ---      ---       LVL=1
      00FF1FB0H   LINE      CODE     ---       #104
      00FF1FBCH   LINE      CODE     ---       #105
      00FF1FBCH   LINE      CODE     ---       #108
      00FF1FBEH   LINE      CODE     ---       #110
      00FF1FD0H   LINE      CODE     ---       #111
      00FF1FD9H   LINE      CODE     ---       #112
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 92


      00FF1FE9H   LINE      CODE     ---       #115
      00FF1FFBH   LINE      CODE     ---       #116
      00FF2014H   LINE      CODE     ---       #117
      00FF2043H   LINE      CODE     ---       #119
      00FF2043H   LINE      CODE     ---       #120
      00FF2059H   LINE      CODE     ---       #121
      00FF205CH   LINE      CODE     ---       #122
      00FF205CH   LINE      CODE     ---       #123
      00FF207CH   LINE      CODE     ---       #124
      00FF2092H   LINE      CODE     ---       #125
      00FF2095H   LINE      CODE     ---       #126
      00FF2095H   LINE      CODE     ---       #127
      00FF20B5H   LINE      CODE     ---       #128
      00FF20D5H   LINE      CODE     ---       #129
      00FF20EBH   LINE      CODE     ---       #130
      00FF20EEH   LINE      CODE     ---       #131
      00FF20EEH   LINE      CODE     ---       #132
      00FF210EH   LINE      CODE     ---       #133
      00FF212EH   LINE      CODE     ---       #134
      00FF214EH   LINE      CODE     ---       #135
      00FF2164H   LINE      CODE     ---       #136
      00FF2167H   LINE      CODE     ---       #137
      00FF2167H   LINE      CODE     ---       #138
      00FF2187H   LINE      CODE     ---       #139
      00FF21A7H   LINE      CODE     ---       #140
      00FF21C7H   LINE      CODE     ---       #141
      00FF21E7H   LINE      CODE     ---       #142
      00FF21FDH   LINE      CODE     ---       #143
      00FF2200H   LINE      CODE     ---       #144
      00FF2200H   LINE      CODE     ---       #145
      00FF2224H   LINE      CODE     ---       #146
      00FF2244H   LINE      CODE     ---       #147
      00FF2264H   LINE      CODE     ---       #148
      00FF2284H   LINE      CODE     ---       #149
      00FF22A4H   LINE      CODE     ---       #150
      00FF22BAH   LINE      CODE     ---       #151
      00FF22BDH   LINE      CODE     ---       #152
      00FF22BDH   LINE      CODE     ---       #153
      00FF22E1H   LINE      CODE     ---       #154
      00FF2305H   LINE      CODE     ---       #155
      00FF2325H   LINE      CODE     ---       #156
      00FF2345H   LINE      CODE     ---       #157
      00FF2365H   LINE      CODE     ---       #158
      00FF2385H   LINE      CODE     ---       #159
      00FF239BH   LINE      CODE     ---       #160
      00FF239EH   LINE      CODE     ---       #161
      00FF239EH   LINE      CODE     ---       #162
      00FF23C2H   LINE      CODE     ---       #163
      00FF23E6H   LINE      CODE     ---       #164
      00FF240AH   LINE      CODE     ---       #165
      00FF242AH   LINE      CODE     ---       #166
      00FF244AH   LINE      CODE     ---       #167
      00FF246AH   LINE      CODE     ---       #168
      00FF248AH   LINE      CODE     ---       #169
      00FF24A0H   LINE      CODE     ---       #170
      00FF24A3H   LINE      CODE     ---       #171
      00FF24A3H   LINE      CODE     ---       #172
      00FF24C7H   LINE      CODE     ---       #173
      00FF24EBH   LINE      CODE     ---       #174
      00FF250FH   LINE      CODE     ---       #175
      00FF2533H   LINE      CODE     ---       #176
      00FF2553H   LINE      CODE     ---       #177
      00FF2573H   LINE      CODE     ---       #178
      00FF2593H   LINE      CODE     ---       #179
      00FF25B3H   LINE      CODE     ---       #180
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 93


      00FF25C9H   LINE      CODE     ---       #181
      00FF25CCH   LINE      CODE     ---       #182
      00FF25CCH   LINE      CODE     ---       #183
      00FF25F0H   LINE      CODE     ---       #184
      00FF2614H   LINE      CODE     ---       #185
      00FF2638H   LINE      CODE     ---       #186
      00FF265CH   LINE      CODE     ---       #187
      00FF2680H   LINE      CODE     ---       #188
      00FF26A0H   LINE      CODE     ---       #189
      00FF26C0H   LINE      CODE     ---       #190
      00FF26E0H   LINE      CODE     ---       #191
      00FF2700H   LINE      CODE     ---       #192
      00FF2719H   LINE      CODE     ---       #193
      00FF2719H   LINE      CODE     ---       #194
      00FF2719H   LINE      CODE     ---       #195
      00FF2719H   LINE      CODE     ---       #197
      00FF271BH   LINE      CODE     ---       #199
      00FF272DH   LINE      CODE     ---       #200
      00FF2736H   LINE      CODE     ---       #201
      ---         BLOCKEND  ---      ---       LVL=0

      00FF1793H   BLOCK     CODE     ---       LVL=0
      R15         REGSYM    ---      BYTE      pin
      00000250H   SYMBOL    EDATA    ---       ps
      00000254H   SYMBOL    EDATA    FLOAT     num
      00000258H   SYMBOL    EDATA    ---       pe
      00FF179FH   BLOCK     CODE     NEAR LAB  LVL=1
      R11         REGSYM    ---      BYTE      k
      0000025CH   SYMBOL    EDATA    DWORD     l
      ---         BLOCKEND  ---      ---       LVL=1
      00FF1793H   LINE      CODE     ---       #202
      00FF179FH   LINE      CODE     ---       #203
      00FF179FH   LINE      CODE     ---       #207
      00FF17B0H   LINE      CODE     ---       #209
      00FF17B2H   LINE      CODE     ---       #211
      00FF17C4H   LINE      CODE     ---       #212
      00FF17CDH   LINE      CODE     ---       #213
      00FF17E0H   LINE      CODE     ---       #215
      00FF17F0H   LINE      CODE     ---       #217
      00FF180BH   LINE      CODE     ---       #218
      00FF183AH   LINE      CODE     ---       #220
      00FF183AH   LINE      CODE     ---       #221
      00FF183AH   LINE      CODE     ---       #222
      00FF183DH   LINE      CODE     ---       #223
      00FF183DH   LINE      CODE     ---       #224
      00FF1862H   LINE      CODE     ---       #225
      00FF1862H   LINE      CODE     ---       #226
      00FF1865H   LINE      CODE     ---       #227
      00FF1865H   LINE      CODE     ---       #228
      00FF188AH   LINE      CODE     ---       #229
      00FF18AFH   LINE      CODE     ---       #230
      00FF18AFH   LINE      CODE     ---       #231
      00FF18B2H   LINE      CODE     ---       #232
      00FF18B2H   LINE      CODE     ---       #233
      00FF18D7H   LINE      CODE     ---       #234
      00FF18FCH   LINE      CODE     ---       #235
      00FF1921H   LINE      CODE     ---       #236
      00FF1921H   LINE      CODE     ---       #237
      00FF1924H   LINE      CODE     ---       #238
      00FF1924H   LINE      CODE     ---       #239
      00FF194BH   LINE      CODE     ---       #240
      00FF1970H   LINE      CODE     ---       #241
      00FF1995H   LINE      CODE     ---       #242
      00FF19BAH   LINE      CODE     ---       #243
      00FF19BAH   LINE      CODE     ---       #244
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 94


      00FF19BDH   LINE      CODE     ---       #245
      00FF19BDH   LINE      CODE     ---       #246
      00FF19E4H   LINE      CODE     ---       #247
      00FF1A0BH   LINE      CODE     ---       #248
      00FF1A30H   LINE      CODE     ---       #249
      00FF1A55H   LINE      CODE     ---       #250
      00FF1A7AH   LINE      CODE     ---       #251
      00FF1A7AH   LINE      CODE     ---       #252
      00FF1A7DH   LINE      CODE     ---       #253
      00FF1A7DH   LINE      CODE     ---       #254
      00FF1AA4H   LINE      CODE     ---       #255
      00FF1ACBH   LINE      CODE     ---       #256
      00FF1AF2H   LINE      CODE     ---       #257
      00FF1B17H   LINE      CODE     ---       #258
      00FF1B3CH   LINE      CODE     ---       #259
      00FF1B61H   LINE      CODE     ---       #260
      00FF1B61H   LINE      CODE     ---       #261
      00FF1B64H   LINE      CODE     ---       #262
      00FF1B64H   LINE      CODE     ---       #263
      00FF1B8BH   LINE      CODE     ---       #264
      00FF1BB2H   LINE      CODE     ---       #265
      00FF1BD9H   LINE      CODE     ---       #266
      00FF1C00H   LINE      CODE     ---       #267
      00FF1C25H   LINE      CODE     ---       #268
      00FF1C4AH   LINE      CODE     ---       #269
      00FF1C6FH   LINE      CODE     ---       #270
      00FF1C6FH   LINE      CODE     ---       #271
      00FF1C72H   LINE      CODE     ---       #272
      00FF1C72H   LINE      CODE     ---       #273
      00FF1C99H   LINE      CODE     ---       #274
      00FF1CC0H   LINE      CODE     ---       #275
      00FF1CE7H   LINE      CODE     ---       #276
      00FF1D0EH   LINE      CODE     ---       #277
      00FF1D35H   LINE      CODE     ---       #278
      00FF1D5AH   LINE      CODE     ---       #279
      00FF1D7FH   LINE      CODE     ---       #280
      00FF1DA4H   LINE      CODE     ---       #281
      00FF1DA4H   LINE      CODE     ---       #282
      00FF1DA7H   LINE      CODE     ---       #283
      00FF1DA7H   LINE      CODE     ---       #284
      00FF1DCEH   LINE      CODE     ---       #285
      00FF1DF5H   LINE      CODE     ---       #286
      00FF1E1CH   LINE      CODE     ---       #287
      00FF1E43H   LINE      CODE     ---       #288
      00FF1E6AH   LINE      CODE     ---       #289
      00FF1E91H   LINE      CODE     ---       #290
      00FF1EB6H   LINE      CODE     ---       #291
      00FF1EDBH   LINE      CODE     ---       #292
      00FF1F00H   LINE      CODE     ---       #293
      00FF1F1CH   LINE      CODE     ---       #294
      00FF1F1CH   LINE      CODE     ---       #295
      00FF1F1CH   LINE      CODE     ---       #296
      00FF1F24H   LINE      CODE     ---       #298
      00FF1F49H   LINE      CODE     ---       #299
      00FF1F6EH   LINE      CODE     ---       #300
      00FF1F90H   LINE      CODE     ---       #302
      00FF1F92H   LINE      CODE     ---       #304
      00FF1FA4H   LINE      CODE     ---       #305
      00FF1FADH   LINE      CODE     ---       #306
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       EEPROM
      00FF7713H   PUBLIC    CODE     ---       EEPROM_Delete
      00FF7ED1H   PUBLIC    CODE     ---       EEPROM_OFF
      00FF74E4H   PUBLIC    CODE     ---       EEPROM_Read
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 95


      00FF7510H   PUBLIC    CODE     ---       EEPROM_Change
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000C6H   SFRSYM    DATA     BYTE      IAP_TRIG
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000C2H   SFRSYM    DATA     BYTE      IAP_DATA
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F5H   SFRSYM    DATA     BYTE      IAP_TPS
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000C4H   SFRSYM    DATA     BYTE      IAP_ADDRL
      000000C3H   SFRSYM    DATA     BYTE      IAP_ADDRH
      000000F6H   SFRSYM    DATA     BYTE      IAP_ADDRE
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000C5H   SFRSYM    DATA     BYTE      IAP_CMD
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF7ED1H   BLOCK     CODE     ---       LVL=0
      00FF7ED1H   LINE      CODE     ---       #11
      00FF7ED1H   LINE      CODE     ---       #13
      00FF7ED4H   LINE      CODE     ---       #14
      00FF7ED7H   LINE      CODE     ---       #15
      00FF7EDAH   LINE      CODE     ---       #16
      00FF7EDDH   LINE      CODE     ---       #17
      00FF7EE0H   LINE      CODE     ---       #18
      00FF7EE3H   LINE      CODE     ---       #19
      ---         BLOCKEND  ---      ---       LVL=0

L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 96


      00FF74E4H   BLOCK     CODE     ---       LVL=0
      DR4         REGSYM    ---      DWORD     addr
      00FF74E6H   BLOCK     CODE     NEAR LAB  LVL=1
      R15         REGSYM    ---      BYTE      dat
      ---         BLOCKEND  ---      ---       LVL=1
      00FF74E4H   LINE      CODE     ---       #21
      00FF74E6H   LINE      CODE     ---       #22
      00FF74E6H   LINE      CODE     ---       #25
      00FF74E9H   LINE      CODE     ---       #26
      00FF74ECH   LINE      CODE     ---       #27
      00FF74EFH   LINE      CODE     ---       #28
      00FF74F3H   LINE      CODE     ---       #29
      00FF74F7H   LINE      CODE     ---       #30
      00FF74FBH   LINE      CODE     ---       #31
      00FF74FEH   LINE      CODE     ---       #32
      00FF7501H   LINE      CODE     ---       #33
      00FF7502H   LINE      CODE     ---       #34
      00FF7503H   LINE      CODE     ---       #35
      00FF7504H   LINE      CODE     ---       #36
      00FF7505H   LINE      CODE     ---       #37
      00FF7508H   LINE      CODE     ---       #38
      00FF750BH   LINE      CODE     ---       #40
      00FF750DH   LINE      CODE     ---       #41
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7510H   BLOCK     CODE     ---       LVL=0
      DR4         REGSYM    ---      DWORD     addr
      R15         REGSYM    ---      BYTE      dat
      00FF7510H   LINE      CODE     ---       #43
      00FF7514H   LINE      CODE     ---       #45
      00FF7517H   LINE      CODE     ---       #46
      00FF751AH   LINE      CODE     ---       #47
      00FF751DH   LINE      CODE     ---       #48
      00FF7521H   LINE      CODE     ---       #49
      00FF7525H   LINE      CODE     ---       #50
      00FF7529H   LINE      CODE     ---       #51
      00FF752CH   LINE      CODE     ---       #52
      00FF752FH   LINE      CODE     ---       #53
      00FF7532H   LINE      CODE     ---       #54
      00FF7533H   LINE      CODE     ---       #55
      00FF7534H   LINE      CODE     ---       #56
      00FF7535H   LINE      CODE     ---       #57
      00FF7536H   LINE      CODE     ---       #58
      00FF7539H   LINE      CODE     ---       #59
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7713H   BLOCK     CODE     ---       LVL=0
      DR4         REGSYM    ---      DWORD     addr
      00FF7713H   LINE      CODE     ---       #61
      00FF7713H   LINE      CODE     ---       #63
      00FF7716H   LINE      CODE     ---       #64
      00FF7719H   LINE      CODE     ---       #65
      00FF771CH   LINE      CODE     ---       #66
      00FF7720H   LINE      CODE     ---       #67
      00FF7724H   LINE      CODE     ---       #68
      00FF7728H   LINE      CODE     ---       #69
      00FF772BH   LINE      CODE     ---       #70
      00FF772EH   LINE      CODE     ---       #71
      00FF772FH   LINE      CODE     ---       #72
      00FF7730H   LINE      CODE     ---       #73
      00FF7731H   LINE      CODE     ---       #74
      00FF7732H   LINE      CODE     ---       #75
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       CAN
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 97


      00FF5EDBH   PUBLIC    CODE     ---       CanReadFifo
      00FF7EE4H   PUBLIC    CODE     ---       CanReadReg
      00FF5810H   PUBLIC    CODE     ---       CanReadMsg
      00FF7E81H   PUBLIC    CODE     ---       CanWriteReg
      00FF4636H   PUBLIC    CODE     ---       CanSendMsg
      00FF3DEAH   PUBLIC    CODE     ---       CANInit
      000002D4H   PUBLIC    EDATA    BYTE      TSG1
      000002D5H   PUBLIC    EDATA    BYTE      TSG2
      000002D6H   PUBLIC    EDATA    BYTE      BRP
      000002D7H   PUBLIC    EDATA    WORD      CAN_time
      00000021H.7 PUBLIC    BIT      BIT       CAN_TX_OK
      00000022H.0 PUBLIC    BIT      BIT       ?CANInit?BIT
      00000022H.1 PUBLIC    BIT      BIT       ?CanReadMsg?BIT
      0000027DH   PUBLIC    EDATA    BYTE      ?CanSendMsg?BYTE
      00000022H.2 PUBLIC    BIT      BIT       ?CanSendMsg?BIT
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000F1H.1 SFRSYM    DATA     BIT       CANIE
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000097H.3 SFRSYM    DATA     BIT       CANSEL
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000F1H.5 SFRSYM    DATA     BIT       CAN2IE
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF7EE4H   BLOCK     CODE     ---       LVL=0
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 98


      R11         REGSYM    ---      BYTE      addr
      R11         REGSYM    ---      BYTE      dat
      00FF7EE4H   LINE      CODE     ---       #14
      00FF7EE4H   LINE      CODE     ---       #15
      00FF7EE4H   LINE      CODE     ---       #17
      00FF7EEFH   LINE      CODE     ---       #18
      00FF7EF6H   LINE      CODE     ---       #19
      00FF7EF6H   LINE      CODE     ---       #20
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7E81H   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      addr
      R2          REGSYM    ---      BYTE      dat
      00FF7E81H   LINE      CODE     ---       #22
      00FF7E83H   LINE      CODE     ---       #24
      00FF7E8EH   LINE      CODE     ---       #25
      00FF7E95H   LINE      CODE     ---       #26
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3DEAH   BLOCK     CODE     ---       LVL=0
      R5          REGSYM    ---      BYTE      CAN
      WR6         REGSYM    ---      INT       Bbaud
      R4          REGSYM    ---      BYTE      CAN_N
      00000022H.0 SYMBOL    BIT      BIT       en
      00FF3DEAH   LINE      CODE     ---       #83
      00FF3DEEH   LINE      CODE     ---       #85
      00FF3E28H   LINE      CODE     ---       #87
      00FF3E39H   LINE      CODE     ---       #88
      00FF3E4AH   LINE      CODE     ---       #89
      00FF3E5BH   LINE      CODE     ---       #90
      00FF3E6CH   LINE      CODE     ---       #91
      00FF3E7CH   LINE      CODE     ---       #92
      00FF3E8CH   LINE      CODE     ---       #93
      00FF3E9CH   LINE      CODE     ---       #94
      00FF3EAAH   LINE      CODE     ---       #95
      00FF3EBAH   LINE      CODE     ---       #96
      00FF3EC8H   LINE      CODE     ---       #97
      00FF3ED6H   LINE      CODE     ---       #98
      00FF3EE4H   LINE      CODE     ---       #99
      00FF3EF4H   LINE      CODE     ---       #100
      00FF3EF4H   LINE      CODE     ---       #101
      00FF3EF4H   LINE      CODE     ---       #103
      00FF3EFFH   LINE      CODE     ---       #105
      00FF3F03H   LINE      CODE     ---       #107
      00FF3F12H   LINE      CODE     ---       #109
      00FF3F1BH   LINE      CODE     ---       #110
      00FF3F23H   LINE      CODE     ---       #111
      00FF3F2BH   LINE      CODE     ---       #112
      00FF3F31H   LINE      CODE     ---       #113
      00FF3F31H   LINE      CODE     ---       #114
      00FF3F34H   LINE      CODE     ---       #115
      00FF3F39H   LINE      CODE     ---       #116
      00FF3F39H   LINE      CODE     ---       #117
      00FF3F3DH   LINE      CODE     ---       #119
      00FF3F4EH   LINE      CODE     ---       #121
      00FF3F57H   LINE      CODE     ---       #122
      00FF3F5FH   LINE      CODE     ---       #123
      00FF3F67H   LINE      CODE     ---       #124
      00FF3F6DH   LINE      CODE     ---       #125
      00FF3F6DH   LINE      CODE     ---       #126
      00FF3F70H   LINE      CODE     ---       #127
      00FF3F75H   LINE      CODE     ---       #128
      00FF3F75H   LINE      CODE     ---       #130
      00FF3F7CH   LINE      CODE     ---       #132
      00FF3F8BH   LINE      CODE     ---       #133
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 99


      00FF3FA6H   LINE      CODE     ---       #135
      00FF3FADH   LINE      CODE     ---       #136
      00FF3FB4H   LINE      CODE     ---       #137
      00FF3FBBH   LINE      CODE     ---       #138
      00FF3FC2H   LINE      CODE     ---       #139
      00FF3FCAH   LINE      CODE     ---       #140
      00FF3FD2H   LINE      CODE     ---       #141
      00FF3FDAH   LINE      CODE     ---       #142
      00FF3FE2H   LINE      CODE     ---       #144
      00FF3FEAH   LINE      CODE     ---       #145
      00FF3FF2H   LINE      CODE     ---       #146
      ---         BLOCKEND  ---      ---       LVL=0

      00FF5EDBH   BLOCK     CODE     ---       LVL=0
      REG=3       REGSYM    ---      ---       pdat
      00FF5EDBH   LINE      CODE     ---       #149
      00FF5EDFH   LINE      CODE     ---       #151
      00FF5EE7H   LINE      CODE     ---       #152
      00FF5EF0H   LINE      CODE     ---       #153
      00FF5EF9H   LINE      CODE     ---       #154
      00FF5F02H   LINE      CODE     ---       #155
      00FF5F0BH   LINE      CODE     ---       #156
      00FF5F14H   LINE      CODE     ---       #157
      00FF5F1DH   LINE      CODE     ---       #158
      00FF5F26H   LINE      CODE     ---       #159
      00FF5F2FH   LINE      CODE     ---       #160
      00FF5F38H   LINE      CODE     ---       #161
      00FF5F41H   LINE      CODE     ---       #162
      00FF5F4AH   LINE      CODE     ---       #163
      00FF5F53H   LINE      CODE     ---       #164
      00FF5F5CH   LINE      CODE     ---       #165
      00FF5F65H   LINE      CODE     ---       #166
      00FF5F6EH   LINE      CODE     ---       #167
      ---         BLOCKEND  ---      ---       LVL=0

      00FF5810H   BLOCK     CODE     ---       LVL=0
      REG=3       REGSYM    ---      ---       pdat
      00000022H.1 SYMBOL    BIT      BIT       mode
      00FF5814H   BLOCK     CODE     NEAR LAB  LVL=1
      00000227H   SYMBOL    EDATA    BYTE      i
      00000228H   SYMBOL    EDATA    DWORD     CanID
      0000022CH   SYMBOL    EDATA    ---       buffer
      ---         BLOCKEND  ---      ---       LVL=1
      00FF5810H   LINE      CODE     ---       #171
      00FF5814H   LINE      CODE     ---       #172
      00FF5814H   LINE      CODE     ---       #177
      00FF581BH   LINE      CODE     ---       #178
      00FF581EH   LINE      CODE     ---       #180
      00FF585CH   LINE      CODE     ---       #181
      00FF5861H   LINE      CODE     ---       #183
      00FF5872H   LINE      CODE     ---       #184
      00FF587EH   LINE      CODE     ---       #185
      00FF5880H   LINE      CODE     ---       #188
      00FF58A0H   LINE      CODE     ---       #189
      00FF58A5H   LINE      CODE     ---       #191
      00FF58B6H   LINE      CODE     ---       #192
      00FF58C2H   LINE      CODE     ---       #193
      00FF58C2H   LINE      CODE     ---       #194
      00FF58C6H   LINE      CODE     ---       #195
      ---         BLOCKEND  ---      ---       LVL=0

      00FF4636H   BLOCK     CODE     ---       LVL=0
      R7          REGSYM    ---      BYTE      CAN
      00000282H   SYMBOL    EDATA    DWORD     canid
      REG=3       REGSYM    ---      ---       pdat
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 100


      00000286H   SYMBOL    EDATA    BYTE      len
      00000022H.2 SYMBOL    BIT      BIT       mode
      00FF463EH   BLOCK     CODE     NEAR LAB  LVL=1
      00000287H   SYMBOL    EDATA    DWORD     CanID
      ---         BLOCKEND  ---      ---       LVL=1
      00FF4636H   LINE      CODE     ---       #196
      00FF463EH   LINE      CODE     ---       #197
      00FF463EH   LINE      CODE     ---       #201
      00FF4649H   LINE      CODE     ---       #203
      00FF464FH   LINE      CODE     ---       #205
      00FF465DH   LINE      CODE     ---       #206
      00FF466CH   LINE      CODE     ---       #207
      00FF4677H   LINE      CODE     ---       #208
      00FF4682H   LINE      CODE     ---       #209
      00FF468DH   LINE      CODE     ---       #211
      00FF4696H   LINE      CODE     ---       #212
      00FF469EH   LINE      CODE     ---       #213
      00FF46A7H   LINE      CODE     ---       #214
      00FF46B0H   LINE      CODE     ---       #216
      00FF46B9H   LINE      CODE     ---       #217
      00FF46C2H   LINE      CODE     ---       #218
      00FF46CBH   LINE      CODE     ---       #219
      00FF46D4H   LINE      CODE     ---       #221
      00FF46DDH   LINE      CODE     ---       #222
      00FF46E4H   LINE      CODE     ---       #223
      00FF46E8H   LINE      CODE     ---       #224
      00FF46E8H   LINE      CODE     ---       #225
      00FF46EAH   LINE      CODE     ---       #228
      00FF46F9H   LINE      CODE     ---       #229
      00FF4702H   LINE      CODE     ---       #230
      00FF470DH   LINE      CODE     ---       #231
      00FF4716H   LINE      CODE     ---       #232
      00FF471EH   LINE      CODE     ---       #234
      00FF4727H   LINE      CODE     ---       #235
      00FF4730H   LINE      CODE     ---       #236
      00FF4739H   LINE      CODE     ---       #237
      00FF4742H   LINE      CODE     ---       #239
      00FF474BH   LINE      CODE     ---       #240
      00FF4754H   LINE      CODE     ---       #241
      00FF475DH   LINE      CODE     ---       #243
      00FF4764H   LINE      CODE     ---       #244
      00FF4764H   LINE      CODE     ---       #245
      00FF4766H   LINE      CODE     ---       #246
      00FF476EH   LINE      CODE     ---       #247
      00FF4776H   LINE      CODE     ---       #249
      00FF4778H   LINE      CODE     ---       #251
      00FF4782H   LINE      CODE     ---       #252
      00FF4789H   LINE      CODE     ---       #253
      00FF4794H   LINE      CODE     ---       #254
      00FF479BH   LINE      CODE     ---       #255
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       DMA
      00FF479EH   PUBLIC    CODE     ---       DMA_RXD_init
      00FF48E6H   PUBLIC    CODE     ---       DMA_TXD_init
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 101


      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF48E6H   BLOCK     CODE     ---       LVL=0
      R7          REGSYM    ---      BYTE      pin
      WR0         REGSYM    ---      WORD      length
      REG=7       REGSYM    ---      ---       DAT
      00FF48E6H   LINE      CODE     ---       #7
      00FF48ECH   LINE      CODE     ---       #9
      00FF4906H   LINE      CODE     ---       #11
      00FF4906H   LINE      CODE     ---       #13
      00FF4912H   LINE      CODE     ---       #14
      00FF4919H   LINE      CODE     ---       #15
      00FF4924H   LINE      CODE     ---       #16
      00FF492DH   LINE      CODE     ---       #17
      00FF4938H   LINE      CODE     ---       #18
      00FF4943H   LINE      CODE     ---       #20
      00FF4949H   LINE      CODE     ---       #21
      00FF494CH   LINE      CODE     ---       #22
      00FF494CH   LINE      CODE     ---       #24
      00FF4958H   LINE      CODE     ---       #25
      00FF495FH   LINE      CODE     ---       #26
      00FF496AH   LINE      CODE     ---       #27
      00FF4973H   LINE      CODE     ---       #28
      00FF497EH   LINE      CODE     ---       #29
      00FF4989H   LINE      CODE     ---       #30
      00FF498FH   LINE      CODE     ---       #31
      00FF4992H   LINE      CODE     ---       #32
      00FF4992H   LINE      CODE     ---       #34
      00FF499EH   LINE      CODE     ---       #35
      00FF49A5H   LINE      CODE     ---       #36
      00FF49B0H   LINE      CODE     ---       #37
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 102


      00FF49B9H   LINE      CODE     ---       #38
      00FF49C4H   LINE      CODE     ---       #39
      00FF49CFH   LINE      CODE     ---       #40
      00FF49D5H   LINE      CODE     ---       #41
      00FF49D7H   LINE      CODE     ---       #42
      00FF49D7H   LINE      CODE     ---       #44
      00FF49E3H   LINE      CODE     ---       #45
      00FF49EAH   LINE      CODE     ---       #46
      00FF49F9H   LINE      CODE     ---       #47
      00FF4A02H   LINE      CODE     ---       #48
      00FF4A0DH   LINE      CODE     ---       #49
      00FF4A18H   LINE      CODE     ---       #50
      00FF4A25H   LINE      CODE     ---       #51
      00FF4A25H   LINE      CODE     ---       #52
      00FF4A25H   LINE      CODE     ---       #53
      ---         BLOCKEND  ---      ---       LVL=0

      00FF479EH   BLOCK     CODE     ---       LVL=0
      R7          REGSYM    ---      BYTE      pin
      WR0         REGSYM    ---      WORD      length
      REG=7       REGSYM    ---      ---       DAT
      00FF479EH   LINE      CODE     ---       #55
      00FF47A4H   LINE      CODE     ---       #57
      00FF47BEH   LINE      CODE     ---       #59
      00FF47BEH   LINE      CODE     ---       #61
      00FF47CBH   LINE      CODE     ---       #62
      00FF47D3H   LINE      CODE     ---       #63
      00FF47DEH   LINE      CODE     ---       #64
      00FF47E7H   LINE      CODE     ---       #65
      00FF47F2H   LINE      CODE     ---       #66
      00FF47FDH   LINE      CODE     ---       #67
      00FF4803H   LINE      CODE     ---       #68
      00FF4806H   LINE      CODE     ---       #69
      00FF4806H   LINE      CODE     ---       #71
      00FF4813H   LINE      CODE     ---       #72
      00FF481BH   LINE      CODE     ---       #73
      00FF4826H   LINE      CODE     ---       #74
      00FF482FH   LINE      CODE     ---       #75
      00FF483AH   LINE      CODE     ---       #76
      00FF4845H   LINE      CODE     ---       #77
      00FF484BH   LINE      CODE     ---       #78
      00FF484EH   LINE      CODE     ---       #79
      00FF484EH   LINE      CODE     ---       #81
      00FF485BH   LINE      CODE     ---       #82
      00FF4863H   LINE      CODE     ---       #83
      00FF486EH   LINE      CODE     ---       #84
      00FF4877H   LINE      CODE     ---       #85
      00FF4882H   LINE      CODE     ---       #86
      00FF488DH   LINE      CODE     ---       #87
      00FF4893H   LINE      CODE     ---       #88
      00FF4895H   LINE      CODE     ---       #89
      00FF4895H   LINE      CODE     ---       #91
      00FF48A2H   LINE      CODE     ---       #92
      00FF48AAH   LINE      CODE     ---       #93
      00FF48B9H   LINE      CODE     ---       #94
      00FF48C2H   LINE      CODE     ---       #95
      00FF48CDH   LINE      CODE     ---       #96
      00FF48D8H   LINE      CODE     ---       #97
      00FF48E5H   LINE      CODE     ---       #98
      00FF48E5H   LINE      CODE     ---       #99
      00FF48E5H   LINE      CODE     ---       #100
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       SPI
      00FF7FA3H   PUBLIC    CODE     ---       ESD_SPI_ReadByte
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 103


      00FF753CH   PUBLIC    CODE     ---       ESD_SPI_Init
      00FF76CEH   PUBLIC    CODE     ---       ESD_SPI_ReadMultiBytes
      00FF7FCDH   PUBLIC    CODE     ---       ESD_SPI_WriteByte
      00FF77D9H   PUBLIC    CODE     ---       ESD_SPI_WriteMultiBytes
      00FF7FB1H   PUBLIC    CODE     ---       ESD_SPI_RW
      00FF0016H   PUBLIC    CODE     ---       ESD_SPI_Deinit
      000000CFH   SFRSYM    DATA     BYTE      SPDAT
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF753CH   BLOCK     CODE     ---       LVL=0
      WR6         REGSYM    ---      INT       spi_port
      00FF753CH   LINE      CODE     ---       #6
      00FF753CH   LINE      CODE     ---       #9
      00FF753FH   LINE      CODE     ---       #14
      00FF7551H   LINE      CODE     ---       #16
      00FF7551H   LINE      CODE     ---       #17
      00FF7555H   LINE      CODE     ---       #18
      00FF7557H   LINE      CODE     ---       #19
      00FF7557H   LINE      CODE     ---       #20
      00FF755AH   LINE      CODE     ---       #21
      00FF755CH   LINE      CODE     ---       #22
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 104


      00FF755CH   LINE      CODE     ---       #23
      00FF755FH   LINE      CODE     ---       #24
      00FF7561H   LINE      CODE     ---       #25
      00FF7561H   LINE      CODE     ---       #26
      00FF7564H   LINE      CODE     ---       #27
      00FF7564H   LINE      CODE     ---       #28
      00FF7564H   LINE      CODE     ---       #29
      00FF7564H   LINE      CODE     ---       #30
      00FF7564H   LINE      CODE     ---       #31
      00FF7567H   LINE      CODE     ---       #32
      ---         BLOCKEND  ---      ---       LVL=0

      00FF0016H   BLOCK     CODE     ---       LVL=0
      00FF0016H   LINE      CODE     ---       #37
      00FF0016H   LINE      CODE     ---       #39
      00FF0019H   LINE      CODE     ---       #40
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7FCDH   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      out
      00FF7FCDH   LINE      CODE     ---       #46
      00FF7FCDH   LINE      CODE     ---       #48
      00FF7FD0H   LINE      CODE     ---       #49
      00FF7FD5H   LINE      CODE     ---       #50
      00FF7FD8H   LINE      CODE     ---       #51
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7FB1H   BLOCK     CODE     ---       LVL=0
      R11         REGSYM    ---      BYTE      dat
      00FF7FB1H   LINE      CODE     ---       #58
      00FF7FB1H   LINE      CODE     ---       #60
      00FF7FB4H   LINE      CODE     ---       #61
      00FF7FB9H   LINE      CODE     ---       #62
      00FF7FBCH   LINE      CODE     ---       #63
      00FF7FBEH   LINE      CODE     ---       #64
      ---         BLOCKEND  ---      ---       LVL=0

      00FF7FA3H   BLOCK     CODE     ---       LVL=0
      00FF7FA3H   LINE      CODE     ---       #70
      00FF7FA3H   LINE      CODE     ---       #72
      00FF7FA6H   LINE      CODE     ---       #73
      00FF7FABH   LINE      CODE     ---       #74
      00FF7FAEH   LINE      CODE     ---       #75
      00FF7FB0H   LINE      CODE     ---       #76
      ---         BLOCKEND  ---      ---       LVL=0

      00FF77D9H   BLOCK     CODE     ---       LVL=0
      REG=7       REGSYM    ---      ---       tx_ptr
      WR8         REGSYM    ---      WORD      n
      00FF77DDH   BLOCK     CODE     NEAR LAB  LVL=1
      WR6         REGSYM    ---      WORD      i
      ---         BLOCKEND  ---      ---       LVL=1
      00FF77D9H   LINE      CODE     ---       #83
      00FF77DDH   LINE      CODE     ---       #84
      00FF77DDH   LINE      CODE     ---       #86
      00FF77E1H   LINE      CODE     ---       #88
      00FF77EAH   LINE      CODE     ---       #89
      00FF77EFH   LINE      CODE     ---       #90
      00FF77F2H   LINE      CODE     ---       #91
      00FF77F8H   LINE      CODE     ---       #92
      ---         BLOCKEND  ---      ---       LVL=0

      00FF76CEH   BLOCK     CODE     ---       LVL=0
      REG=7       REGSYM    ---      ---       rx_ptr
      WR8         REGSYM    ---      WORD      n
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 105


      00FF76D2H   BLOCK     CODE     NEAR LAB  LVL=1
      WR6         REGSYM    ---      WORD      i
      ---         BLOCKEND  ---      ---       LVL=1
      00FF76CEH   LINE      CODE     ---       #99
      00FF76D2H   LINE      CODE     ---       #100
      00FF76D2H   LINE      CODE     ---       #102
      00FF76D6H   LINE      CODE     ---       #104
      00FF76D9H   LINE      CODE     ---       #105
      00FF76DEH   LINE      CODE     ---       #106
      00FF76E1H   LINE      CODE     ---       #107
      00FF76EAH   LINE      CODE     ---       #108
      00FF76F0H   LINE      CODE     ---       #109
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ?C?FPADD
      00FF01D4H   PUBLIC    CODE     ---       ?C?FPADD
      00FF01D1H   PUBLIC    CODE     ---       ?C?FPSUB

      ---         MODULE    ---      ---       ?C?FPMUL
      00FF028CH   PUBLIC    CODE     ---       ?C?FPMUL

      ---         MODULE    ---      ---       ?C?FPDIV
      00FF032CH   PUBLIC    CODE     ---       ?C?FPDIV

      ---         MODULE    ---      ---       ?C?FPCMP
      00FF03AFH   PUBLIC    CODE     ---       ?C?FPCMP
      00FF03ADH   PUBLIC    CODE     ---       ?C?FPCMP3

      ---         MODULE    ---      ---       ?C?FPNEG
      00FF03F3H   PUBLIC    CODE     ---       ?C?FPNEG

      ---         MODULE    ---      ---       ?C?FCAST
      00FF040BH   PUBLIC    CODE     NEAR LAB  ?C?FCASTC
      00FF0406H   PUBLIC    CODE     NEAR LAB  ?C?FCASTI
      00FF0401H   PUBLIC    CODE     NEAR LAB  ?C?FCASTL

      ---         MODULE    ---      ---       ?C?CASTF
      00FF043EH   PUBLIC    CODE     NEAR LAB  ?C?CASTF

      ---         MODULE    ---      ---       SPRINTF
      000001A7H   PUBLIC    EDATA    ---       ?SPRINTF?BYTE
      00FF047BH   PUBLIC    CODE     NEAR LAB  SPRINTF

      ---         MODULE    ---      ---       VSPRINTF
      000002F1H   PUBLIC    EDATA    ---       ?VSPRINTF?BYTE
      00FF049BH   PUBLIC    CODE     NEAR LAB  VSPRINTF

      ---         MODULE    ---      ---       FABS
      00FF04B7H   PUBLIC    CODE     NEAR LAB  FABS?_

      ---         MODULE    ---      ---       LOG10?_
      00FF04C6H   PUBLIC    CODE     NEAR LAB  LOG10?_

      ---         MODULE    ---      ---       SQRT?_
      00FF04D4H   PUBLIC    CODE     ---       SQRT?_

      ---         MODULE    ---      ---       ASIN?_
      00FF0564H   PUBLIC    CODE     ---       ASIN?_

      ---         MODULE    ---      ---       floor
      00FF668FH   PUBLIC    CODE     ---       floor?_

      ---         MODULE    ---      ---       ?C?FPGETOPN
      00FF0584H   PUBLIC    CODE     ---       ?C?FPGETOPN2
      00FF05C1H   PUBLIC    CODE     ---       ?C?FPNANRESULT
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 106


      00FF05C9H   PUBLIC    CODE     ---       ?C?FPOVERFLOW
      00FF059DH   PUBLIC    CODE     ---       ?C?FPRESULT
      00FF05B3H   PUBLIC    CODE     ---       ?C?FPRESULT2
      00FF05C6H   PUBLIC    CODE     ---       ?C?FPUNDERFLOW

      ---         MODULE    ---      ---       ?C?PRNFMT
      00FF0602H   PUBLIC    CODE     ---       ?C?PRNFMT

      ---         MODULE    ---      ---       LOG?_
      00FF0A3DH   PUBLIC    CODE     NEAR LAB  LOG?_

      ---         MODULE    ---      ---       ATAN?_
      00FF0B22H   PUBLIC    CODE     ---       ATAN?_

      ---         MODULE    ---      ---       ?C?FPCONVERT
      00FF0C02H   PUBLIC    CODE     ---       ?C?FPCONVERT
      00FF0CBAH   PUBLIC    CODE     ---       ?C?FPROUND

      ---         MODULE    ---      ---       ?C?FPSERIES
      00FF0CFDH   PUBLIC    CODE     ---       ?C?FP2SERIES
      00FF0D06H   PUBLIC    CODE     ---       ?C?FPSERIES

      ---         MODULE    ---      ---       ?C?FTNPWR
      00FF0D5AH   PUBLIC    CODE     ---       ?C?FTNPWR

      ---         MODULE    ---      ---       ?C_START
      00FF0000H   PUBLIC    CODE     ---       ?C?STARTUP
      00FF0000H   PUBLIC    CODE     ---       ?C_STARTUP

      ---         MODULE    ---      ---       ?C?INITEDATA
      00FF6B00H   PUBLIC    CODE     ---       ?C?INITEDATA

      ---         MODULE    ---      ---       ?C?SIDIV
      00FF0D91H   PUBLIC    CODE     ---       ?C?SIDIV

      ---         MODULE    ---      ---       ?C?LMUL
      00FF0DC3H   PUBLIC    CODE     ---       ?C?LMUL

      ---         MODULE    ---      ---       ?C?ULDIV
      00FF0DD6H   PUBLIC    CODE     NEAR LAB  ?C?ULDIV
      00FF0DD4H   PUBLIC    CODE     NEAR LAB  ?C?ULIDIV

      ---         MODULE    ---      ---       ?C?SLDIV
      00FF0E27H   PUBLIC    CODE     NEAR LAB  ?C?SLDIV

      ---         MODULE    ---      ---       ABS
      00FF7F49H   PUBLIC    CODE     ---       abs?_

      ---         MODULE    ---      ---       LABS
      00FF7F59H   PUBLIC    CODE     ---       labs?_

      ---         MODULE    ---      ---       STRLEN
      00FF7FBFH   PUBLIC    CODE     ---       strlen?_

      ---         MODULE    ---      ---       memcpy
      00FF76A9H   PUBLIC    CODE     ---       memcpy?_

      ---         MODULE    ---      ---       ?C?INITEDATA_END
      00FF8200H   PUBLIC    HCONST   WORD      ?C?INITEDATA_END



*** ERROR L127: UNRESOLVED EXTERNAL SYMBOL
    SYMBOL:  MOTOR_control
    MODULE:  .\Objects\main.obj (main)
L251 LINKER/LOCATER V4.66.93.0                                                        07/26/2025  14:58:12  PAGE 107



*** ERROR L128: REFERENCE MADE TO UNRESOLVED EXTERNAL
    SYMBOL:  MOTOR_control
    MODULE:  .\Objects\main.obj (main)
    ADDRESS: FF4C54H

*** ERROR L127: UNRESOLVED EXTERNAL SYMBOL
    SYMBOL:  TM0_isr
    MODULE:  .\Objects\isr.obj (isr)

*** ERROR L127: UNRESOLVED EXTERNAL SYMBOL
    SYMBOL:  INT2_isr
    MODULE:  .\Objects\isr.obj (isr)

*** ERROR L128: REFERENCE MADE TO UNRESOLVED EXTERNAL
    SYMBOL:  TM0_isr
    MODULE:  .\Objects\isr.obj (isr)
    ADDRESS: FF7220H

*** ERROR L128: REFERENCE MADE TO UNRESOLVED EXTERNAL
    SYMBOL:  INT2_isr
    MODULE:  .\Objects\isr.obj (isr)
    ADDRESS: FF73B9H

Program Size: data=10.4 edata+hdata=1016 xdata=192 const=664 code=32612
L251 RUN COMPLETE.  0 WARNING(S),  6 ERROR(S)
