/*****************************************************************************
* 例程名称		 : 机械臂红外控制
* 说明         : 基于红外管传感器的机械臂自动夹取控制系统
* 接线方式     : 红外管通过UART2接收数据，电机通过PWM控制
* 注意    		 : 支持自动和手动两种模式
*******************************************************************************/

//--包含你要使用的头文件--//
#include "config.h"          //通用配置头文件

/*************  本地常量声明    **************/
#define SPD_KP 12.0  //PID比例系数
#define SPD_KI 33.0  //PID积分系数
#define SPD_KD 0.0   //PID微分系数

/*************  IO口定义    **************/

/*************  本地变量声明    **************/
bit OLED_flag=0;
bit S_flag=0;
bit EC11_DIR;
bit PRINT_flag=0;
bit EC11_R_flag=0;

unsigned char xdata IRLED_dat[3];
signed char IR_ERR;  //红外偏差
unsigned int IR_DAT;  //红外管状态
unsigned char IR_N;   //有几个红外管压线

float PWR_U = 0.0;  //实际电池电压
int NUM=0;
unsigned char KEY_dat[2]={0xff,0xff};   //0=当前，1=上一时刻

//机械臂控制变量
bit gripper_auto_mode = 1;  //自动模式：1=自动，0=手动
bit object_detected = 0;    //物体检测标志
int detection_counter = 0;  //检测稳定计数器

//机械臂状态定义
typedef enum {
    GRIPPER_IDLE = 0,     //待机状态
    GRIPPER_DETECTING,    //检测状态
    GRIPPER_CLOSING,      //夹取中
    GRIPPER_HOLDING,      //保持状态
    GRIPPER_OPENING       //松开中
} gripper_state_t;

gripper_state_t gripper_state = GRIPPER_IDLE;
long motor_pos = 0, motor_pos_old = 0;  //电机位置
int motor_spd = 0;                      //电机速度
int set_spd = 0;                        //目标速度
long pwm_out = 0;                       //PWM输出
int err_spd[3] = {0};                   //速度误差数组
int gripper_timeout = 0;                //夹爪超时计数器
long target_pos = 0;                    //目标位置



/*************  本地函数声明    **************/
void EC11_app(void);  //编码器处理函数
void DIS_OLED(void);  //OLED刷新函数
void IR_Object_Detection(void);  //红外物体检测函数
void Gripper_Control(void);  //机械臂控制函数
void Set_Motor_Speed(int speed);  //设置电机速度

/****************  外部函数声明和外部变量声明 *****************/
void MOTOR_control(int PWM);
/*******************************************************************************
* 函 数 名         : main
* 函数功能		     : 主函数
* 输    入         : 无
* 输    出         : 无
*******************************************************************************/
void main(void)
{
	System_init();  //系统初始化
	PIT_init_ms(0,1);		//用户任务定时器1ms

	OLED_Init();

	UART_init(2,57600,1,0);
	DMA_RXD_init(2,3,IRLED_dat);

	ADC_init(ADC_P17,4);

	INT_init(1,1);   //初始化EC11旋转信号引脚，下降沿触发

	PWM_init(PWMA_CH1P_P10,6000,0);  //初始化电机PWM
	P11=0;

	EUSB = 1;   //IE2相关的中断使能后，需要重新设置EUSB
	EA = 1;  						//允许所有中断
	
	while(1)
	{
		EC11_app();  //编码器处理

		IR_Object_Detection();  //红外物体检测

		Gripper_Control();  //机械臂控制逻辑

		if(S_flag)  //1s一次
		{
			PWR_U = (ADC_get(ADC_P17)*10.55)/1000.0; //获取电池电压 mV
			S_flag=0;
		}

		if(OLED_flag)
		{
			DIS_OLED();
			OLED_flag=0;
		}

		if(PRINT_flag)
		{
			PRINT_flag=0;
		}

		//PID控制执行
		if(T0_cnt%50==0)
		{
			motor_spd = motor_pos-motor_pos_old;  //获取速度
			motor_pos_old = motor_pos;  //更新位置记录

				err_spd[0] = set_spd - motor_spd;

			pwm_out += (err_spd[0]-err_spd[1])*SPD_KP + err_spd[0]*SPD_KI + (err_spd[0]-2*err_spd[1]+err_spd[2])*SPD_KD;

			err_spd[2]=err_spd[1];
			err_spd[1]=err_spd[0];

			if(pwm_out>10000)	pwm_out=10000;
			if(pwm_out<-10000)	pwm_out=-10000;

			MOTOR_control(pwm_out);

			USB_flag=1;
		}
	}
}

void EC11_app(void)
{
	if(KEY_dat[0]==0 && KEY_dat[1]!=0) //按键按下
	{
		gripper_auto_mode = !gripper_auto_mode;  //切换自动/手动模式
		if(!gripper_auto_mode) {
			gripper_state = GRIPPER_IDLE;  //手动模式时回到待机
			set_spd = 0;  //停止电机
		}
	}
	KEY_dat[1] = KEY_dat[0];

	if(EC11_R_flag)  //按钮旋转
	{
		if(EC11_DIR)  //正转
		{
			NUM++;
		}
		else //反转
		{
			NUM--;
		}
		EC11_R_flag=0;
	}
}

/*******************************************************************************
* 函 数 名         : IR_Object_Detection
* 函数功能		     : 基于红外管数据的物体检测函数
* 输    入         : 无
* 输    出         : 无
*******************************************************************************/
void IR_Object_Detection(void)
{
	unsigned int temp_dat;
	unsigned char i;

	//统计有多少个红外管检测到物体
	IR_N = 0;
	temp_dat = IR_DAT;

	for(i=0; i<16; i++)
	{
		if(temp_dat & (0x8000>>i))
		{
			IR_N++;
		}
	}

	//根据检测到的红外管数量判断是否有物体
	if(IR_N >= 3)   //至少3个红外管检测到物体
	{
		if(detection_counter < 20)
		{
			detection_counter++;
		}
		else
		{
			object_detected = 1;  //确认检测到物体
		}
	}
	else
	{
		detection_counter = 0;
		object_detected = 0;  //没有检测到物体
	}
}

/*******************************************************************************
* 函 数 名         : Set_Motor_Speed
* 函数功能		     : 设置电机速度
* 输    入         : speed - 电机速度 (正数正转夹取，负数反转松开)
* 输    出         : 无
*******************************************************************************/
void Set_Motor_Speed(int speed)
{
	set_spd = speed;
}

/*******************************************************************************
* 函 数 名         : Gripper_Control
* 函数功能		     : 机械臂控制函数
* 输    入         : 无
* 输    出         : 无
*******************************************************************************/
void Gripper_Control(void)
{
	if(!gripper_auto_mode) return;  //手动模式不执行自动控制

	switch(gripper_state) {
		case GRIPPER_IDLE:
			Set_Motor_Speed(0);  //停止电机
			if(object_detected) {
				gripper_state = GRIPPER_DETECTING;
				detection_counter = 0;
			}
			break;

		case GRIPPER_DETECTING:
			if(object_detected && detection_counter > 20) {  //确认检测到物体
				gripper_state = GRIPPER_CLOSING;
				target_pos = motor_pos + 3000;  //设置夹取目标位置
				gripper_timeout = 0;
			} else if(!object_detected) {
				gripper_state = GRIPPER_IDLE;  //物体移开，回到待机
			}
			break;

		case GRIPPER_CLOSING:
			Set_Motor_Speed(120);  //正转夹取
			gripper_timeout++;

			//检查是否到达目标位置或超时
			if(motor_pos >= target_pos || gripper_timeout > 2000) {
				gripper_state = GRIPPER_HOLDING;
				gripper_timeout = 0;
			}
			break;

		case GRIPPER_HOLDING:
			Set_Motor_Speed(20);  //保持夹取力度
			gripper_timeout++;

			//保持一段时间后松开
			if(gripper_timeout > 3000) {
				gripper_state = GRIPPER_OPENING;
				target_pos = motor_pos - 2000;  //设置松开目标位置
				gripper_timeout = 0;
			}
			break;

		case GRIPPER_OPENING:
			Set_Motor_Speed(-100);  //反转松开
			gripper_timeout++;

			//检查是否到达目标位置或超时
			if(motor_pos <= target_pos || gripper_timeout > 1500) {
				gripper_state = GRIPPER_IDLE;
				gripper_timeout = 0;
			}
			break;

		default:
			gripper_state = GRIPPER_IDLE;
			break;
	}
}

void DIS_OLED(void)
{
	unsigned char n;

	OLED_ShowString(0,0,"U=",16);
	OLED_ShowFloat(16, 0, PWR_U, 2, 2, 16);

	OLED_ShowString(0,2,"E=",16);
	OLED_ShowInt(16, 2, IR_ERR, 2, 16);

	OLED_ShowString(0,4,"N=",16);
	OLED_ShowInt(16, 4, IR_N, 2, 16);

	OLED_ShowString(64,4,"M=",16);
	OLED_ShowInt(80, 4, gripper_auto_mode, 1, 16);

	OLED_ShowString(0,6,"S=",16);
	OLED_ShowInt(16, 6, gripper_state, 1, 16);

	for(n=0; n<16; n++)
	{
		if(IR_DAT&(0x8000>>n))	OLED_ShowChar(n*8, 7 , '1', 8);
		else OLED_ShowChar(n*8, 7 , '0', 8);
	}
}

void INT1_isr(void)  //EC11发生旋转
{
	EC11_R_flag=1;
	EC11_DIR = !P34;  //记录旋转方向
}

void DMA_RXD2_isr(void)
{
	unsigned char n;
	for(n=0; n<3; n++)
	{
		if(IRLED_dat[n]&0x80)  //判断数据头
		{
			IR_ERR = (IRLED_dat[n]>>2)&0x0f; //获取偏差
			if(IRLED_dat[n]&0x40) IR_ERR*=-1;
			//解析红外数据
			IR_DAT = ((unsigned int)IRLED_dat[n]&0x02)<<14 | (IRLED_dat[n]&0x01) | (unsigned int)(IRLED_dat[(n+1)%3])<<8 | (unsigned int)(IRLED_dat[(n+2)%3])<<1;
			break;
		}
	}
}

void TM0_isr(void)  //1ms用户任务定时器中断
{
	if(++T0_cnt==60000)	T0_cnt=0; //形成循环变量

	if(T0_cnt%8==0)	//按键窗口采集，先进先出
	{
		KEY_dat[0]<<=1;
		KEY_dat[0] |= (unsigned char)P32;
	}

	if(T0_cnt%50==0)	OLED_flag=1;  //50ms一次

	if(T0_cnt%200==0)	PRINT_flag=1; //200ms发送一次数据

	if(T0_cnt%1000==0)  S_flag=1;   //1s标志

	//机械臂控制计数器更新
	if(gripper_auto_mode && gripper_state != GRIPPER_IDLE)
	{
		gripper_timeout++;
	}
}
