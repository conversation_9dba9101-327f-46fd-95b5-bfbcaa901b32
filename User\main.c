/*****************************************************************************
* 例程名称		 : 空工程
* 说明         : 新建项目可以直接复制空工程开始编程。
* 接线方式     : ---
* 注意    		 : ---
*******************************************************************************/

//--包含你要使用的头文件--//
#include "config.h"          //通用配置头文件

/*************  本地常量声明    **************/
#define SPD_KP 12.0
#define SPD_KI 33.0
#define SPD_KD 0.0

/*************  IO口定义    **************/
sbit KEY1 = P3^2;  //机械臂控制按键
sbit IR_SENSOR = P3^3;  //红外传感器输入引脚

/*************  本地变量声明    **************/
bit key1_flag = 0;  //按键标志位
bit key1_old = 1;   //按键上次状态
bit gripper_mode = 0;  //夹爪模式：0=松开，1=夹取
bit ir_detected = 0;  //红外检测标志位
bit ir_old = 1;  //红外传感器上次状态
bit auto_mode = 1;  //自动模式：1=自动，0=手动

//机械臂状态定义
typedef enum {
    GRIPPER_IDLE = 0,     //待机状态
    GRIPPER_CLOSING,      //夹取中
    GRIPPER_OPENING,      //松开中
    GRIPPER_HOLDING,      //保持状态
    GRIPPER_DETECTING     //检测状态
} gripper_state_t;

gripper_state_t gripper_state = GRIPPER_IDLE;
long target_pos = 0;      //目标位置
long collision_pos = 0;   //碰撞检测位置
int collision_counter = 0; //碰撞检测计数器
int ir_stable_counter = 0; //红外稳定计数器
int gripper_timeout = 0;   //夹爪超时计数器

long motor_pos=0,motor_pos_old=0;
int motor_spd=0;


int err_spd[3];  //速度值

int set_spd=120;

long pwm_out=0;



/*************  本地函数声明    **************/
void IR_Detection(void);  //红外检测函数
void Gripper_Control(void);  //机械臂控制函数
void Set_Motor_Speed(int speed);  //设置电机速度

/****************  外部函数声明和外部变量声明 *****************/
void MOTOR_control(int PWM);	
/*******************************************************************************
* 函 数 名         : main
* 函数功能		     : 主函数
* 输    入         : 无
* 输    出         : 无
*******************************************************************************/	
void main(void)
{
	System_init();  //系统初始化
	PIT_init_ms(0,1);		//用户任务定时器1ms
	INT_init(2,1);

	PWM_init(PWMA_CH1P_P10,6000,0);
	P11=0;

	//GPIO初始化
	GPIO_init_pin(33,2);  //P3^3红外传感器输入，高阻态
	GPIO_pull_pin(33,1);  //P3^3上拉

	EUSB = 1;   //IE2相关的中断使能后，需要重新设置EUSB
	EA = 1;  						//允许所有中断
	
	while(1)
	{
		//红外传感器检测
		IR_Detection();

		//按键检测与消抖
		if(KEY1 != key1_old) {
			key1_old = KEY1;
			if(KEY1 == 0) {  //按键按下
				key1_flag = 1;
			}
		}

		//按键处理 - 切换自动/手动模式
		if(key1_flag) {
			key1_flag = 0;
			auto_mode = !auto_mode;  //切换模式
			if(!auto_mode) {
				gripper_state = GRIPPER_IDLE;  //手动模式时回到待机
				set_spd = 0;  //停止电机
			}
		}

		//机械臂控制逻辑
		Gripper_Control();

		//PID控制执行
		if(T0_cnt%50==0)
		{
			motor_spd = motor_pos-motor_pos_old;  //获取速度
			motor_pos_old = motor_pos;  //更新位置记录

				err_spd[0] = set_spd - motor_spd;

			pwm_out += (err_spd[0]-err_spd[1])*SPD_KP + err_spd[0]*SPD_KI + (err_spd[0]-2*err_spd[1]+err_spd[2])*SPD_KD;

			err_spd[2]=err_spd[1];
			err_spd[1]=err_spd[0];

			if(pwm_out>10000)	pwm_out=10000;
			if(pwm_out<-10000)	pwm_out=-10000;

			MOTOR_control(pwm_out);

			USB_flag=1;
		}
	}
}

/*******************************************************************************
* 函 数 名         : IR_Detection
* 函数功能		     : 红外传感器检测函数
* 输    入         : 无
* 输    出         : 无
*******************************************************************************/
void IR_Detection(void)
{
	bit ir_current = IR_SENSOR;  //读取当前红外状态

	if(ir_current != ir_old) {
		ir_old = ir_current;
		ir_stable_counter = 0;  //重置稳定计数器
	} else {
		if(ir_stable_counter < 10) {  //消抖处理
			ir_stable_counter++;
		} else {
			ir_detected = !ir_current;  //红外检测到物体时为低电平
		}
	}
}

/*******************************************************************************
* 函 数 名         : Set_Motor_Speed
* 函数功能		     : 设置电机速度
* 输    入         : speed - 电机速度 (正数正转夹取，负数反转松开)
* 输    出         : 无
*******************************************************************************/
void Set_Motor_Speed(int speed)
{
	set_spd = speed;
}

/*******************************************************************************
* 函 数 名         : Gripper_Control
* 函数功能		     : 机械臂控制函数
* 输    入         : 无
* 输    出         : 无
*******************************************************************************/
void Gripper_Control(void)
{
	if(!auto_mode) return;  //手动模式不执行自动控制

	switch(gripper_state) {
		case GRIPPER_IDLE:
			Set_Motor_Speed(0);  //停止电机
			if(ir_detected) {
				gripper_state = GRIPPER_DETECTING;
				ir_stable_counter = 0;
			}
			break;

		case GRIPPER_DETECTING:
			if(ir_detected && ir_stable_counter > 20) {  //确认检测到物体
				gripper_state = GRIPPER_CLOSING;
				target_pos = motor_pos + 3000;  //设置夹取目标位置
				gripper_timeout = 0;
			} else if(!ir_detected) {
				gripper_state = GRIPPER_IDLE;  //物体移开，回到待机
			}
			break;

		case GRIPPER_CLOSING:
			Set_Motor_Speed(120);  //正转夹取
			gripper_timeout++;

			//检查是否到达目标位置或超时
			if(motor_pos >= target_pos || gripper_timeout > 2000) {
				gripper_state = GRIPPER_HOLDING;
				gripper_timeout = 0;
			}
			break;

		case GRIPPER_HOLDING:
			Set_Motor_Speed(20);  //保持夹取力度
			gripper_timeout++;

			//保持一段时间后松开
			if(gripper_timeout > 3000) {
				gripper_state = GRIPPER_OPENING;
				target_pos = motor_pos - 2000;  //设置松开目标位置
				gripper_timeout = 0;
			}
			break;

		case GRIPPER_OPENING:
			Set_Motor_Speed(-100);  //反转松开
			gripper_timeout++;

			//检查是否到达目标位置或超时
			if(motor_pos <= target_pos || gripper_timeout > 1500) {
				gripper_state = GRIPPER_IDLE;
				gripper_timeout = 0;
			}
			break;

		default:
			gripper_state = GRIPPER_IDLE;
			break;
	}
}
