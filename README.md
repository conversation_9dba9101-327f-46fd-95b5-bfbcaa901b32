# 机械臂红外控制系统

## 功能概述
基于PID控制和红外管传感器阵列的智能机械臂夹爪控制系统，支持多点红外检测和自动夹取功能。

## 主要功能

### 1. 红外管阵列检测
- **16路红外管**: 通过UART2接收红外管传感器数据
- **多点检测**: 支持16个红外管同时检测物体位置
- **智能判断**: 当≥3个红外管检测到物体时触发夹取动作
- **偏差计算**: 自动计算物体相对中心的偏差值

### 2. PID电机控制
- **正转夹取**: 电机正转控制机械臂夹紧
- **反转松开**: 电机反转控制机械臂松开
- **速度控制**: 通过PID算法精确控制电机速度

### 3. 显示与交互
- **OLED显示**: 实时显示电池电压、红外偏差、检测状态等信息
- **编码器控制**: 支持EC11编码器进行参数调节
- **模式切换**: 通过按键在自动/手动模式间切换

### 4. 工作模式
- **自动模式**: 红外管检测到物体自动执行夹取流程
- **手动模式**: 停止自动控制，等待手动指令

## 硬件连接

| 功能 | 引脚 | 说明 |
|------|------|------|
| 红外管数据 | UART2 | 接收16路红外管传感器数据 |
| 控制按键 | P3^2 | 切换自动/手动模式 |
| EC11编码器 | P3^4, P3^5 | 旋转编码器输入 |
| 电机PWM | P1^0 | PWM输出控制电机 |
| 电机方向 | P1^1 | 控制电机转向 |
| OLED显示 | I2C | 状态信息显示 |
| 电池检测 | P1^7 | ADC检测电池电压 |

## 工作流程

### 自动模式流程：
1. **待机状态**: 系统等待红外管阵列检测物体
2. **检测状态**: 当≥3个红外管检测到物体时，进行稳定性确认
3. **夹取状态**: 电机正转，机械臂夹紧物体
4. **保持状态**: 保持夹取力度一段时间
5. **松开状态**: 电机反转，机械臂松开物体
6. **返回待机**: 完成一个完整循环

### 手动模式：
- 按下P3^2按键切换到手动模式
- 机械臂停止自动控制，等待手动指令
- 可通过编码器调节参数

## 参数配置

### PID参数
```c
#define SPD_KP 12.0  // 比例系数
#define SPD_KI 33.0  // 积分系数  
#define SPD_KD 0.0   // 微分系数
```

### 控制参数
- 夹取速度: 120 (正转)
- 松开速度: -100 (反转)
- 保持力度: 20
- 检测阈值: ≥3个红外管
- 检测稳定时间: 20个周期
- 夹取超时: 2000个周期
- 保持时间: 3000个周期
- 松开超时: 1500个周期

### 红外管数据格式
- 数据长度: 3字节
- 数据头: 0x80标识
- 偏差值: 4位，带符号
- 状态位: 16位，每位对应一个红外管

## OLED显示信息

| 行 | 内容 | 说明 |
|---|------|------|
| 0 | U=X.XX | 电池电压(V) |
| 2 | E=XX | 红外偏差值 |
| 4 | N=XX M=X | 检测数量和模式 |
| 6 | S=X | 机械臂状态 |
| 7 | 0101010... | 16位红外管状态 |

## 使用说明

1. **系统启动**: 上电后系统自动进入自动模式
2. **物体检测**: 将物体放置在红外管阵列检测范围内
3. **自动夹取**: 当≥3个红外管检测到物体时自动执行夹取流程
4. **模式切换**: 按下P3^2按键可在自动/手动模式间切换
5. **状态监控**: 通过OLED屏幕实时查看系统状态
6. **参数调节**: 在手动模式下可通过编码器调节参数

## 编译说明

### C51编译器兼容性
代码已针对Keil C51编译器进行优化，修复了以下问题：

**语法问题修复：**
- 使用 `unsigned int` 和 `unsigned char` 替代 `uint` 和 `uchar` 类型定义
- 变量声明移至函数开头，符合C51语法要求
- 优化了for循环和条件语句的格式
- 修复了数据类型转换问题

**链接错误修复：**
- 添加了 `MOTOR_control(long PWM)` 函数实现
- 添加了 `INT2_isr(void)` 中断服务函数
- 修复了PWM控制函数的参数类型匹配
- 确保了函数声明与实现的一致性

**函数声明问题修复：**
- 在文件开头添加了 `MOTOR_control` 函数声明
- 避免了编译器假设函数返回类型的问题
- 确保函数调用前已知正确的函数签名
- 解决了"redefinition"和"too many parameters"错误

### 编译环境
- 编译器: Keil C251 V5.60.0.0
- 目标芯片: STC32G12K128系列
- 项目文件: User/mode.uvproj

## 注意事项

- 红外管传感器需要稳定的电源和通信连接
- 确保机械臂运动范围内无障碍物
- 定期检查电机和传动机构的润滑状态
- 调试时建议先在手动模式下测试各项功能
- 红外管数据通过UART2以57600波特率传输
- 编译前请确保所有驱动库文件完整
