# 机械臂控制系统

## 功能概述
基于PID控制的智能机械臂夹爪控制系统，支持红外传感器自动检测和手动控制两种模式。

## 主要功能

### 1. 红外传感器自动控制
- **红外检测**: 使用P3^3引脚连接红外传感器
- **自动夹取**: 检测到物体时自动执行夹取动作
- **智能松开**: 夹取保持一段时间后自动松开

### 2. PID电机控制
- **正转夹取**: 电机正转控制机械臂夹紧
- **反转松开**: 电机反转控制机械臂松开
- **速度控制**: 通过PID算法精确控制电机速度

### 3. 工作模式
- **自动模式**: 红外传感器检测到物体自动执行夹取流程
- **手动模式**: 通过按键手动切换模式

## 硬件连接

| 功能 | 引脚 | 说明 |
|------|------|------|
| 红外传感器 | P3^3 | 输入，检测前方物体 |
| 控制按键 | P3^2 | 切换自动/手动模式 |
| 电机PWM | P1^0 | PWM输出控制电机 |
| 电机方向 | P1^1 | 控制电机转向 |

## 工作流程

### 自动模式流程：
1. **待机状态**: 系统等待红外传感器检测
2. **检测状态**: 红外传感器检测到物体，进行稳定性确认
3. **夹取状态**: 电机正转，机械臂夹紧物体
4. **保持状态**: 保持夹取力度一段时间
5. **松开状态**: 电机反转，机械臂松开物体
6. **返回待机**: 完成一个完整循环

### 手动模式：
- 按下P3^2按键切换到手动模式
- 机械臂停止自动控制，等待手动指令

## 参数配置

### PID参数
```c
#define SPD_KP 12.0  // 比例系数
#define SPD_KI 33.0  // 积分系数  
#define SPD_KD 0.0   // 微分系数
```

### 控制参数
- 夹取速度: 120 (正转)
- 松开速度: -100 (反转)
- 保持力度: 20
- 检测稳定时间: 20个周期
- 夹取超时: 2000个周期
- 保持时间: 3000个周期
- 松开超时: 1500个周期

## 使用说明

1. **系统启动**: 上电后系统自动进入自动模式
2. **物体检测**: 将物体放置在红外传感器检测范围内
3. **自动夹取**: 系统检测到物体后自动执行夹取流程
4. **模式切换**: 按下P3^2按键可在自动/手动模式间切换
5. **状态监控**: 通过USB串口可查看系统状态信息

## 注意事项

- 红外传感器需要稳定的电源供应
- 确保机械臂运动范围内无障碍物
- 定期检查电机和传动机构的润滑状态
- 调试时建议先在手动模式下测试各项功能
